{"__meta": {"id": "Xc09c6ad202495023b82cf609b2b0f5d9", "datetime": "2025-07-03 12:46:04", "utime": **********.544069, "method": "GET", "uri": "/api/v1/translations", "ip": "127.0.0.1"}, "php": {"version": "8.4.6", "interface": "cgi-fcgi"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.282123, "end": **********.544092, "duration": 0.26196885108947754, "duration_str": "262ms", "measures": [{"label": "Booting", "start": **********.282123, "relative_start": 0, "end": **********.496242, "relative_end": **********.496242, "duration": 0.21411895751953125, "duration_str": "214ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.496253, "relative_start": 0.21412992477416992, "end": **********.544096, "relative_end": 4.0531158447265625e-06, "duration": 0.047842979431152344, "duration_str": "47.84ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 6069672, "peak_usage_str": "6MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET api/v1/translations", "middleware": "api, set-locale", "controller": "App\\Http\\Controllers\\Api\\V1\\TranslationsController@index", "namespace": null, "prefix": "api/v1/translations", "where": [], "as": "api.translations.index", "file": "<a href=\"phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FHttp%2FControllers%2FApi%2FV1%2FTranslationsController.php&line=16\" onclick=\"\">app/Http/Controllers/Api/V1/TranslationsController.php:16-54</a>"}, "queries": {"nb_statements": 2, "nb_visible_statements": 2, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.010409999999999999, "accumulated_duration_str": "10.41ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `sessions` where `id` = 'LJhIALVTfLKwA8y0FseQKV2l0RiUvM4AkZ87ydyN' limit 1", "type": "query", "params": [], "bindings": ["LJhIALVTfLKwA8y0FseQKV2l0RiUvM4AkZ87ydyN"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 147}], "start": **********.513136, "duration": 0.00248, "duration_str": "2.48ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:97", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=97", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "97"}, "connection": "ticketgol", "explain": null, "start_percent": 0, "width_percent": 23.823}, {"sql": "update `sessions` set `payload` = 'YTo0OntzOjY6Il90b2tlbiI7czo0MDoiajdlUDd4QXZ1eGhjMzY5U3F4eVhwT1pRYTdhYk1XN3NKNEVkWHJ1YyI7czozOiJ1cmwiO2E6MTp7czo4OiJpbnRlbmRlZCI7czozOToiaHR0cDovL3RpY2tldGdvbC50ZXN0L215LWFjY291bnQvb3JkZXJzIjt9czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6Mjc6Imh0dHA6Ly90aWNrZXRnb2wudGVzdC9sb2dpbiI7fXM6NjoiX2ZsYXNoIjthOjI6e3M6Mzoib2xkIjthOjA6e31zOjM6Im5ldyI7YTowOnt9fX0=', `last_activity` = **********, `user_id` = null, `ip_address` = '127.0.0.1', `user_agent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' where `id` = 'LJhIALVTfLKwA8y0FseQKV2l0RiUvM4AkZ87ydyN'", "type": "query", "params": [], "bindings": ["YTo0OntzOjY6Il90b2tlbiI7czo0MDoiajdlUDd4QXZ1eGhjMzY5U3F4eVhwT1pRYTdhYk1XN3NKNEVkWHJ1YyI7czozOiJ1cmwiO2E6MTp7czo4OiJpbnRlbmRlZCI7czozOToiaHR0cDovL3RpY2tldGdvbC50ZXN0L215LWFjY291bnQvb3JkZXJzIjt9czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6Mjc6Imh0dHA6Ly90aWNrZXRnb2wudGVzdC9sb2dpbiI7fXM6NjoiX2ZsYXNoIjthOjI6e3M6Mzoib2xkIjthOjA6e31zOjM6Im5ldyI7YTowOnt9fX0=", **********, null, "127.0.0.1", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "LJhIALVTfLKwA8y0FseQKV2l0RiUvM4AkZ87ydyN"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 173}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 140}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 175}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 245}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 130}], "start": **********.534334, "duration": 0.00793, "duration_str": "7.93ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:173", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 173}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=173", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "173"}, "connection": "ticketgol", "explain": null, "start_percent": 23.823, "width_percent": 76.177}]}, "models": {"data": [], "count": 0, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "j7eP7xAvuxhc369SqxyXpOZQa7abMW7sJ4EdXruc", "url": "array:1 [\n  \"intended\" => \"http://ticketgol.test/my-account/orders\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://ticketgol.test/login\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]"}, "request": {"telescope": "<a href=\"http://ticketgol.test/_debugbar/telescope/9f4d6045-5511-4096-9ccf-d20a717c5661\" target=\"_blank\">View in Telescope</a>", "path_info": "/api/v1/translations", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2103898497 data-indent-pad=\"  \"><span class=sf-dump-note>array:11</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"853 characters\">ajs_anonymous_id=%2205cf8d6b-8a14-40b9-9d4f-c4ebd1620a6e%22; selected_locale=en; __stripe_mid=fd60eaa0-e90c-4914-bb27-ce58ed87fc6066be49; XSRF-TOKEN=eyJpdiI6IjcvRjdnK0JocHB3OWs1dDU0UGcybnc9PSIsInZhbHVlIjoiaWxtcjNXYmpoVFo2cSs0Vmw3MzJ1YXRiT0JWMDJvSG9Wa3lXeFV6cmFJRmdkdExkRkVIb3Z5WFByazJaWW1JNm1zWEovVHB3bmFnYm9sVTdtemJRNXdTZEhTdFFML2t6dXRFNTByT2lnMWhxTEp6Z25NbW4yTUpNSDZGQnBBbkUiLCJtYWMiOiI4ZTZhZmMyMjU4NGU1MGQ3OGNkZTJjYzA0ZjljNGY4ZWYwMGVjZjE1NjE2NjQ5YTc1MDI0MmUxZDAwNDY0Nzg2IiwidGFnIjoiIn0%3D; ticketgol_session=eyJpdiI6IjJTRHRoTGhqcFBpamQ1T213bW5qdGc9PSIsInZhbHVlIjoidC9pLy94NWxkZk9wV1hiWVoxdVZPeGhYaytCVlpLNHlkeTlva3hISTlvTlI4bCs0cS80NUZucElGNzdydy80dzdXc01kbWZRN25GUURkMWt2SkZhL1M3Z2grVXhiM2JIdTFzRTg4OHk0NmxKczlDeHlUZy9rclluUkppZWlPUlgiLCJtYWMiOiI2YjVlNTJjOGIyYjNjMTcyZjgyNDliNGU1ZjVmMjc5ODcyZTUwODM4OTVhNjg5YjQxYzAxZWIzMjM5YTdmNjhiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">en-GB,en;q=0.9,es;q=0.8,en-US;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://ticketgol.test/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-locale</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IjcvRjdnK0JocHB3OWs1dDU0UGcybnc9PSIsInZhbHVlIjoiaWxtcjNXYmpoVFo2cSs0Vmw3MzJ1YXRiT0JWMDJvSG9Wa3lXeFV6cmFJRmdkdExkRkVIb3Z5WFByazJaWW1JNm1zWEovVHB3bmFnYm9sVTdtemJRNXdTZEhTdFFML2t6dXRFNTByT2lnMWhxTEp6Z25NbW4yTUpNSDZGQnBBbkUiLCJtYWMiOiI4ZTZhZmMyMjU4NGU1MGQ3OGNkZTJjYzA0ZjljNGY4ZWYwMGVjZjE1NjE2NjQ5YTc1MDI0MmUxZDAwNDY0Nzg2IiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">ticketgol.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2103898497\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1660164633 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>ajs_anonymous_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>selected_locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">j7eP7xAvuxhc369SqxyXpOZQa7abMW7sJ4EdXruc</span>\"\n  \"<span class=sf-dump-key>ticketgol_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LJhIALVTfLKwA8y0FseQKV2l0RiUvM4AkZ87ydyN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1660164633\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-306778149 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 03 Jul 2025 12:46:04 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IkQvZnU0MU1KbWh6VlBRb01DU054M2c9PSIsInZhbHVlIjoiaTgyZWJ5L2dmandnRVk5ZlVxZXFkL21FMkxZNXp0QTdYWUJOMU5seGVEdW1hTmFycjBwZFZ2SHlaUTQ4ck1DNzJCeUpMd0w1cFlQbEtyY1JuM3U2Mk5jMzFtVjFKc0pGNXRLK2lrVHpxVU1WWllqR1JCQlpTcTROKzNucWZTL0giLCJtYWMiOiIxZTg2NTU3NGRkOGM0NjE1ZTlkNzgyNjBmYmI0OGFiMGQxYTU2ZmZjYTIzMmVkOGU2OWZmZTQxOWMyNzVlNjliIiwidGFnIjoiIn0%3D; expires=Thu, 03 Jul 2025 14:46:04 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"445 characters\">ticketgol_session=eyJpdiI6IkxCYmQzNENLZjVuN3RQSVJjM01WK3c9PSIsInZhbHVlIjoiWFRweFppMkQ2b2gvOHVLa0FoeXFGZmhjeXpCdlZYM0lPb1B0OWhHQU44TmFnMkV6UGNNMEMxWjZaN1NqYjg2em9lN3haKzdTQW8yMnBVaVpVK0JNMVRucUVuVldobS9PNTFpb296N0tIaGVTL0YyNDdDS1NUWlR3Kzk1UFQ5RkgiLCJtYWMiOiJhMTYzZDRiZWVhODdlMDIwOTdmMTNlNjE3MGE1ODc1NWUyMTA1YmZiMmRmM2QzOGIxMzAwNmVlYzZmY2ZlZDdlIiwidGFnIjoiIn0%3D; expires=Thu, 03 Jul 2025 14:46:04 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IkQvZnU0MU1KbWh6VlBRb01DU054M2c9PSIsInZhbHVlIjoiaTgyZWJ5L2dmandnRVk5ZlVxZXFkL21FMkxZNXp0QTdYWUJOMU5seGVEdW1hTmFycjBwZFZ2SHlaUTQ4ck1DNzJCeUpMd0w1cFlQbEtyY1JuM3U2Mk5jMzFtVjFKc0pGNXRLK2lrVHpxVU1WWllqR1JCQlpTcTROKzNucWZTL0giLCJtYWMiOiIxZTg2NTU3NGRkOGM0NjE1ZTlkNzgyNjBmYmI0OGFiMGQxYTU2ZmZjYTIzMmVkOGU2OWZmZTQxOWMyNzVlNjliIiwidGFnIjoiIn0%3D; expires=Thu, 03-Jul-2025 14:46:04 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"417 characters\">ticketgol_session=eyJpdiI6IkxCYmQzNENLZjVuN3RQSVJjM01WK3c9PSIsInZhbHVlIjoiWFRweFppMkQ2b2gvOHVLa0FoeXFGZmhjeXpCdlZYM0lPb1B0OWhHQU44TmFnMkV6UGNNMEMxWjZaN1NqYjg2em9lN3haKzdTQW8yMnBVaVpVK0JNMVRucUVuVldobS9PNTFpb296N0tIaGVTL0YyNDdDS1NUWlR3Kzk1UFQ5RkgiLCJtYWMiOiJhMTYzZDRiZWVhODdlMDIwOTdmMTNlNjE3MGE1ODc1NWUyMTA1YmZiMmRmM2QzOGIxMzAwNmVlYzZmY2ZlZDdlIiwidGFnIjoiIn0%3D; expires=Thu, 03-Jul-2025 14:46:04 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-306778149\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">j7eP7xAvuxhc369SqxyXpOZQa7abMW7sJ4EdXruc</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://ticketgol.test/my-account/orders</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://ticketgol.test/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}