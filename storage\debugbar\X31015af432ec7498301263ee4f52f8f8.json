{"__meta": {"id": "X31015af432ec7498301263ee4f52f8f8", "datetime": "2025-07-03 15:35:22", "utime": **********.06495, "method": "POST", "uri": "/api/v1/orders", "ip": "127.0.0.1"}, "php": {"version": "8.4.6", "interface": "cgi-fcgi"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751556921.909412, "end": **********.064964, "duration": 0.15555214881896973, "duration_str": "156ms", "measures": [{"label": "Booting", "start": 1751556921.909412, "relative_start": 0, "end": **********.008647, "relative_end": **********.008647, "duration": 0.09923505783081055, "duration_str": "99.24ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.00866, "relative_start": 0.09924817085266113, "end": **********.064966, "relative_end": 1.9073486328125e-06, "duration": 0.056305885314941406, "duration_str": "56.31ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 6468232, "peak_usage_str": "6MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST api/v1/orders", "middleware": "api, set-locale, auth:sanctum", "controller": "App\\Http\\Controllers\\Api\\V1\\OrderController@index", "namespace": null, "prefix": "api/v1/orders", "where": [], "as": "api.orders.index", "file": "<a href=\"phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FHttp%2FControllers%2FApi%2FV1%2FOrderController.php&line=26\" onclick=\"\">app/Http/Controllers/Api/V1/OrderController.php:26-32</a>"}, "queries": {"nb_statements": 11, "nb_visible_statements": 11, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.01565, "accumulated_duration_str": "15.65ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `sessions` where `id` = 'I4Fq1FZZ7QKkcMTyrkJarLDIbDoHn8Yi66uDsaPk' limit 1", "type": "query", "params": [], "bindings": ["I4Fq1FZZ7QKkcMTyrkJarLDIbDoHn8Yi66uDsaPk"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 147}], "start": **********.013448, "duration": 0.0025099999999999996, "duration_str": "2.51ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:97", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=97", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "97"}, "connection": "ticketgol", "explain": null, "start_percent": 0, "width_percent": 16.038}, {"sql": "select * from `users` where `id` = 7 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "auth.session", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\sanctum\\src\\Http\\Middleware\\AuthenticateSession.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.018727, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ticketgol", "explain": null, "start_percent": 16.038, "width_percent": 5.687}, {"sql": "select count(*) as aggregate from `orders` where `buyer_id` = 7 and `orders`.`deleted_at` is null", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/OrderService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\OrderService.php", "line": 43}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/V1/OrderController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\OrderController.php", "line": 29}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 19, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}, {"index": 20, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingRoutingDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingRoutingDispatcher.php", "line": 35}], "start": **********.024671, "duration": 0.0020299999999999997, "duration_str": "2.03ms", "memory": 0, "memory_str": null, "filename": "OrderService.php:43", "source": {"index": 16, "namespace": null, "name": "app/Services/OrderService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\OrderService.php", "line": 43}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FServices%2FOrderService.php&line=43", "ajax": false, "filename": "OrderService.php", "line": "43"}, "connection": "ticketgol", "explain": null, "start_percent": 21.725, "width_percent": 12.971}, {"sql": "select * from `orders` where `buyer_id` = 7 and `orders`.`deleted_at` is null order by `created_at` desc limit 9 offset 0", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/OrderService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\OrderService.php", "line": 43}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/V1/OrderController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\OrderController.php", "line": 29}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 19, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}, {"index": 20, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingRoutingDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingRoutingDispatcher.php", "line": 35}], "start": **********.029756, "duration": 0.00135, "duration_str": "1.35ms", "memory": 0, "memory_str": null, "filename": "OrderService.php:43", "source": {"index": 16, "namespace": null, "name": "app/Services/OrderService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\OrderService.php", "line": 43}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FServices%2FOrderService.php&line=43", "ajax": false, "filename": "OrderService.php", "line": "43"}, "connection": "ticketgol", "explain": null, "start_percent": 34.696, "width_percent": 8.626}, {"sql": "select * from `tickets` where `tickets`.`id` in (2, 3, 6, 8, 20) and `tickets`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Services/OrderService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\OrderService.php", "line": 43}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Api/V1/OrderController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\OrderController.php", "line": 29}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 24, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}, {"index": 25, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingRoutingDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingRoutingDispatcher.php", "line": 35}], "start": **********.034213, "duration": 0.00103, "duration_str": "1.03ms", "memory": 0, "memory_str": null, "filename": "OrderService.php:43", "source": {"index": 21, "namespace": null, "name": "app/Services/OrderService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\OrderService.php", "line": 43}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FServices%2FOrderService.php&line=43", "ajax": false, "filename": "OrderService.php", "line": "43"}, "connection": "ticketgol", "explain": null, "start_percent": 43.323, "width_percent": 6.581}, {"sql": "select * from `events` where `events`.`id` in (1, 5, 6, 7) and `events`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 26, "namespace": null, "name": "app/Services/OrderService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\OrderService.php", "line": 43}, {"index": 27, "namespace": null, "name": "app/Http/Controllers/Api/V1/OrderController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\OrderController.php", "line": 29}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 29, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}, {"index": 30, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingRoutingDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingRoutingDispatcher.php", "line": 35}], "start": **********.038907, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "OrderService.php:43", "source": {"index": 26, "namespace": null, "name": "app/Services/OrderService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\OrderService.php", "line": 43}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FServices%2FOrderService.php&line=43", "ajax": false, "filename": "OrderService.php", "line": "43"}, "connection": "ticketgol", "explain": null, "start_percent": 49.904, "width_percent": 5.751}, {"sql": "select * from `stadiums` where `stadiums`.`id` in (1, 5, 6, 9) and `stadiums`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 31, "namespace": null, "name": "app/Services/OrderService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\OrderService.php", "line": 43}, {"index": 32, "namespace": null, "name": "app/Http/Controllers/Api/V1/OrderController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\OrderController.php", "line": 29}, {"index": 33, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 34, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}, {"index": 35, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingRoutingDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingRoutingDispatcher.php", "line": 35}], "start": **********.043424, "duration": 0.00106, "duration_str": "1.06ms", "memory": 0, "memory_str": null, "filename": "OrderService.php:43", "source": {"index": 31, "namespace": null, "name": "app/Services/OrderService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\OrderService.php", "line": 43}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FServices%2FOrderService.php&line=43", "ajax": false, "filename": "OrderService.php", "line": "43"}, "connection": "ticketgol", "explain": null, "start_percent": 55.655, "width_percent": 6.773}, {"sql": "select * from (select *, row_number() over (partition by `media`.`model_id`) as `laravel_row` from `media` where `media`.`model_id` in (1, 5, 6, 9) and `media`.`model_type` = 'App\\\\Models\\\\Stadium') as `laravel_table` where `laravel_row` <= 1 order by `laravel_row`", "type": "query", "params": [], "bindings": ["App\\Models\\Stadium"], "hints": null, "show_copy": true, "backtrace": [{"index": 36, "namespace": null, "name": "app/Services/OrderService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\OrderService.php", "line": 43}, {"index": 37, "namespace": null, "name": "app/Http/Controllers/Api/V1/OrderController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\OrderController.php", "line": 29}, {"index": 38, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 39, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}, {"index": 40, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingRoutingDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingRoutingDispatcher.php", "line": 35}], "start": **********.047405, "duration": 0.00347, "duration_str": "3.47ms", "memory": 0, "memory_str": null, "filename": "OrderService.php:43", "source": {"index": 36, "namespace": null, "name": "app/Services/OrderService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\OrderService.php", "line": 43}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FServices%2FOrderService.php&line=43", "ajax": false, "filename": "OrderService.php", "line": "43"}, "connection": "ticketgol", "explain": null, "start_percent": 62.428, "width_percent": 22.173}, {"sql": "select `sluggable_id`, `slug`, `locale` from `slugs` where `locale` = 'en' and `slugs`.`sluggable_id` in (1, 5, 6, 9) and `slugs`.`sluggable_type` = 'App\\\\Models\\\\Stadium'", "type": "query", "params": [], "bindings": ["en", "App\\Models\\Stadium"], "hints": null, "show_copy": true, "backtrace": [{"index": 36, "namespace": null, "name": "app/Services/OrderService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\OrderService.php", "line": 43}, {"index": 37, "namespace": null, "name": "app/Http/Controllers/Api/V1/OrderController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\OrderController.php", "line": 29}, {"index": 38, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 39, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}, {"index": 40, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingRoutingDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingRoutingDispatcher.php", "line": 35}], "start": **********.05317, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "OrderService.php:43", "source": {"index": 36, "namespace": null, "name": "app/Services/OrderService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\OrderService.php", "line": 43}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FServices%2FOrderService.php&line=43", "ajax": false, "filename": "OrderService.php", "line": "43"}, "connection": "ticketgol", "explain": null, "start_percent": 84.601, "width_percent": 5.431}, {"sql": "select `sluggable_id`, `slug`, `locale` from `slugs` where `locale` = 'en' and `slugs`.`sluggable_id` in (1, 5, 6, 7) and `slugs`.`sluggable_type` = 'App\\\\Models\\\\Event'", "type": "query", "params": [], "bindings": ["en", "App\\Models\\Event"], "hints": null, "show_copy": true, "backtrace": [{"index": 31, "namespace": null, "name": "app/Services/OrderService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\OrderService.php", "line": 43}, {"index": 32, "namespace": null, "name": "app/Http/Controllers/Api/V1/OrderController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\OrderController.php", "line": 29}, {"index": 33, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 34, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}, {"index": 35, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingRoutingDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingRoutingDispatcher.php", "line": 35}], "start": **********.056287, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "OrderService.php:43", "source": {"index": 31, "namespace": null, "name": "app/Services/OrderService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\OrderService.php", "line": 43}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FServices%2FOrderService.php&line=43", "ajax": false, "filename": "OrderService.php", "line": "43"}, "connection": "ticketgol", "explain": null, "start_percent": 90.032, "width_percent": 4.792}, {"sql": "update `sessions` set `payload` = 'YTo1OntzOjY6Il90b2tlbiI7czo0MDoieEhXSWxNZHMxb0htOHJsa2ZRRmVGVFdaS2ZyNFhTUkRRakxVc1ZzViI7czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6Mzk6Imh0dHA6Ly90aWNrZXRnb2wudGVzdC9teS1hY2NvdW50L29yZGVycyI7fXM6NTA6ImxvZ2luX3dlYl81OWJhMzZhZGRjMmIyZjk0MDE1ODBmMDE0YzdmNThlYTRlMzA5ODlkIjtpOjc7czoxNzoicGFzc3dvcmRfaGFzaF93ZWIiO3M6NjA6IiQyeSQxMiRWMXdSNjRHTVFvbFRNQXBFM3d6bHlld0w4UDlic0FDM2FmWk9nSWhqOWZybUN1cXpZLi41ZSI7fQ==', `last_activity` = **********, `user_id` = 7, `ip_address` = '127.0.0.1', `user_agent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' where `id` = 'I4Fq1FZZ7QKkcMTyrkJarLDIbDoHn8Yi66uDsaPk'", "type": "query", "params": [], "bindings": ["YTo1OntzOjY6Il90b2tlbiI7czo0MDoieEhXSWxNZHMxb0htOHJsa2ZRRmVGVFdaS2ZyNFhTUkRRakxVc1ZzViI7czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6Mzk6Imh0dHA6Ly90aWNrZXRnb2wudGVzdC9teS1hY2NvdW50L29yZGVycyI7fXM6NTA6ImxvZ2luX3dlYl81OWJhMzZhZGRjMmIyZjk0MDE1ODBmMDE0YzdmNThlYTRlMzA5ODlkIjtpOjc7czoxNzoicGFzc3dvcmRfaGFzaF93ZWIiO3M6NjA6IiQyeSQxMiRWMXdSNjRHTVFvbFRNQXBFM3d6bHlld0w4UDlic0FDM2FmWk9nSWhqOWZybUN1cXpZLi41ZSI7fQ==", **********, 7, "127.0.0.1", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "I4Fq1FZZ7QKkcMTyrkJarLDIbDoHn8Yi66uDsaPk"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 173}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 140}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 175}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 245}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 130}], "start": **********.063178, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:173", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 173}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=173", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "173"}, "connection": "ticketgol", "explain": null, "start_percent": 94.824, "width_percent": 5.176}]}, "models": {"data": {"App\\Models\\Slug": {"value": 8, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FSlug.php&line=1", "ajax": false, "filename": "Slug.php", "line": "?"}}, "App\\Models\\Order": {"value": 6, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FOrder.php&line=1", "ajax": false, "filename": "Order.php", "line": "?"}}, "App\\Models\\Ticket": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FTicket.php&line=1", "ajax": false, "filename": "Ticket.php", "line": "?"}}, "App\\Models\\Event": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FEvent.php&line=1", "ajax": false, "filename": "Event.php", "line": "?"}}, "App\\Models\\Stadium": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FStadium.php&line=1", "ajax": false, "filename": "Stadium.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\MediaLibrary\\MediaCollections\\Models\\Media": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Fspatie%2Flaravel-medialibrary%2Fsrc%2FMediaCollections%2FModels%2FMedia.php&line=1", "ajax": false, "filename": "Media.php", "line": "?"}}}, "count": 29, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "xHWIlMds1oHm8rlkfQFeFTWZKfr4XSRDQjLUsVsV", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://ticketgol.test/my-account/orders\"\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "7", "password_hash_web": "$2y$12$V1wR64GMQolTMApE3wzlyewL8P9bsAC3afZOgIhj9frmCuqzY..5e"}, "request": {"telescope": "<a href=\"http://ticketgol.test/_debugbar/telescope/9f4d9cd0-7aa0-4ee1-b08a-3e123fc7494b\" target=\"_blank\">View in Telescope</a>", "path_info": "/api/v1/orders", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>date_from</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>date_to</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2037175301 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"853 characters\">ajs_anonymous_id=%2205cf8d6b-8a14-40b9-9d4f-c4ebd1620a6e%22; selected_locale=en; __stripe_mid=fd60eaa0-e90c-4914-bb27-ce58ed87fc6066be49; XSRF-TOKEN=eyJpdiI6ImFSY3J1M0l3YlgwZGZjME4wdkp4b3c9PSIsInZhbHVlIjoiQ3RRNUQyQ0hCYzg4dEpySElGTE1ad2Q5aUc0MVk2Y2I0Y0pUUVJtazRBZ29sVjRjU0t0S3JkdjZ5YmZ2UlRkLzhsWFVxWVVHSGVYNGtYZllzblNuUzE0RFcyN2doM2lXaHpIUm1vR0duNmZxakh5VXRobmJOTWRhN1RTR1BEaWUiLCJtYWMiOiI2OWY1ZDM1OGU3YTE0ZjFmYmRkMDc1N2Q5MDk5OWViNTRiMWNmNDhlMjU1YmE4M2Y0NTc0YTMzNWUwNGQ4ZWVkIiwidGFnIjoiIn0%3D; ticketgol_session=eyJpdiI6Illuc3E5VEl1Zm1SdDlWaTJhTVNtWVE9PSIsInZhbHVlIjoiVVVKQ1gwL1FJa0E4Q21OOXQrZzlGQ2k1cnVEcnIwbFpPRUZGbHJzTXovQ1NKL2s3bUtuZklzWm84YVpJYnVVR3VTR3FFZHRjaHF0THhvNTVoRWdpTCtyVFdYcVowZGxhUEJEay9uNERwN210WlowWXdDTHlJVmV4Q0dRL3Nxa3YiLCJtYWMiOiIxMjFiMDhkNTM1NzJhMzJlYzUzZjhjZDA5NDU2MmU5NTQ3ZWZkOTJhNmRlMWU5ODc0ODNjMTcxNjdlNGFjYjZlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">en-GB,en;q=0.9,es;q=0.8,en-US;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://ticketgol.test/my-account/orders</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://ticketgol.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-locale</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6ImFSY3J1M0l3YlgwZGZjME4wdkp4b3c9PSIsInZhbHVlIjoiQ3RRNUQyQ0hCYzg4dEpySElGTE1ad2Q5aUc0MVk2Y2I0Y0pUUVJtazRBZ29sVjRjU0t0S3JkdjZ5YmZ2UlRkLzhsWFVxWVVHSGVYNGtYZllzblNuUzE0RFcyN2doM2lXaHpIUm1vR0duNmZxakh5VXRobmJOTWRhN1RTR1BEaWUiLCJtYWMiOiI2OWY1ZDM1OGU3YTE0ZjFmYmRkMDc1N2Q5MDk5OWViNTRiMWNmNDhlMjU1YmE4M2Y0NTc0YTMzNWUwNGQ4ZWVkIiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">53</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">ticketgol.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2037175301\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1507583827 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>ajs_anonymous_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>selected_locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xHWIlMds1oHm8rlkfQFeFTWZKfr4XSRDQjLUsVsV</span>\"\n  \"<span class=sf-dump-key>ticketgol_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">I4Fq1FZZ7QKkcMTyrkJarLDIbDoHn8Yi66uDsaPk</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1507583827\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1343485870 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 03 Jul 2025 15:35:22 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IllxUzc2ckd0Qkw3YmkrSVF2WFpHWnc9PSIsInZhbHVlIjoiQjBMVUEzcVdkdEpqTDd1Ui9HekRhOWZON0VKVkNpZkRXM2JLd1l5S2gwRm1WSjRpVjVzVFgyNjNJOURsK1kxcU53TTBvSjdseVNSbXhFcjRseENxNUh5VW1wSEJwR2xKSHdRUlFIMndJemtNQ1dmaHMxSTNBNDM4RnNBZHc2RmwiLCJtYWMiOiJkMjI3OTU2YThjNGRiMGVkNmIwMDUxYjViZDYwZjUzYjMwOGJlZjdjNGI1MTcwZWQ2ZThhYTE5OTIwNzg2ZDY1IiwidGFnIjoiIn0%3D; expires=Thu, 03 Jul 2025 17:35:22 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"445 characters\">ticketgol_session=eyJpdiI6InpKOU1McC91bm95NXBUb0pJRDROMUE9PSIsInZhbHVlIjoiRURQbXRGREtMaUxQcUd2SVFTdEFMUGw5UjVGUXROMEZoRythZTl0QjBTcFRpTjhXQ2J6U2RtRXFJZlNPdjBsUk5vSEFDTzFIVDg4NDZqZFhsZlVYU0dScmNtUjVJSTdIMVd3ZHhybS9NODNBakZhb0VqZDllczl3aUlaR2k1WUkiLCJtYWMiOiJkYmY0ZmE0NDJkYmM0ZTRhZWQ4MWVhY2FhMzAwMGE4M2U3MWFkNjZjM2QzYzc3OTdiZmMwOGU3MzY4NzJmOGI0IiwidGFnIjoiIn0%3D; expires=Thu, 03 Jul 2025 17:35:22 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IllxUzc2ckd0Qkw3YmkrSVF2WFpHWnc9PSIsInZhbHVlIjoiQjBMVUEzcVdkdEpqTDd1Ui9HekRhOWZON0VKVkNpZkRXM2JLd1l5S2gwRm1WSjRpVjVzVFgyNjNJOURsK1kxcU53TTBvSjdseVNSbXhFcjRseENxNUh5VW1wSEJwR2xKSHdRUlFIMndJemtNQ1dmaHMxSTNBNDM4RnNBZHc2RmwiLCJtYWMiOiJkMjI3OTU2YThjNGRiMGVkNmIwMDUxYjViZDYwZjUzYjMwOGJlZjdjNGI1MTcwZWQ2ZThhYTE5OTIwNzg2ZDY1IiwidGFnIjoiIn0%3D; expires=Thu, 03-Jul-2025 17:35:22 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"417 characters\">ticketgol_session=eyJpdiI6InpKOU1McC91bm95NXBUb0pJRDROMUE9PSIsInZhbHVlIjoiRURQbXRGREtMaUxQcUd2SVFTdEFMUGw5UjVGUXROMEZoRythZTl0QjBTcFRpTjhXQ2J6U2RtRXFJZlNPdjBsUk5vSEFDTzFIVDg4NDZqZFhsZlVYU0dScmNtUjVJSTdIMVd3ZHhybS9NODNBakZhb0VqZDllczl3aUlaR2k1WUkiLCJtYWMiOiJkYmY0ZmE0NDJkYmM0ZTRhZWQ4MWVhY2FhMzAwMGE4M2U3MWFkNjZjM2QzYzc3OTdiZmMwOGU3MzY4NzJmOGI0IiwidGFnIjoiIn0%3D; expires=Thu, 03-Jul-2025 17:35:22 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1343485870\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xHWIlMds1oHm8rlkfQFeFTWZKfr4XSRDQjLUsVsV</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://ticketgol.test/my-account/orders</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$V1wR64GMQolTMApE3wzlyewL8P9bsAC3afZOgIhj9frmCuqzY..5e</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}