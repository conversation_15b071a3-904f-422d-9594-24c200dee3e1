{"__meta": {"id": "Xa4c105ff7c29059f9f21fb759193fa4d", "datetime": "2025-07-03 08:35:35", "utime": **********.963896, "method": "GET", "uri": "/api/v1/events/event-jaunita-runolfsdottir-7", "ip": "127.0.0.1"}, "php": {"version": "8.4.6", "interface": "cgi-fcgi"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.787224, "end": **********.96391, "duration": 0.17668604850769043, "duration_str": "177ms", "measures": [{"label": "Booting", "start": **********.787224, "relative_start": 0, "end": **********.845832, "relative_end": **********.845832, "duration": 0.058608055114746094, "duration_str": "58.61ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.845847, "relative_start": 0.05862283706665039, "end": **********.963913, "relative_end": 2.86102294921875e-06, "duration": 0.11806607246398926, "duration_str": "118ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 7361424, "peak_usage_str": "7MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET api/v1/events/{slug}", "middleware": "api, set-locale", "controller": "App\\Http\\Controllers\\Api\\V1\\EventController@show", "namespace": null, "prefix": "api/v1/events", "where": [], "as": "api.events.show", "file": "<a href=\"phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FHttp%2FControllers%2FApi%2FV1%2FEventController.php&line=111\" onclick=\"\">app/Http/Controllers/Api/V1/EventController.php:111-124</a>"}, "queries": {"nb_statements": 26, "nb_visible_statements": 26, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.03939, "accumulated_duration_str": "39.39ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `sessions` where `id` = 'N75WHSWBQig1s7jRbuIicGDoQiErAj8tdVS6z3Wn' limit 1", "type": "query", "params": [], "bindings": ["N75WHSWBQig1s7jRbuIicGDoQiErAj8tdVS6z3Wn"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 147}], "start": **********.8497312, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:97", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=97", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "97"}, "connection": "ticketgol", "explain": null, "start_percent": 0, "width_percent": 2.031}, {"sql": "select * from `users` where `id` = 7 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "auth.session", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\sanctum\\src\\Http\\Middleware\\AuthenticateSession.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.853017, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ticketgol", "explain": null, "start_percent": 2.031, "width_percent": 1.676}, {"sql": "select `id`, `date`, `time`, `timezone`, `category`, `stadium_id`, `league_id`, `home_club_id`, `guest_club_id`, `is_feature_event`, (select MIN(price) from `tickets` where `event_id` = `events`.`id` and `tickets`.`deleted_at` is null) as `min_price`, (select MAX(price) from `tickets` where `event_id` = `events`.`id` and `tickets`.`deleted_at` is null) as `max_price` from `events` where exists (select * from `slugs` where `events`.`id` = `slugs`.`sluggable_id` and `slugs`.`sluggable_type` = 'App\\\\Models\\\\Event' and `slug` = 'event-jaunita-runolfsdottir-7' and `locale` = 'en' and `locale` = 'en') and `is_published` = 1 and `events`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["App\\Models\\Event", "event-jaunita-runolfsdottir-7", "en", "en", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, {"index": 17, "namespace": null, "name": "app/Services/EventService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\EventService.php", "line": 71}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Api/V1/EventController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\EventController.php", "line": 114}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 20, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.859437, "duration": 0.00551, "duration_str": "5.51ms", "memory": 0, "memory_str": null, "filename": "EventRepository.php:222", "source": {"index": 16, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FEventRepository.php&line=222", "ajax": false, "filename": "EventRepository.php", "line": "222"}, "connection": "ticketgol", "explain": null, "start_percent": 3.707, "width_percent": 13.988}, {"sql": "select `event_id`, `locale`, `name`, `description`, `meta_title`, `meta_description`, `meta_keywords` from `event_translations` where `locale` = 'en' and `event_translations`.`event_id` in (7)", "type": "query", "params": [], "bindings": ["en"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, {"index": 22, "namespace": null, "name": "app/Services/EventService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\EventService.php", "line": 71}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/Api/V1/EventController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\EventController.php", "line": 114}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 25, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.867659, "duration": 0.00195, "duration_str": "1.95ms", "memory": 0, "memory_str": null, "filename": "EventRepository.php:222", "source": {"index": 21, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FEventRepository.php&line=222", "ajax": false, "filename": "EventRepository.php", "line": "222"}, "connection": "ticketgol", "explain": null, "start_percent": 17.695, "width_percent": 4.95}, {"sql": "select `id`, `address_line_1`, `address_line_2`, `postcode`, `country_id` from `stadiums` where `stadiums`.`id` in (5) and `stadiums`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, {"index": 22, "namespace": null, "name": "app/Services/EventService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\EventService.php", "line": 71}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/Api/V1/EventController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\EventController.php", "line": 114}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 25, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.871666, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "EventRepository.php:222", "source": {"index": 21, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FEventRepository.php&line=222", "ajax": false, "filename": "EventRepository.php", "line": "222"}, "connection": "ticketgol", "explain": null, "start_percent": 22.645, "width_percent": 1.472}, {"sql": "select `stadium_id`, `locale`, `name` from `stadium_translations` where `locale` = 'en' and `stadium_translations`.`stadium_id` in (5)", "type": "query", "params": [], "bindings": ["en"], "hints": null, "show_copy": true, "backtrace": [{"index": 26, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, {"index": 27, "namespace": null, "name": "app/Services/EventService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\EventService.php", "line": 71}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/Api/V1/EventController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\EventController.php", "line": 114}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 30, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.874586, "duration": 0.00159, "duration_str": "1.59ms", "memory": 0, "memory_str": null, "filename": "EventRepository.php:222", "source": {"index": 26, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FEventRepository.php&line=222", "ajax": false, "filename": "EventRepository.php", "line": "222"}, "connection": "ticketgol", "explain": null, "start_percent": 24.118, "width_percent": 4.037}, {"sql": "select `id`, `shortcode` from `countries` where `countries`.`id` in (211) and `countries`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 26, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, {"index": 27, "namespace": null, "name": "app/Services/EventService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\EventService.php", "line": 71}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/Api/V1/EventController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\EventController.php", "line": 114}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 30, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.877985, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "EventRepository.php:222", "source": {"index": 26, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FEventRepository.php&line=222", "ajax": false, "filename": "EventRepository.php", "line": "222"}, "connection": "ticketgol", "explain": null, "start_percent": 28.154, "width_percent": 1.599}, {"sql": "select `country_id`, `locale`, `name` from `country_translations` where `locale` = 'en' and `country_translations`.`country_id` in (211)", "type": "query", "params": [], "bindings": ["en"], "hints": null, "show_copy": true, "backtrace": [{"index": 31, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, {"index": 32, "namespace": null, "name": "app/Services/EventService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\EventService.php", "line": 71}, {"index": 33, "namespace": null, "name": "app/Http/Controllers/Api/V1/EventController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\EventController.php", "line": 114}, {"index": 34, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 35, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.880441, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "EventRepository.php:222", "source": {"index": 31, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FEventRepository.php&line=222", "ajax": false, "filename": "EventRepository.php", "line": "222"}, "connection": "ticketgol", "explain": null, "start_percent": 29.754, "width_percent": 1.676}, {"sql": "select `sluggable_id`, `slug`, `locale` from `slugs` where `locale` = 'en' and `slugs`.`sluggable_id` in (5) and `slugs`.`sluggable_type` = 'App\\\\Models\\\\Stadium'", "type": "query", "params": [], "bindings": ["en", "App\\Models\\Stadium"], "hints": null, "show_copy": true, "backtrace": [{"index": 26, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, {"index": 27, "namespace": null, "name": "app/Services/EventService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\EventService.php", "line": 71}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/Api/V1/EventController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\EventController.php", "line": 114}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 30, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.8838599, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "EventRepository.php:222", "source": {"index": 26, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FEventRepository.php&line=222", "ajax": false, "filename": "EventRepository.php", "line": "222"}, "connection": "ticketgol", "explain": null, "start_percent": 31.429, "width_percent": 1.904}, {"sql": "select `id` from `leagues` where `leagues`.`id` in (4) and `leagues`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, {"index": 22, "namespace": null, "name": "app/Services/EventService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\EventService.php", "line": 71}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/Api/V1/EventController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\EventController.php", "line": 114}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 25, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.886984, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "EventRepository.php:222", "source": {"index": 21, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FEventRepository.php&line=222", "ajax": false, "filename": "EventRepository.php", "line": "222"}, "connection": "ticketgol", "explain": null, "start_percent": 33.333, "width_percent": 1.574}, {"sql": "select `league_id`, `locale`, `name` from `league_translations` where `locale` = 'en' and `league_translations`.`league_id` in (4)", "type": "query", "params": [], "bindings": ["en"], "hints": null, "show_copy": true, "backtrace": [{"index": 26, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, {"index": 27, "namespace": null, "name": "app/Services/EventService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\EventService.php", "line": 71}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/Api/V1/EventController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\EventController.php", "line": 114}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 30, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.8901129, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "EventRepository.php:222", "source": {"index": 26, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FEventRepository.php&line=222", "ajax": false, "filename": "EventRepository.php", "line": "222"}, "connection": "ticketgol", "explain": null, "start_percent": 34.907, "width_percent": 1.929}, {"sql": "select `sluggable_id`, `slug`, `locale` from `slugs` where `locale` = 'en' and `slugs`.`sluggable_id` in (4) and `slugs`.`sluggable_type` = 'App\\\\Models\\\\League'", "type": "query", "params": [], "bindings": ["en", "App\\Models\\League"], "hints": null, "show_copy": true, "backtrace": [{"index": 26, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, {"index": 27, "namespace": null, "name": "app/Services/EventService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\EventService.php", "line": 71}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/Api/V1/EventController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\EventController.php", "line": 114}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 30, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.892966, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "EventRepository.php:222", "source": {"index": 26, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FEventRepository.php&line=222", "ajax": false, "filename": "EventRepository.php", "line": "222"}, "connection": "ticketgol", "explain": null, "start_percent": 36.837, "width_percent": 1.574}, {"sql": "select `id` from `clubs` where `clubs`.`id` in (1) and `clubs`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, {"index": 22, "namespace": null, "name": "app/Services/EventService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\EventService.php", "line": 71}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/Api/V1/EventController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\EventController.php", "line": 114}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 25, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.895074, "duration": 0.00088, "duration_str": "880μs", "memory": 0, "memory_str": null, "filename": "EventRepository.php:222", "source": {"index": 21, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FEventRepository.php&line=222", "ajax": false, "filename": "EventRepository.php", "line": "222"}, "connection": "ticketgol", "explain": null, "start_percent": 38.411, "width_percent": 2.234}, {"sql": "select `club_id`, `locale`, `name` from `club_translations` where `locale` = 'en' and `club_translations`.`club_id` in (1)", "type": "query", "params": [], "bindings": ["en"], "hints": null, "show_copy": true, "backtrace": [{"index": 26, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, {"index": 27, "namespace": null, "name": "app/Services/EventService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\EventService.php", "line": 71}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/Api/V1/EventController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\EventController.php", "line": 114}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 30, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.898534, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "EventRepository.php:222", "source": {"index": 26, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FEventRepository.php&line=222", "ajax": false, "filename": "EventRepository.php", "line": "222"}, "connection": "ticketgol", "explain": null, "start_percent": 40.645, "width_percent": 1.777}, {"sql": "select `sluggable_id`, `slug`, `locale` from `slugs` where `locale` = 'en' and `slugs`.`sluggable_id` in (1) and `slugs`.`sluggable_type` = 'App\\\\Models\\\\Club'", "type": "query", "params": [], "bindings": ["en", "App\\Models\\Club"], "hints": null, "show_copy": true, "backtrace": [{"index": 26, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, {"index": 27, "namespace": null, "name": "app/Services/EventService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\EventService.php", "line": 71}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/Api/V1/EventController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\EventController.php", "line": 114}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 30, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.901076, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "EventRepository.php:222", "source": {"index": 26, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FEventRepository.php&line=222", "ajax": false, "filename": "EventRepository.php", "line": "222"}, "connection": "ticketgol", "explain": null, "start_percent": 42.422, "width_percent": 1.346}, {"sql": "select `id` from `clubs` where `clubs`.`id` in (5) and `clubs`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, {"index": 22, "namespace": null, "name": "app/Services/EventService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\EventService.php", "line": 71}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/Api/V1/EventController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\EventController.php", "line": 114}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 25, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.903934, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "EventRepository.php:222", "source": {"index": 21, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FEventRepository.php&line=222", "ajax": false, "filename": "EventRepository.php", "line": "222"}, "connection": "ticketgol", "explain": null, "start_percent": 43.767, "width_percent": 1.523}, {"sql": "select `club_id`, `locale`, `name` from `club_translations` where `locale` = 'en' and `club_translations`.`club_id` in (5)", "type": "query", "params": [], "bindings": ["en"], "hints": null, "show_copy": true, "backtrace": [{"index": 26, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, {"index": 27, "namespace": null, "name": "app/Services/EventService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\EventService.php", "line": 71}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/Api/V1/EventController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\EventController.php", "line": 114}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 30, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.906738, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "EventRepository.php:222", "source": {"index": 26, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FEventRepository.php&line=222", "ajax": false, "filename": "EventRepository.php", "line": "222"}, "connection": "ticketgol", "explain": null, "start_percent": 45.291, "width_percent": 1.599}, {"sql": "select `sluggable_id`, `slug`, `locale` from `slugs` where `locale` = 'en' and `slugs`.`sluggable_id` in (5) and `slugs`.`sluggable_type` = 'App\\\\Models\\\\Club'", "type": "query", "params": [], "bindings": ["en", "App\\Models\\Club"], "hints": null, "show_copy": true, "backtrace": [{"index": 26, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, {"index": 27, "namespace": null, "name": "app/Services/EventService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\EventService.php", "line": 71}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/Api/V1/EventController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\EventController.php", "line": 114}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 30, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.9093862, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "EventRepository.php:222", "source": {"index": 26, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FEventRepository.php&line=222", "ajax": false, "filename": "EventRepository.php", "line": "222"}, "connection": "ticketgol", "explain": null, "start_percent": 46.89, "width_percent": 1.625}, {"sql": "select `stadium_sectors`.`id`, `event_stadium_sectors`.`event_id` as `pivot_event_id`, `event_stadium_sectors`.`stadium_sector_id` as `pivot_stadium_sector_id`, `event_stadium_sectors`.`created_at` as `pivot_created_at`, `event_stadium_sectors`.`updated_at` as `pivot_updated_at` from `stadium_sectors` inner join `event_stadium_sectors` on `stadium_sectors`.`id` = `event_stadium_sectors`.`stadium_sector_id` where `event_stadium_sectors`.`event_id` in (7) and `stadium_sectors`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, {"index": 21, "namespace": null, "name": "app/Services/EventService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\EventService.php", "line": 71}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Api/V1/EventController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\EventController.php", "line": 114}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 24, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.9146798, "duration": 0.00623, "duration_str": "6.23ms", "memory": 0, "memory_str": null, "filename": "EventRepository.php:222", "source": {"index": 20, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FEventRepository.php&line=222", "ajax": false, "filename": "EventRepository.php", "line": "222"}, "connection": "ticketgol", "explain": null, "start_percent": 48.515, "width_percent": 15.816}, {"sql": "select `stadium_sector_id`, `locale`, `name` from `stadium_sector_translations` where `locale` = 'en' and `stadium_sector_translations`.`stadium_sector_id` in (44, 45, 48)", "type": "query", "params": [], "bindings": ["en"], "hints": null, "show_copy": true, "backtrace": [{"index": 25, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, {"index": 26, "namespace": null, "name": "app/Services/EventService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\EventService.php", "line": 71}, {"index": 27, "namespace": null, "name": "app/Http/Controllers/Api/V1/EventController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\EventController.php", "line": 114}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 29, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.925418, "duration": 0.0025099999999999996, "duration_str": "2.51ms", "memory": 0, "memory_str": null, "filename": "EventRepository.php:222", "source": {"index": 25, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FEventRepository.php&line=222", "ajax": false, "filename": "EventRepository.php", "line": "222"}, "connection": "ticketgol", "explain": null, "start_percent": 64.331, "width_percent": 6.372}, {"sql": "select `restrictions`.`id`, `event_restrictions`.`event_id` as `pivot_event_id`, `event_restrictions`.`restriction_id` as `pivot_restriction_id` from `restrictions` inner join `event_restrictions` on `restrictions`.`id` = `event_restrictions`.`restriction_id` where `event_restrictions`.`event_id` in (7) and `restrictions`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, {"index": 21, "namespace": null, "name": "app/Services/EventService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\EventService.php", "line": 71}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Api/V1/EventController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\EventController.php", "line": 114}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 24, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.930647, "duration": 0.0025, "duration_str": "2.5ms", "memory": 0, "memory_str": null, "filename": "EventRepository.php:222", "source": {"index": 20, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FEventRepository.php&line=222", "ajax": false, "filename": "EventRepository.php", "line": "222"}, "connection": "ticketgol", "explain": null, "start_percent": 70.703, "width_percent": 6.347}, {"sql": "select `restriction_id`, `locale`, `name` from `restriction_translations` where `locale` = 'en' and `restriction_translations`.`restriction_id` in (6, 7, 9)", "type": "query", "params": [], "bindings": ["en"], "hints": null, "show_copy": true, "backtrace": [{"index": 25, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, {"index": 26, "namespace": null, "name": "app/Services/EventService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\EventService.php", "line": 71}, {"index": 27, "namespace": null, "name": "app/Http/Controllers/Api/V1/EventController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\EventController.php", "line": 114}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 29, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.935677, "duration": 0.0015, "duration_str": "1.5ms", "memory": 0, "memory_str": null, "filename": "EventRepository.php:222", "source": {"index": 25, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FEventRepository.php&line=222", "ajax": false, "filename": "EventRepository.php", "line": "222"}, "connection": "ticketgol", "explain": null, "start_percent": 77.05, "width_percent": 3.808}, {"sql": "select * from `media` where `media`.`model_id` in (7) and `media`.`model_type` = 'App\\\\Models\\\\Event'", "type": "query", "params": [], "bindings": ["App\\Models\\Event"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, {"index": 22, "namespace": null, "name": "app/Services/EventService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\EventService.php", "line": 71}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/Api/V1/EventController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\EventController.php", "line": 114}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 25, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.939432, "duration": 0.0011200000000000001, "duration_str": "1.12ms", "memory": 0, "memory_str": null, "filename": "EventRepository.php:222", "source": {"index": 21, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FEventRepository.php&line=222", "ajax": false, "filename": "EventRepository.php", "line": "222"}, "connection": "ticketgol", "explain": null, "start_percent": 80.858, "width_percent": 2.843}, {"sql": "select `sluggable_id`, `slug`, `locale` from `slugs` where `locale` = 'en' and `slugs`.`sluggable_id` in (7) and `slugs`.`sluggable_type` = 'App\\\\Models\\\\Event'", "type": "query", "params": [], "bindings": ["en", "App\\Models\\Event"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, {"index": 22, "namespace": null, "name": "app/Services/EventService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\EventService.php", "line": 71}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/Api/V1/EventController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\EventController.php", "line": 114}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 25, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.942707, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "EventRepository.php:222", "source": {"index": 21, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FEventRepository.php&line=222", "ajax": false, "filename": "EventRepository.php", "line": "222"}, "connection": "ticketgol", "explain": null, "start_percent": 83.701, "width_percent": 1.574}, {"sql": "select tickets.id, tickets.quantity - COALESCE(SUM(r.quantity), 0) as available_quantity from `tickets` left join `ticket_reservations` as `r` on `r`.`ticket_id` = `tickets`.`id` and `status` in ('active', 'processing') where `tickets`.`event_id` = 7 and `tickets`.`event_id` is not null and `tickets`.`deleted_at` is null and `tickets`.`deleted_at` is null group by `tickets`.`id`, `tickets`.`quantity`", "type": "query", "params": [], "bindings": ["active", "processing", 7], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Services/EventService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\EventService.php", "line": 82}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Api/V1/EventController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\EventController.php", "line": 114}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 20, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}, {"index": 21, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingRoutingDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingRoutingDispatcher.php", "line": 35}], "start": **********.947711, "duration": 0.00513, "duration_str": "5.13ms", "memory": 0, "memory_str": null, "filename": "EventService.php:82", "source": {"index": 17, "namespace": null, "name": "app/Services/EventService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\EventService.php", "line": 82}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FServices%2FEventService.php&line=82", "ajax": false, "filename": "EventService.php", "line": "82"}, "connection": "ticketgol", "explain": null, "start_percent": 85.275, "width_percent": 13.024}, {"sql": "update `sessions` set `payload` = 'YTo1OntzOjY6Il90b2tlbiI7czo0MDoiS0xFOGV2OWtGMGp0VkVVUzQzaXViM3hnRVFYUmJtV09KUnFqNVoxSiI7czo2OiJfZmxhc2giO2E6Mjp7czozOiJuZXciO2E6MDp7fXM6Mzoib2xkIjthOjA6e319czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6Mzk6Imh0dHA6Ly90aWNrZXRnb2wudGVzdC9teS1hY2NvdW50L29yZGVycyI7fXM6NTA6ImxvZ2luX3dlYl81OWJhMzZhZGRjMmIyZjk0MDE1ODBmMDE0YzdmNThlYTRlMzA5ODlkIjtpOjc7czoxNzoicGFzc3dvcmRfaGFzaF93ZWIiO3M6NjA6IiQyeSQxMiRWMXdSNjRHTVFvbFRNQXBFM3d6bHlld0w4UDlic0FDM2FmWk9nSWhqOWZybUN1cXpZLi41ZSI7fQ==', `last_activity` = **********, `user_id` = 7, `ip_address` = '127.0.0.1', `user_agent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' where `id` = 'N75WHSWBQig1s7jRbuIicGDoQiErAj8tdVS6z3Wn'", "type": "query", "params": [], "bindings": ["YTo1OntzOjY6Il90b2tlbiI7czo0MDoiS0xFOGV2OWtGMGp0VkVVUzQzaXViM3hnRVFYUmJtV09KUnFqNVoxSiI7czo2OiJfZmxhc2giO2E6Mjp7czozOiJuZXciO2E6MDp7fXM6Mzoib2xkIjthOjA6e319czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6Mzk6Imh0dHA6Ly90aWNrZXRnb2wudGVzdC9teS1hY2NvdW50L29yZGVycyI7fXM6NTA6ImxvZ2luX3dlYl81OWJhMzZhZGRjMmIyZjk0MDE1ODBmMDE0YzdmNThlYTRlMzA5ODlkIjtpOjc7czoxNzoicGFzc3dvcmRfaGFzaF93ZWIiO3M6NjA6IiQyeSQxMiRWMXdSNjRHTVFvbFRNQXBFM3d6bHlld0w4UDlic0FDM2FmWk9nSWhqOWZybUN1cXpZLi41ZSI7fQ==", **********, 7, "127.0.0.1", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "N75WHSWBQig1s7jRbuIicGDoQiErAj8tdVS6z3Wn"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 173}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 140}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 175}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 245}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 130}], "start": **********.962352, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:173", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 173}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=173", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "173"}, "connection": "ticketgol", "explain": null, "start_percent": 98.299, "width_percent": 1.701}]}, "models": {"data": {"App\\Models\\Slug": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FSlug.php&line=1", "ajax": false, "filename": "Slug.php", "line": "?"}}, "App\\Models\\StadiumSector": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FStadiumSector.php&line=1", "ajax": false, "filename": "StadiumSector.php", "line": "?"}}, "App\\Models\\StadiumSectorTranslation": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FStadiumSectorTranslation.php&line=1", "ajax": false, "filename": "StadiumSectorTranslation.php", "line": "?"}}, "App\\Models\\Restriction": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FRestriction.php&line=1", "ajax": false, "filename": "Restriction.php", "line": "?"}}, "App\\Models\\RestrictionTranslation": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FRestrictionTranslation.php&line=1", "ajax": false, "filename": "RestrictionTranslation.php", "line": "?"}}, "App\\Models\\Club": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FClub.php&line=1", "ajax": false, "filename": "Club.php", "line": "?"}}, "App\\Models\\ClubTranslation": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FClubTranslation.php&line=1", "ajax": false, "filename": "ClubTranslation.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Event": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FEvent.php&line=1", "ajax": false, "filename": "Event.php", "line": "?"}}, "App\\Models\\EventTranslation": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FEventTranslation.php&line=1", "ajax": false, "filename": "EventTranslation.php", "line": "?"}}, "App\\Models\\Stadium": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FStadium.php&line=1", "ajax": false, "filename": "Stadium.php", "line": "?"}}, "App\\Models\\StadiumTranslation": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FStadiumTranslation.php&line=1", "ajax": false, "filename": "StadiumTranslation.php", "line": "?"}}, "App\\Models\\Country": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FCountry.php&line=1", "ajax": false, "filename": "Country.php", "line": "?"}}, "App\\Models\\CountryTranslation": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FCountryTranslation.php&line=1", "ajax": false, "filename": "CountryTranslation.php", "line": "?"}}, "App\\Models\\League": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FLeague.php&line=1", "ajax": false, "filename": "League.php", "line": "?"}}, "App\\Models\\LeagueTranslation": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FLeagueTranslation.php&line=1", "ajax": false, "filename": "LeagueTranslation.php", "line": "?"}}, "Spatie\\MediaLibrary\\MediaCollections\\Models\\Media": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Fspatie%2Flaravel-medialibrary%2Fsrc%2FMediaCollections%2FModels%2FMedia.php&line=1", "ajax": false, "filename": "Media.php", "line": "?"}}}, "count": 31, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "KLE8ev9kF0jtVEUS43iub3xgEQXRbmWOJRqj5Z1J", "_flash": "array:2 [\n  \"new\" => []\n  \"old\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://ticketgol.test/my-account/orders\"\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "7", "password_hash_web": "$2y$12$V1wR64GMQolTMApE3wzlyewL8P9bsAC3afZOgIhj9frmCuqzY..5e"}, "request": {"telescope": "<a href=\"http://ticketgol.test/_debugbar/telescope/9f4d06b1-8a99-4220-97b4-95083bb63cd1\" target=\"_blank\">View in Telescope</a>", "path_info": "/api/v1/events/event-jaunita-runolfsdottir-7", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-204055710 data-indent-pad=\"  \"><span class=sf-dump-note>array:11</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"853 characters\">ajs_anonymous_id=%2205cf8d6b-8a14-40b9-9d4f-c4ebd1620a6e%22; selected_locale=en; __stripe_mid=fd60eaa0-e90c-4914-bb27-ce58ed87fc6066be49; XSRF-TOKEN=eyJpdiI6Ilh0UVBOQUQ1VHRoM0ZtRmZjL0pjb2c9PSIsInZhbHVlIjoiRllqMHdHeXF4LzdDdmpkazFrRFJOdjZFQTZhQUFIZ2lYblhtbU4rM1pkV1BiejV6bW40L0tPZHZYbVBad0Y3eUM4elRQNmx0RHpTbXZ1K0FuSEduR2wzc3FYOFNCRkVmNTZqdllRVGw3KzhOWHNmZXI5aXd6b1N1bGZGSUcxT0ciLCJtYWMiOiI3ODU5MmU4ZGI2ODgzN2VjODc5MWRlOTExMDdjYTM3MjA1ZDA0Nzk4YWY2YTc5M2VhY2MyZDkyNTM3ZGM5N2Y4IiwidGFnIjoiIn0%3D; ticketgol_session=eyJpdiI6IjF1a1RPWTlGMUtzaGs5bVdlODdXS1E9PSIsInZhbHVlIjoidkFucXJJb3pBQlhVdWFaVmVsZDJ5Y29YWVFEMUl5bS9EYXllS05SMHBtWE9vdVl6bi9TVDZoUlVwVlNoMlEvTjQrODU5TlplUElmSEN3YXdsQmNaZ3pXamFBZVpBSjUzZlBWZUdrcGdsNVJIeVdObHJoMEZsV2FENDk4YTA3My8iLCJtYWMiOiI5ZTUxZTQ2YzdiOTA2ZWI1ODUzNjY1ZGNlMGU0NTM2ZDhhNjQxYmQyOGU0NGJhODE3YTZhNGNmNzM3ZTdiNWUyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">en-GB,en;q=0.9,es;q=0.8,en-US;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"51 characters\">http://ticketgol.test/event-jaunita-runolfsdottir-7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-locale</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6Ilh0UVBOQUQ1VHRoM0ZtRmZjL0pjb2c9PSIsInZhbHVlIjoiRllqMHdHeXF4LzdDdmpkazFrRFJOdjZFQTZhQUFIZ2lYblhtbU4rM1pkV1BiejV6bW40L0tPZHZYbVBad0Y3eUM4elRQNmx0RHpTbXZ1K0FuSEduR2wzc3FYOFNCRkVmNTZqdllRVGw3KzhOWHNmZXI5aXd6b1N1bGZGSUcxT0ciLCJtYWMiOiI3ODU5MmU4ZGI2ODgzN2VjODc5MWRlOTExMDdjYTM3MjA1ZDA0Nzk4YWY2YTc5M2VhY2MyZDkyNTM3ZGM5N2Y4IiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">ticketgol.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-204055710\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1831997968 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>ajs_anonymous_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>selected_locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">KLE8ev9kF0jtVEUS43iub3xgEQXRbmWOJRqj5Z1J</span>\"\n  \"<span class=sf-dump-key>ticketgol_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">N75WHSWBQig1s7jRbuIicGDoQiErAj8tdVS6z3Wn</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1831997968\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1341169253 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 03 Jul 2025 08:35:35 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6Imt3T0JaKzV2NUlFazFKb3l6MUs4T3c9PSIsInZhbHVlIjoiT1FUN2lwaE1CNUdoOVFndEJ5MFdjTW5DblpCWlZiRjJyZ3FUYnJGRkt3Y3ZuRmU3S2pIRFdxL1lGL3ZnbFV5QnVJN2FoRHNIV0ZGaVZxYWtPOU5hczlGT0ZRQzI3V2EyTUtJUllFVEZQUFRrQ2IwM3ZVZTZuYmVXcGpSNjJ1MGkiLCJtYWMiOiJjNjBhYzk5ZTBjOWFiOTk4YjUwNTU3MTUzNmI0MTg1MmVlZWYyYzJkZjk0Y2YwOWJhYTMyYjU4NzA5ZTZiYTM2IiwidGFnIjoiIn0%3D; expires=Thu, 03 Jul 2025 10:35:35 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"445 characters\">ticketgol_session=eyJpdiI6Ik9UWUJjdmsrUUFCYVpTcFJmdVkwU0E9PSIsInZhbHVlIjoiOTB1WlRLMkxvNXg0WStkd1pFeVJBamFUbVpuVkdBSUs3S2JsOGhkRERnWE5WcGVjUnpmemhvUjVibW52TVhQaEg2V0pDbFlFaTY0UTFsVnVwekxHWXFpM0VIRlNHb25nTk9JMHdwQjhuQVZQZ1FNbzJiQ1ptSGtvZHpBaTJ5MXEiLCJtYWMiOiI1YjcwMjU1Y2QyMTU1MmVkM2YwYTdiZDE4ODU5ZGFiNDJiN2Q2YTgzZDZmMTMwNTVkZTRkZDQyZGJjMmI3OWQ0IiwidGFnIjoiIn0%3D; expires=Thu, 03 Jul 2025 10:35:35 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6Imt3T0JaKzV2NUlFazFKb3l6MUs4T3c9PSIsInZhbHVlIjoiT1FUN2lwaE1CNUdoOVFndEJ5MFdjTW5DblpCWlZiRjJyZ3FUYnJGRkt3Y3ZuRmU3S2pIRFdxL1lGL3ZnbFV5QnVJN2FoRHNIV0ZGaVZxYWtPOU5hczlGT0ZRQzI3V2EyTUtJUllFVEZQUFRrQ2IwM3ZVZTZuYmVXcGpSNjJ1MGkiLCJtYWMiOiJjNjBhYzk5ZTBjOWFiOTk4YjUwNTU3MTUzNmI0MTg1MmVlZWYyYzJkZjk0Y2YwOWJhYTMyYjU4NzA5ZTZiYTM2IiwidGFnIjoiIn0%3D; expires=Thu, 03-Jul-2025 10:35:35 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"417 characters\">ticketgol_session=eyJpdiI6Ik9UWUJjdmsrUUFCYVpTcFJmdVkwU0E9PSIsInZhbHVlIjoiOTB1WlRLMkxvNXg0WStkd1pFeVJBamFUbVpuVkdBSUs3S2JsOGhkRERnWE5WcGVjUnpmemhvUjVibW52TVhQaEg2V0pDbFlFaTY0UTFsVnVwekxHWXFpM0VIRlNHb25nTk9JMHdwQjhuQVZQZ1FNbzJiQ1ptSGtvZHpBaTJ5MXEiLCJtYWMiOiI1YjcwMjU1Y2QyMTU1MmVkM2YwYTdiZDE4ODU5ZGFiNDJiN2Q2YTgzZDZmMTMwNTVkZTRkZDQyZGJjMmI3OWQ0IiwidGFnIjoiIn0%3D; expires=Thu, 03-Jul-2025 10:35:35 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1341169253\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">KLE8ev9kF0jtVEUS43iub3xgEQXRbmWOJRqj5Z1J</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>new</span>\" => []\n    \"<span class=sf-dump-key>old</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://ticketgol.test/my-account/orders</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$V1wR64GMQolTMApE3wzlyewL8P9bsAC3afZOgIhj9frmCuqzY..5e</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}