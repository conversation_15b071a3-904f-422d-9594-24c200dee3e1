<?php

namespace App\Services;

use App\DTO\OrderFilterDTO;
use App\DTO\OrderStatusDTO;
use App\DTO\OrderStoreDTO;
use App\Enums\OrderStatus;
use App\Enums\OrderTransactionStatus;
use App\Http\Resources\OrderCollection;
use App\Jobs\UpdateOrderMetaDataJob;
use App\Repositories\OrderRepository;
use App\Repositories\OrderTransactionRepository;
use App\Repositories\TicketReservationRepository;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Stripe\StripeClient;

class OrderService
{
    private const EURO_MINOR_UNITS = 100;

    private $orderRepository;

    private $orderTransactionRepository;

    private $ticketReservationRepository;

    private $stripe;

    public function __construct()
    {
        $this->orderRepository = app(OrderRepository::class);
        $this->orderTransactionRepository = app(OrderTransactionRepository::class);
        $this->ticketReservationRepository = app(TicketReservationRepository::class);
        $this->stripe = new StripeClient(config('services.stripe.secret'));
    }

    // Current implementation
    public function getPaginatedOrdersForUser($userId, OrderFilterDTO $filtersDTO)
    {
        $query = $this->orderRepository->getOrdersForUser($userId, $filtersDTO);
        $orders = $query->paginate(config('services.ticketgol.items_per_page'));

        return new OrderCollection($orders);
    }

    public function createOrder(OrderStoreDTO $orderDTO)
    {
        $order = $this->orderRepository->createOrder($orderDTO);

        $orderTransaction = $this->orderTransactionRepository->initiateTransaction($order, $orderDTO);

        $this->ticketReservationRepository->reservationProcessing($orderDTO->tempTicketReservationId);

        UpdateOrderMetaDataJob::dispatch($order->id);

        return [$order, $orderTransaction];
    }

    public function getOrderDetail($orderId)
    {
        return $this->orderRepository->findById($orderId);
    }

    public function checkOrderStatus(OrderStatusDTO $orderStatusDTO)
    {
        $orderId = decrypt($orderStatusDTO->orderId);
        $orderTransaction = $this->orderTransactionRepository->findByOrderId($orderId);

        if ($orderTransaction) {

            if ($orderTransaction->status === OrderTransactionStatus::PENDING) {
                $paymentIntent = $this->stripe->paymentIntents->retrieve($orderTransaction->payment_intent_id);
                if ($paymentIntent->status === 'succeeded') {
                    $stripeWebhookService = app(StripeWebhookService::class);
                    $orderTransaction = $stripeWebhookService->confirmOrderAndTransaction($orderTransaction);
                }
            }

            $order = $orderTransaction->order;

            return [
                'order_id' => $order->id,
                'status' => $order->status,
                'is_completed' => $order->status === OrderStatus::CONFIRMED
                    && $orderTransaction->status === OrderTransactionStatus::COMPLETED,
            ];
        }

        return $orderTransaction;
    }

    public function getStatusOptions()
    {
        $options = collect(OrderStatus::cases())
            ->filter(fn($status) => $status !== OrderStatus::EXPIRED)
            ->map(function ($status) {
                return [
                    'label' => $status->getLabel(),
                    'value' => $status->value,
                ];
            });

        $options->unshift([
            'label' => __('common.all') ?? 'All',
            'value' => '',
        ]);

        return $options;
    }

    public function createStripePaymentIntent($order, $orderDTO)
    {
        $user = Auth::user();

        return $this->stripe->paymentIntents->create([
            'payment_method_types' => ['card'],
            'amount' => $order->grand_total * self::EURO_MINOR_UNITS,
            'currency' => $orderDTO->currencyCode,
            'metadata' => [
                'order_id' => $order->id,
                'user_id' => $user->id,
                'ticket_reservation_id' => $order->ticket_reservation_id,
                'customer_email' => $user->email,
            ],
        ]);
    }

    public function expireOrderAndTransaction($order)
    {
        $this->orderRepository->expireOrder($order);

        $this->orderTransactionRepository->expireTransaction($order->transaction);
        try {
            $paymentIntent = $this->stripe->paymentIntents->retrieve($order->transaction->payment_intent_id);
            if (in_array($paymentIntent->status, ['requires_payment_method', 'requires_confirmation', 'requires_action'])) {
                Log::channel('stripe')->info('Payment Intent cancelled through SDK', ['payment_intent_id' => $order->transaction->payment_intent_id]);
                $this->stripe->paymentIntents->cancel($paymentIntent->id);
            }
        } catch (\Exception $e) {
            Log::channel('stripe')->error("Failed to cancel payment intent: {$e->getMessage()}");
        }
    }

    public function getOrderDetails(int $userId, int $orderId)
    {
        return $this->orderRepository->getOrderWithDetails($userId, $orderId);
    }
}
