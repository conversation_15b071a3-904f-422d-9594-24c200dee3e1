<?php return array (
  'amidesfahani/filament-tinyeditor' => 
  array (
    'providers' => 
    array (
      0 => 'Amid<PERSON><PERSON>hani\\FilamentTinyEditor\\TinyeditorServiceProvider',
    ),
  ),
  'anourvalar/eloquent-serialize' => 
  array (
    'aliases' => 
    array (
      'EloquentSerialize' => 'AnourValar\\EloquentSerialize\\Facades\\EloquentSerializeFacade',
    ),
  ),
  'awcodes/filament-badgeable-column' => 
  array (
    'providers' => 
    array (
      0 => 'Awcodes\\FilamentBadgeableColumn\\BadgeableColumnServiceProvider',
    ),
  ),
  'barryvdh/laravel-debugbar' => 
  array (
    'aliases' => 
    array (
      'Debugbar' => 'Barryvdh\\Debugbar\\Facades\\Debugbar',
    ),
    'providers' => 
    array (
      0 => 'Barryvdh\\Debugbar\\ServiceProvider',
    ),
  ),
  'barryvdh/laravel-dompdf' => 
  array (
    'aliases' => 
    array (
      'PDF' => 'Barryvdh\\DomPDF\\Facade\\Pdf',
      'Pdf' => 'Barryvdh\\DomPDF\\Facade\\Pdf',
    ),
    'providers' => 
    array (
      0 => 'Barryvdh\\DomPDF\\ServiceProvider',
    ),
  ),
  'beyondcode/laravel-query-detector' => 
  array (
    'providers' => 
    array (
      0 => 'BeyondCode\\QueryDetector\\QueryDetectorServiceProvider',
    ),
  ),
  'bezhansalleh/filament-language-switch' => 
  array (
    'providers' => 
    array (
      0 => 'BezhanSalleh\\FilamentLanguageSwitch\\FilamentLanguageSwitchServiceProvider',
    ),
  ),
  'bezhansalleh/filament-shield' => 
  array (
    'aliases' => 
    array (
      'FilamentShield' => 'BezhanSalleh\\FilamentShield\\Facades\\FilamentShield',
    ),
    'providers' => 
    array (
      0 => 'BezhanSalleh\\FilamentShield\\FilamentShieldServiceProvider',
    ),
  ),
  'blade-ui-kit/blade-heroicons' => 
  array (
    'providers' => 
    array (
      0 => 'BladeUI\\Heroicons\\BladeHeroiconsServiceProvider',
    ),
  ),
  'blade-ui-kit/blade-icons' => 
  array (
    'providers' => 
    array (
      0 => 'BladeUI\\Icons\\BladeIconsServiceProvider',
    ),
  ),
  'dotswan/filament-laravel-pulse' => 
  array (
    'aliases' => 
    array (
      'FilamentLaravelPulse' => 'Dotswan\\FilamentLaravelPulse\\Facades\\FilamentLaravelPulse',
    ),
    'providers' => 
    array (
      0 => 'Dotswan\\FilamentLaravelPulse\\FilamentLaravelPulseServiceProvider',
    ),
  ),
  'filament/actions' => 
  array (
    'providers' => 
    array (
      0 => 'Filament\\Actions\\ActionsServiceProvider',
    ),
  ),
  'filament/filament' => 
  array (
    'providers' => 
    array (
      0 => 'Filament\\FilamentServiceProvider',
    ),
  ),
  'filament/forms' => 
  array (
    'providers' => 
    array (
      0 => 'Filament\\Forms\\FormsServiceProvider',
    ),
  ),
  'filament/infolists' => 
  array (
    'providers' => 
    array (
      0 => 'Filament\\Infolists\\InfolistsServiceProvider',
    ),
  ),
  'filament/notifications' => 
  array (
    'providers' => 
    array (
      0 => 'Filament\\Notifications\\NotificationsServiceProvider',
    ),
  ),
  'filament/support' => 
  array (
    'providers' => 
    array (
      0 => 'Filament\\Support\\SupportServiceProvider',
    ),
  ),
  'filament/tables' => 
  array (
    'providers' => 
    array (
      0 => 'Filament\\Tables\\TablesServiceProvider',
    ),
  ),
  'filament/widgets' => 
  array (
    'providers' => 
    array (
      0 => 'Filament\\Widgets\\WidgetsServiceProvider',
    ),
  ),
  'inertiajs/inertia-laravel' => 
  array (
    'providers' => 
    array (
      0 => 'Inertia\\ServiceProvider',
    ),
  ),
  'kirschbaum-development/eloquent-power-joins' => 
  array (
    'providers' => 
    array (
      0 => 'Kirschbaum\\PowerJoins\\PowerJoinsServiceProvider',
    ),
  ),
  'laravel/breeze' => 
  array (
    'providers' => 
    array (
      0 => 'Laravel\\Breeze\\BreezeServiceProvider',
    ),
  ),
  'laravel/pail' => 
  array (
    'providers' => 
    array (
      0 => 'Laravel\\Pail\\PailServiceProvider',
    ),
  ),
  'laravel/pulse' => 
  array (
    'aliases' => 
    array (
      'Pulse' => 'Laravel\\Pulse\\Facades\\Pulse',
    ),
    'providers' => 
    array (
      0 => 'Laravel\\Pulse\\PulseServiceProvider',
    ),
  ),
  'laravel/sail' => 
  array (
    'providers' => 
    array (
      0 => 'Laravel\\Sail\\SailServiceProvider',
    ),
  ),
  'laravel/sanctum' => 
  array (
    'providers' => 
    array (
      0 => 'Laravel\\Sanctum\\SanctumServiceProvider',
    ),
  ),
  'laravel/telescope' => 
  array (
    'providers' => 
    array (
      0 => 'Laravel\\Telescope\\TelescopeServiceProvider',
    ),
  ),
  'laravel/tinker' => 
  array (
    'providers' => 
    array (
      0 => 'Laravel\\Tinker\\TinkerServiceProvider',
    ),
  ),
  'livewire/livewire' => 
  array (
    'providers' => 
    array (
      0 => 'Livewire\\LivewireServiceProvider',
    ),
    'aliases' => 
    array (
      'Livewire' => 'Livewire\\Livewire',
    ),
  ),
  'nesbot/carbon' => 
  array (
    'providers' => 
    array (
      0 => 'Carbon\\Laravel\\ServiceProvider',
    ),
  ),
  'nunomaduro/collision' => 
  array (
    'providers' => 
    array (
      0 => 'NunoMaduro\\Collision\\Adapters\\Laravel\\CollisionServiceProvider',
    ),
  ),
  'nunomaduro/termwind' => 
  array (
    'providers' => 
    array (
      0 => 'Termwind\\Laravel\\TermwindServiceProvider',
    ),
  ),
  'opcodesio/log-viewer' => 
  array (
    'aliases' => 
    array (
      'LogViewer' => 'Opcodes\\LogViewer\\Facades\\LogViewer',
    ),
    'providers' => 
    array (
      0 => 'Opcodes\\LogViewer\\LogViewerServiceProvider',
    ),
  ),
  'pestphp/pest-plugin-laravel' => 
  array (
    'providers' => 
    array (
      0 => 'Pest\\Laravel\\PestServiceProvider',
    ),
  ),
  'pxlrbt/filament-environment-indicator' => 
  array (
    'providers' => 
    array (
      0 => '\\pxlrbt\\FilamentEnvironmentIndicator\\FilamentEnvironmentIndicatorServiceProvider',
    ),
  ),
  'resend/resend-laravel' => 
  array (
    'providers' => 
    array (
      0 => 'Resend\\Laravel\\ResendServiceProvider',
    ),
  ),
  'ryangjchandler/blade-capture-directive' => 
  array (
    'providers' => 
    array (
      0 => 'RyanChandler\\BladeCaptureDirective\\BladeCaptureDirectiveServiceProvider',
    ),
    'aliases' => 
    array (
      'BladeCaptureDirective' => 'RyanChandler\\BladeCaptureDirective\\Facades\\BladeCaptureDirective',
    ),
  ),
  'sentry/sentry-laravel' => 
  array (
    'aliases' => 
    array (
      'Sentry' => 'Sentry\\Laravel\\Facade',
    ),
    'providers' => 
    array (
      0 => 'Sentry\\Laravel\\ServiceProvider',
      1 => 'Sentry\\Laravel\\Tracing\\ServiceProvider',
    ),
  ),
  'shuvroroy/filament-spatie-laravel-health' => 
  array (
    'providers' => 
    array (
      0 => 'ShuvroRoy\\FilamentSpatieLaravelHealth\\FilamentSpatieLaravelHealthServiceProvider',
    ),
  ),
  'spatie/laravel-activitylog' => 
  array (
    'providers' => 
    array (
      0 => 'Spatie\\Activitylog\\ActivitylogServiceProvider',
    ),
  ),
  'spatie/laravel-health' => 
  array (
    'aliases' => 
    array (
      'Health' => 'Spatie\\Health\\Facades\\Health',
    ),
    'providers' => 
    array (
      0 => 'Spatie\\Health\\HealthServiceProvider',
    ),
  ),
  'spatie/laravel-medialibrary' => 
  array (
    'providers' => 
    array (
      0 => 'Spatie\\MediaLibrary\\MediaLibraryServiceProvider',
    ),
  ),
  'spatie/laravel-permission' => 
  array (
    'providers' => 
    array (
      0 => 'Spatie\\Permission\\PermissionServiceProvider',
    ),
  ),
  'spatie/laravel-web-tinker' => 
  array (
    'providers' => 
    array (
      0 => 'Spatie\\WebTinker\\WebTinkerServiceProvider',
    ),
  ),
  'tightenco/ziggy' => 
  array (
    'providers' => 
    array (
      0 => 'Tighten\\Ziggy\\ZiggyServiceProvider',
    ),
  ),
  'z3d0x/filament-logger' => 
  array (
    'aliases' => 
    array (
      'FilamentLogger' => 'Z3d0X\\FilamentLogger\\Facades\\FilamentLogger',
    ),
    'providers' => 
    array (
      0 => 'Z3d0X\\FilamentLogger\\FilamentLoggerServiceProvider',
    ),
  ),
);