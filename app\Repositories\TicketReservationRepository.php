<?php

namespace App\Repositories;

use App\DTO\TicketReservationLockDTO;
use App\Enums\TicketReservationStatus;
use App\Models\TicketReservation;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Redis;

class TicketReservationRepository extends BaseRepository
{
    private const TICKET_RESERVED_PREFIX = 'ticket:{ticketId}:reserved_counter';

    private const TICKET_RESERVATION_WITH_USER_PREFIX = 'temp_ticket_reservation:users:{userId}';

    public function __construct(TicketReservation $model)
    {
        $this->model = $model;
    }

    public function getReservedTickets($ticketId)
    {
        $reservedKey = str_replace('{ticketId}', $ticketId, self::TICKET_RESERVED_PREFIX);

        if (Redis::exists($reservedKey)) {
            $reservedCount = (int) Redis::get($reservedKey);

            return $reservedCount;
        } else {
            $activeReservations = $this->model->where('ticket_id', $ticketId)
                ->reserved()
                ->get();
            $reservedQuantity = $activeReservations->sum('quantity') ?? 0;

            Redis::transaction(function () use ($reservedKey, $reservedQuantity) {
                Redis::set($reservedKey, $reservedQuantity);
            });

            return $reservedQuantity;
        }
    }

    public function reserveTicket(TicketReservationLockDTO $ticketReservationDTO)
    {
        $user = Auth::user();

        $reservation = $this->model->create([
            'ticket_id' => $ticketReservationDTO->ticketId,
            'user_id' => $user->id,
            'quantity' => $ticketReservationDTO->quantity,
            'price' => $ticketReservationDTO->price,
            'status' => TicketReservationStatus::ACTIVE,
            'expires_at' => now()->addMinutes(config('services.ticketgol.temp_reservation_minutes')),
        ]);

        $reservedCounterKey = str_replace('{ticketId}', $reservation->ticket_id, self::TICKET_RESERVED_PREFIX);
        $ticketReservationUserKey = str_replace('{userId}', $user->id, self::TICKET_RESERVATION_WITH_USER_PREFIX);

        $encryptedReservationId = encrypt($reservation->id);

        Redis::transaction(function () use ($ticketReservationUserKey, $encryptedReservationId, $reservedCounterKey, $ticketReservationDTO) {
            Redis::set($ticketReservationUserKey, $encryptedReservationId, 'EX', config('services.ticketgol.temp_reservation_minutes') * 60);
            Redis::incrby($reservedCounterKey, $ticketReservationDTO->quantity);
        });

        return $encryptedReservationId;
    }

    public function checkIfUserHasReservationInProgress($userId)
    {
        $ticketReservationId = '';
        $redisKey = str_replace('{userId}', $userId, self::TICKET_RESERVATION_WITH_USER_PREFIX);

        if (Redis::exists($redisKey)) {
            $ticketReservationId = Redis::get($redisKey);
        }

        return $ticketReservationId;
    }

    public function getReservedTicketById($reservationId)
    {
        $relations = [
            'ticket:id,ticket_type,event_id,sector_id,currency_code,quantity',
            'ticket.event:id,date,time,timezone,stadium_id',
            'ticket.event.translation:event_id,name,locale',
            'ticket.event.stadium:id,address_line_1,address_line_2,postcode,country_id',
            'ticket.event.stadium.translation:stadium_id,name,locale',
            'ticket.event.stadium.country:id,shortcode',
            'ticket.event.stadium.country.translation:country_id,name',
            'ticket.sector:id',
            'ticket.sector.translation:stadium_sector_id,name',
            'ticket.restrictions:id',
            'ticket.restrictions.translation:restriction_id,name',
            'ticket.event.restrictions:id',
            'ticket.event.restrictions.translation:restriction_id,name',
            'user:id,name,email,user_type',
            'user.userDetail:user_id,address,city,phone,zip,country_id',
            'user.userDetail.country:id,shortcode',
            'user.userDetail.country.translation:country_id,name',
            'order:id,ticket_reservation_id',
            'order.transaction:id,order_id,payment_intent_id',
            'order.attendees:id,order_id,name,email,dob,gender',
        ];

        return $this->findById($reservationId, ['id', 'ticket_id', 'user_id', 'quantity', 'price', 'status', 'expires_at'], $relations);
    }

    public function updateReservationStatus($ticketReservationId, $status)
    {
        $ticketReservation = $this->model->findOrFail($ticketReservationId);

        $ticketReservation->update([
            'status' => $status,
        ]);

        return $ticketReservation;
    }

    public function reservationProcessing($ticketReservationId)
    {
        $ticketReservation = $this->model->findOrFail($ticketReservationId);

        $ticketReservation->update([
            'status' => TicketReservationStatus::PROCESSING,
        ]);

        return $ticketReservation;
    }

    public function updateCounterAndCompleteReservation($ticketReservationId)
    {
        $ticketReservation = $this->model->findOrFail($ticketReservationId);

        $ticketReservation->update([
            'status' => TicketReservationStatus::COMPLETED,
        ]);

        Log::channel('stripe')->info('Reservation status updated to completed', ['ticket_reservation' => $ticketReservation]);

        $reservedCounterKey = str_replace('{ticketId}', $ticketReservation->ticket_id, self::TICKET_RESERVED_PREFIX);
        $ticketReservationWithUserKey = str_replace('{userId}', $ticketReservation->user_id, self::TICKET_RESERVATION_WITH_USER_PREFIX);

        Redis::transaction(function () use ($reservedCounterKey, $ticketReservationWithUserKey, $ticketReservation) {
            Log::channel('stripe')->info('reservedCounter is ', ['coutner' => Redis::get($reservedCounterKey)]);
            Redis::decrby($reservedCounterKey, $ticketReservation->quantity);
            Redis::del($ticketReservationWithUserKey);
        });

        return $ticketReservation;
    }

    public function getExpiredReservationsByStatus($status)
    {
        $date = now();

        if ($status === TicketReservationStatus::PROCESSING) {
            $date = now()->subMinutes(config('services.ticketgol.buffer_minutes_for_webhook_processing'));
        }

        $query = $this->model
            ->with(['order', 'order.transaction'])
            ->where('status', $status)
            ->where('expires_at', '<', $date);

        return $query->get();
    }

    public function updateExpiredRecords($reservations)
    {
        $ids = $reservations->pluck('id');

        Log::channel('stripe')->info('Updating expired records', ['ids' => $ids]);

        Redis::transaction(function () use ($reservations) {
            foreach ($reservations as $ticketReservation) {
                $reservedCounterKey = str_replace('{ticketId}', $ticketReservation->ticket_id, self::TICKET_RESERVED_PREFIX);
                Redis::decrby($reservedCounterKey, $ticketReservation->quantity);
            }
        });

        return $this->model->whereIn('id', $ids)
            ->update([
                'status' => TicketReservationStatus::EXPIRED,
            ]);
    }
}
