{"__meta": {"id": "X971012c300030d6f05c8c4acccaaf027", "datetime": "2025-07-02 10:57:03", "utime": **********.470359, "method": "GET", "uri": "/api/v1/events/event-jaunita-runolfsdottir-7", "ip": "127.0.0.1"}, "php": {"version": "8.4.6", "interface": "cgi-fcgi"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.259194, "end": **********.470372, "duration": 0.21117806434631348, "duration_str": "211ms", "measures": [{"label": "Booting", "start": **********.259194, "relative_start": 0, "end": **********.34412, "relative_end": **********.34412, "duration": 0.08492612838745117, "duration_str": "84.93ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.344134, "relative_start": 0.08494019508361816, "end": **********.470375, "relative_end": 3.0994415283203125e-06, "duration": 0.12624096870422363, "duration_str": "126ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 7361648, "peak_usage_str": "7MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET api/v1/events/{slug}", "middleware": "api, set-locale", "controller": "App\\Http\\Controllers\\Api\\V1\\EventController@show", "namespace": null, "prefix": "api/v1/events", "where": [], "as": "api.events.show", "file": "<a href=\"phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FHttp%2FControllers%2FApi%2FV1%2FEventController.php&line=111\" onclick=\"\">app/Http/Controllers/Api/V1/EventController.php:111-124</a>"}, "queries": {"nb_statements": 26, "nb_visible_statements": 26, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.04671, "accumulated_duration_str": "46.71ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `sessions` where `id` = '0SPVygoyCBWzh93wW1oy7sqhnBQ7iBtsSbwRhUjx' limit 1", "type": "query", "params": [], "bindings": ["0SPVygoyCBWzh93wW1oy7sqhnBQ7iBtsSbwRhUjx"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 147}], "start": **********.350345, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:97", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=97", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "97"}, "connection": "ticketgol", "explain": null, "start_percent": 0, "width_percent": 1.52}, {"sql": "select * from `users` where `id` = 7 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "auth.session", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\sanctum\\src\\Http\\Middleware\\AuthenticateSession.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.355336, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ticketgol", "explain": null, "start_percent": 1.52, "width_percent": 1.263}, {"sql": "select `id`, `date`, `time`, `timezone`, `category`, `stadium_id`, `league_id`, `home_club_id`, `guest_club_id`, `is_feature_event`, (select MIN(price) from `tickets` where `event_id` = `events`.`id` and `tickets`.`deleted_at` is null) as `min_price`, (select MAX(price) from `tickets` where `event_id` = `events`.`id` and `tickets`.`deleted_at` is null) as `max_price` from `events` where exists (select * from `slugs` where `events`.`id` = `slugs`.`sluggable_id` and `slugs`.`sluggable_type` = 'App\\\\Models\\\\Event' and `slug` = 'event-jaunita-runolfsdottir-7' and `locale` = 'en' and `locale` = 'en') and `is_published` = 1 and `events`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["App\\Models\\Event", "event-jaunita-runolfsdottir-7", "en", "en", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, {"index": 17, "namespace": null, "name": "app/Services/EventService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\EventService.php", "line": 71}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Api/V1/EventController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\EventController.php", "line": 114}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 20, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.363583, "duration": 0.00911, "duration_str": "9.11ms", "memory": 0, "memory_str": null, "filename": "EventRepository.php:222", "source": {"index": 16, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FEventRepository.php&line=222", "ajax": false, "filename": "EventRepository.php", "line": "222"}, "connection": "ticketgol", "explain": null, "start_percent": 2.783, "width_percent": 19.503}, {"sql": "select `event_id`, `locale`, `name`, `description`, `meta_title`, `meta_description`, `meta_keywords` from `event_translations` where `locale` = 'en' and `event_translations`.`event_id` in (7)", "type": "query", "params": [], "bindings": ["en"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, {"index": 22, "namespace": null, "name": "app/Services/EventService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\EventService.php", "line": 71}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/Api/V1/EventController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\EventController.php", "line": 114}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 25, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.3753932, "duration": 0.00188, "duration_str": "1.88ms", "memory": 0, "memory_str": null, "filename": "EventRepository.php:222", "source": {"index": 21, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FEventRepository.php&line=222", "ajax": false, "filename": "EventRepository.php", "line": "222"}, "connection": "ticketgol", "explain": null, "start_percent": 22.286, "width_percent": 4.025}, {"sql": "select `id`, `address_line_1`, `address_line_2`, `postcode`, `country_id` from `stadiums` where `stadiums`.`id` in (5) and `stadiums`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, {"index": 22, "namespace": null, "name": "app/Services/EventService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\EventService.php", "line": 71}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/Api/V1/EventController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\EventController.php", "line": 114}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 25, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.380222, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "EventRepository.php:222", "source": {"index": 21, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FEventRepository.php&line=222", "ajax": false, "filename": "EventRepository.php", "line": "222"}, "connection": "ticketgol", "explain": null, "start_percent": 26.311, "width_percent": 1.092}, {"sql": "select `stadium_id`, `locale`, `name` from `stadium_translations` where `locale` = 'en' and `stadium_translations`.`stadium_id` in (5)", "type": "query", "params": [], "bindings": ["en"], "hints": null, "show_copy": true, "backtrace": [{"index": 26, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, {"index": 27, "namespace": null, "name": "app/Services/EventService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\EventService.php", "line": 71}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/Api/V1/EventController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\EventController.php", "line": 114}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 30, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.3836, "duration": 0.00209, "duration_str": "2.09ms", "memory": 0, "memory_str": null, "filename": "EventRepository.php:222", "source": {"index": 26, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FEventRepository.php&line=222", "ajax": false, "filename": "EventRepository.php", "line": "222"}, "connection": "ticketgol", "explain": null, "start_percent": 27.403, "width_percent": 4.474}, {"sql": "select `id`, `shortcode` from `countries` where `countries`.`id` in (211) and `countries`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 26, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, {"index": 27, "namespace": null, "name": "app/Services/EventService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\EventService.php", "line": 71}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/Api/V1/EventController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\EventController.php", "line": 114}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 30, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.3880801, "duration": 0.00188, "duration_str": "1.88ms", "memory": 0, "memory_str": null, "filename": "EventRepository.php:222", "source": {"index": 26, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FEventRepository.php&line=222", "ajax": false, "filename": "EventRepository.php", "line": "222"}, "connection": "ticketgol", "explain": null, "start_percent": 31.878, "width_percent": 4.025}, {"sql": "select `country_id`, `locale`, `name` from `country_translations` where `locale` = 'en' and `country_translations`.`country_id` in (211)", "type": "query", "params": [], "bindings": ["en"], "hints": null, "show_copy": true, "backtrace": [{"index": 31, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, {"index": 32, "namespace": null, "name": "app/Services/EventService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\EventService.php", "line": 71}, {"index": 33, "namespace": null, "name": "app/Http/Controllers/Api/V1/EventController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\EventController.php", "line": 114}, {"index": 34, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 35, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.392861, "duration": 0.00107, "duration_str": "1.07ms", "memory": 0, "memory_str": null, "filename": "EventRepository.php:222", "source": {"index": 31, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FEventRepository.php&line=222", "ajax": false, "filename": "EventRepository.php", "line": "222"}, "connection": "ticketgol", "explain": null, "start_percent": 35.902, "width_percent": 2.291}, {"sql": "select `sluggable_id`, `slug`, `locale` from `slugs` where `locale` = 'en' and `slugs`.`sluggable_id` in (5) and `slugs`.`sluggable_type` = 'App\\\\Models\\\\Stadium'", "type": "query", "params": [], "bindings": ["en", "App\\Models\\Stadium"], "hints": null, "show_copy": true, "backtrace": [{"index": 26, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, {"index": 27, "namespace": null, "name": "app/Services/EventService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\EventService.php", "line": 71}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/Api/V1/EventController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\EventController.php", "line": 114}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 30, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.395865, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "EventRepository.php:222", "source": {"index": 26, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FEventRepository.php&line=222", "ajax": false, "filename": "EventRepository.php", "line": "222"}, "connection": "ticketgol", "explain": null, "start_percent": 38.193, "width_percent": 1.177}, {"sql": "select `id` from `leagues` where `leagues`.`id` in (4) and `leagues`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, {"index": 22, "namespace": null, "name": "app/Services/EventService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\EventService.php", "line": 71}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/Api/V1/EventController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\EventController.php", "line": 114}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 25, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.398149, "duration": 0.0014399999999999999, "duration_str": "1.44ms", "memory": 0, "memory_str": null, "filename": "EventRepository.php:222", "source": {"index": 21, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FEventRepository.php&line=222", "ajax": false, "filename": "EventRepository.php", "line": "222"}, "connection": "ticketgol", "explain": null, "start_percent": 39.371, "width_percent": 3.083}, {"sql": "select `league_id`, `locale`, `name` from `league_translations` where `locale` = 'en' and `league_translations`.`league_id` in (4)", "type": "query", "params": [], "bindings": ["en"], "hints": null, "show_copy": true, "backtrace": [{"index": 26, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, {"index": 27, "namespace": null, "name": "app/Services/EventService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\EventService.php", "line": 71}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/Api/V1/EventController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\EventController.php", "line": 114}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 30, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.402036, "duration": 0.0014399999999999999, "duration_str": "1.44ms", "memory": 0, "memory_str": null, "filename": "EventRepository.php:222", "source": {"index": 26, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FEventRepository.php&line=222", "ajax": false, "filename": "EventRepository.php", "line": "222"}, "connection": "ticketgol", "explain": null, "start_percent": 42.453, "width_percent": 3.083}, {"sql": "select `sluggable_id`, `slug`, `locale` from `slugs` where `locale` = 'en' and `slugs`.`sluggable_id` in (4) and `slugs`.`sluggable_type` = 'App\\\\Models\\\\League'", "type": "query", "params": [], "bindings": ["en", "App\\Models\\League"], "hints": null, "show_copy": true, "backtrace": [{"index": 26, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, {"index": 27, "namespace": null, "name": "app/Services/EventService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\EventService.php", "line": 71}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/Api/V1/EventController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\EventController.php", "line": 114}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 30, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.40531, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "EventRepository.php:222", "source": {"index": 26, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FEventRepository.php&line=222", "ajax": false, "filename": "EventRepository.php", "line": "222"}, "connection": "ticketgol", "explain": null, "start_percent": 45.536, "width_percent": 1.22}, {"sql": "select `id` from `clubs` where `clubs`.`id` in (1) and `clubs`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, {"index": 22, "namespace": null, "name": "app/Services/EventService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\EventService.php", "line": 71}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/Api/V1/EventController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\EventController.php", "line": 114}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 25, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.4075758, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "EventRepository.php:222", "source": {"index": 21, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FEventRepository.php&line=222", "ajax": false, "filename": "EventRepository.php", "line": "222"}, "connection": "ticketgol", "explain": null, "start_percent": 46.757, "width_percent": 2.355}, {"sql": "select `club_id`, `locale`, `name` from `club_translations` where `locale` = 'en' and `club_translations`.`club_id` in (1)", "type": "query", "params": [], "bindings": ["en"], "hints": null, "show_copy": true, "backtrace": [{"index": 26, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, {"index": 27, "namespace": null, "name": "app/Services/EventService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\EventService.php", "line": 71}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/Api/V1/EventController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\EventController.php", "line": 114}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 30, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.411107, "duration": 0.0015400000000000001, "duration_str": "1.54ms", "memory": 0, "memory_str": null, "filename": "EventRepository.php:222", "source": {"index": 26, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FEventRepository.php&line=222", "ajax": false, "filename": "EventRepository.php", "line": "222"}, "connection": "ticketgol", "explain": null, "start_percent": 49.112, "width_percent": 3.297}, {"sql": "select `sluggable_id`, `slug`, `locale` from `slugs` where `locale` = 'en' and `slugs`.`sluggable_id` in (1) and `slugs`.`sluggable_type` = 'App\\\\Models\\\\Club'", "type": "query", "params": [], "bindings": ["en", "App\\Models\\Club"], "hints": null, "show_copy": true, "backtrace": [{"index": 26, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, {"index": 27, "namespace": null, "name": "app/Services/EventService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\EventService.php", "line": 71}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/Api/V1/EventController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\EventController.php", "line": 114}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 30, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.414648, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "EventRepository.php:222", "source": {"index": 26, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FEventRepository.php&line=222", "ajax": false, "filename": "EventRepository.php", "line": "222"}, "connection": "ticketgol", "explain": null, "start_percent": 52.408, "width_percent": 1.199}, {"sql": "select `id` from `clubs` where `clubs`.`id` in (5) and `clubs`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, {"index": 22, "namespace": null, "name": "app/Services/EventService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\EventService.php", "line": 71}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/Api/V1/EventController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\EventController.php", "line": 114}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 25, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.417259, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "EventRepository.php:222", "source": {"index": 21, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FEventRepository.php&line=222", "ajax": false, "filename": "EventRepository.php", "line": "222"}, "connection": "ticketgol", "explain": null, "start_percent": 53.607, "width_percent": 1.22}, {"sql": "select `club_id`, `locale`, `name` from `club_translations` where `locale` = 'en' and `club_translations`.`club_id` in (5)", "type": "query", "params": [], "bindings": ["en"], "hints": null, "show_copy": true, "backtrace": [{"index": 26, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, {"index": 27, "namespace": null, "name": "app/Services/EventService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\EventService.php", "line": 71}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/Api/V1/EventController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\EventController.php", "line": 114}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 30, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.419901, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "EventRepository.php:222", "source": {"index": 26, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FEventRepository.php&line=222", "ajax": false, "filename": "EventRepository.php", "line": "222"}, "connection": "ticketgol", "explain": null, "start_percent": 54.828, "width_percent": 1.156}, {"sql": "select `sluggable_id`, `slug`, `locale` from `slugs` where `locale` = 'en' and `slugs`.`sluggable_id` in (5) and `slugs`.`sluggable_type` = 'App\\\\Models\\\\Club'", "type": "query", "params": [], "bindings": ["en", "App\\Models\\Club"], "hints": null, "show_copy": true, "backtrace": [{"index": 26, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, {"index": 27, "namespace": null, "name": "app/Services/EventService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\EventService.php", "line": 71}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/Api/V1/EventController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\EventController.php", "line": 114}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 30, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.421769, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "EventRepository.php:222", "source": {"index": 26, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FEventRepository.php&line=222", "ajax": false, "filename": "EventRepository.php", "line": "222"}, "connection": "ticketgol", "explain": null, "start_percent": 55.984, "width_percent": 1.22}, {"sql": "select `stadium_sectors`.`id`, `event_stadium_sectors`.`event_id` as `pivot_event_id`, `event_stadium_sectors`.`stadium_sector_id` as `pivot_stadium_sector_id`, `event_stadium_sectors`.`created_at` as `pivot_created_at`, `event_stadium_sectors`.`updated_at` as `pivot_updated_at` from `stadium_sectors` inner join `event_stadium_sectors` on `stadium_sectors`.`id` = `event_stadium_sectors`.`stadium_sector_id` where `event_stadium_sectors`.`event_id` in (7) and `stadium_sectors`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, {"index": 21, "namespace": null, "name": "app/Services/EventService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\EventService.php", "line": 71}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Api/V1/EventController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\EventController.php", "line": 114}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 24, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.4262738, "duration": 0.00552, "duration_str": "5.52ms", "memory": 0, "memory_str": null, "filename": "EventRepository.php:222", "source": {"index": 20, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FEventRepository.php&line=222", "ajax": false, "filename": "EventRepository.php", "line": "222"}, "connection": "ticketgol", "explain": null, "start_percent": 57.204, "width_percent": 11.818}, {"sql": "select `stadium_sector_id`, `locale`, `name` from `stadium_sector_translations` where `locale` = 'en' and `stadium_sector_translations`.`stadium_sector_id` in (44, 45, 48)", "type": "query", "params": [], "bindings": ["en"], "hints": null, "show_copy": true, "backtrace": [{"index": 25, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, {"index": 26, "namespace": null, "name": "app/Services/EventService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\EventService.php", "line": 71}, {"index": 27, "namespace": null, "name": "app/Http/Controllers/Api/V1/EventController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\EventController.php", "line": 114}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 29, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.4360158, "duration": 0.002, "duration_str": "2ms", "memory": 0, "memory_str": null, "filename": "EventRepository.php:222", "source": {"index": 25, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FEventRepository.php&line=222", "ajax": false, "filename": "EventRepository.php", "line": "222"}, "connection": "ticketgol", "explain": null, "start_percent": 69.022, "width_percent": 4.282}, {"sql": "select `restrictions`.`id`, `event_restrictions`.`event_id` as `pivot_event_id`, `event_restrictions`.`restriction_id` as `pivot_restriction_id` from `restrictions` inner join `event_restrictions` on `restrictions`.`id` = `event_restrictions`.`restriction_id` where `event_restrictions`.`event_id` in (7) and `restrictions`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, {"index": 21, "namespace": null, "name": "app/Services/EventService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\EventService.php", "line": 71}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Api/V1/EventController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\EventController.php", "line": 114}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 24, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.440557, "duration": 0.00294, "duration_str": "2.94ms", "memory": 0, "memory_str": null, "filename": "EventRepository.php:222", "source": {"index": 20, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FEventRepository.php&line=222", "ajax": false, "filename": "EventRepository.php", "line": "222"}, "connection": "ticketgol", "explain": null, "start_percent": 73.303, "width_percent": 6.294}, {"sql": "select `restriction_id`, `locale`, `name` from `restriction_translations` where `locale` = 'en' and `restriction_translations`.`restriction_id` in (6, 7, 9)", "type": "query", "params": [], "bindings": ["en"], "hints": null, "show_copy": true, "backtrace": [{"index": 25, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, {"index": 26, "namespace": null, "name": "app/Services/EventService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\EventService.php", "line": 71}, {"index": 27, "namespace": null, "name": "app/Http/Controllers/Api/V1/EventController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\EventController.php", "line": 114}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 29, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.4458148, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "EventRepository.php:222", "source": {"index": 25, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FEventRepository.php&line=222", "ajax": false, "filename": "EventRepository.php", "line": "222"}, "connection": "ticketgol", "explain": null, "start_percent": 79.598, "width_percent": 1.691}, {"sql": "select * from `media` where `media`.`model_id` in (7) and `media`.`model_type` = 'App\\\\Models\\\\Event'", "type": "query", "params": [], "bindings": ["App\\Models\\Event"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, {"index": 22, "namespace": null, "name": "app/Services/EventService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\EventService.php", "line": 71}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/Api/V1/EventController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\EventController.php", "line": 114}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 25, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.448442, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "EventRepository.php:222", "source": {"index": 21, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FEventRepository.php&line=222", "ajax": false, "filename": "EventRepository.php", "line": "222"}, "connection": "ticketgol", "explain": null, "start_percent": 81.289, "width_percent": 1.242}, {"sql": "select `sluggable_id`, `slug`, `locale` from `slugs` where `locale` = 'en' and `slugs`.`sluggable_id` in (7) and `slugs`.`sluggable_type` = 'App\\\\Models\\\\Event'", "type": "query", "params": [], "bindings": ["en", "App\\Models\\Event"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, {"index": 22, "namespace": null, "name": "app/Services/EventService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\EventService.php", "line": 71}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/Api/V1/EventController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\EventController.php", "line": 114}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 25, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.450813, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "EventRepository.php:222", "source": {"index": 21, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FEventRepository.php&line=222", "ajax": false, "filename": "EventRepository.php", "line": "222"}, "connection": "ticketgol", "explain": null, "start_percent": 82.531, "width_percent": 1.327}, {"sql": "select tickets.id, tickets.quantity - COALESCE(SUM(r.quantity), 0) as available_quantity from `tickets` left join `ticket_reservations` as `r` on `r`.`ticket_id` = `tickets`.`id` and `status` in ('active', 'processing') where `tickets`.`event_id` = 7 and `tickets`.`event_id` is not null and `tickets`.`deleted_at` is null and `tickets`.`deleted_at` is null group by `tickets`.`id`, `tickets`.`quantity`", "type": "query", "params": [], "bindings": ["active", "processing", 7], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Services/EventService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\EventService.php", "line": 82}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Api/V1/EventController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\EventController.php", "line": 114}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 20, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}, {"index": 21, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingRoutingDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingRoutingDispatcher.php", "line": 35}], "start": **********.454273, "duration": 0.0069500000000000004, "duration_str": "6.95ms", "memory": 0, "memory_str": null, "filename": "EventService.php:82", "source": {"index": 17, "namespace": null, "name": "app/Services/EventService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\EventService.php", "line": 82}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FServices%2FEventService.php&line=82", "ajax": false, "filename": "EventService.php", "line": "82"}, "connection": "ticketgol", "explain": null, "start_percent": 83.858, "width_percent": 14.879}, {"sql": "update `sessions` set `payload` = 'YTo2OntzOjY6Il90b2tlbiI7czo0MDoiMGV4SkJyZk5vUWFtWHJTeEJpSDVLR0ZnMGo4V3BhNk5mTGgzRlBvZSI7czozOiJ1cmwiO2E6MDp7fXM6OToiX3ByZXZpb3VzIjthOjE6e3M6MzoidXJsIjtzOjM5OiJodHRwOi8vdGlja2V0Z29sLnRlc3QvbXktYWNjb3VudC9vcmRlcnMiO31zOjY6Il9mbGFzaCI7YToyOntzOjM6Im9sZCI7YTowOnt9czozOiJuZXciO2E6MDp7fX1zOjUwOiJsb2dpbl93ZWJfNTliYTM2YWRkYzJiMmY5NDAxNTgwZjAxNGM3ZjU4ZWE0ZTMwOTg5ZCI7aTo3O3M6MTc6InBhc3N3b3JkX2hhc2hfd2ViIjtzOjYwOiIkMnkkMTIkVjF3UjY0R01Rb2xUTUFwRTN3emx5ZXdMOFA5YnNBQzNhZlpPZ0loajlmcm1DdXF6WS4uNWUiO30=', `last_activity` = **********, `user_id` = 7, `ip_address` = '127.0.0.1', `user_agent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' where `id` = '0SPVygoyCBWzh93wW1oy7sqhnBQ7iBtsSbwRhUjx'", "type": "query", "params": [], "bindings": ["YTo2OntzOjY6Il90b2tlbiI7czo0MDoiMGV4SkJyZk5vUWFtWHJTeEJpSDVLR0ZnMGo4V3BhNk5mTGgzRlBvZSI7czozOiJ1cmwiO2E6MDp7fXM6OToiX3ByZXZpb3VzIjthOjE6e3M6MzoidXJsIjtzOjM5OiJodHRwOi8vdGlja2V0Z29sLnRlc3QvbXktYWNjb3VudC9vcmRlcnMiO31zOjY6Il9mbGFzaCI7YToyOntzOjM6Im9sZCI7YTowOnt9czozOiJuZXciO2E6MDp7fX1zOjUwOiJsb2dpbl93ZWJfNTliYTM2YWRkYzJiMmY5NDAxNTgwZjAxNGM3ZjU4ZWE0ZTMwOTg5ZCI7aTo3O3M6MTc6InBhc3N3b3JkX2hhc2hfd2ViIjtzOjYwOiIkMnkkMTIkVjF3UjY0R01Rb2xUTUFwRTN3emx5ZXdMOFA5YnNBQzNhZlpPZ0loajlmcm1DdXF6WS4uNWUiO30=", **********, 7, "127.0.0.1", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "0SPVygoyCBWzh93wW1oy7sqhnBQ7iBtsSbwRhUjx"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 173}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 140}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 175}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 245}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 130}], "start": **********.4687688, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:173", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 173}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=173", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "173"}, "connection": "ticketgol", "explain": null, "start_percent": 98.737, "width_percent": 1.263}]}, "models": {"data": {"App\\Models\\Slug": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FSlug.php&line=1", "ajax": false, "filename": "Slug.php", "line": "?"}}, "App\\Models\\StadiumSector": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FStadiumSector.php&line=1", "ajax": false, "filename": "StadiumSector.php", "line": "?"}}, "App\\Models\\StadiumSectorTranslation": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FStadiumSectorTranslation.php&line=1", "ajax": false, "filename": "StadiumSectorTranslation.php", "line": "?"}}, "App\\Models\\Restriction": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FRestriction.php&line=1", "ajax": false, "filename": "Restriction.php", "line": "?"}}, "App\\Models\\RestrictionTranslation": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FRestrictionTranslation.php&line=1", "ajax": false, "filename": "RestrictionTranslation.php", "line": "?"}}, "App\\Models\\Club": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FClub.php&line=1", "ajax": false, "filename": "Club.php", "line": "?"}}, "App\\Models\\ClubTranslation": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FClubTranslation.php&line=1", "ajax": false, "filename": "ClubTranslation.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Event": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FEvent.php&line=1", "ajax": false, "filename": "Event.php", "line": "?"}}, "App\\Models\\EventTranslation": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FEventTranslation.php&line=1", "ajax": false, "filename": "EventTranslation.php", "line": "?"}}, "App\\Models\\Stadium": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FStadium.php&line=1", "ajax": false, "filename": "Stadium.php", "line": "?"}}, "App\\Models\\StadiumTranslation": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FStadiumTranslation.php&line=1", "ajax": false, "filename": "StadiumTranslation.php", "line": "?"}}, "App\\Models\\Country": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FCountry.php&line=1", "ajax": false, "filename": "Country.php", "line": "?"}}, "App\\Models\\CountryTranslation": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FCountryTranslation.php&line=1", "ajax": false, "filename": "CountryTranslation.php", "line": "?"}}, "App\\Models\\League": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FLeague.php&line=1", "ajax": false, "filename": "League.php", "line": "?"}}, "App\\Models\\LeagueTranslation": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FLeagueTranslation.php&line=1", "ajax": false, "filename": "LeagueTranslation.php", "line": "?"}}, "Spatie\\MediaLibrary\\MediaCollections\\Models\\Media": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Fspatie%2Flaravel-medialibrary%2Fsrc%2FMediaCollections%2FModels%2FMedia.php&line=1", "ajax": false, "filename": "Media.php", "line": "?"}}}, "count": 31, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "0exJBrfNoQamXrSxBiH5KGFg0j8Wpa6NfLh3FPoe", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://ticketgol.test/my-account/orders\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "7", "password_hash_web": "$2y$12$V1wR64GMQolTMApE3wzlyewL8P9bsAC3afZOgIhj9frmCuqzY..5e"}, "request": {"telescope": "<a href=\"http://ticketgol.test/_debugbar/telescope/9f4b364c-82a6-4423-983c-a4dabc0eb2c4\" target=\"_blank\">View in Telescope</a>", "path_info": "/api/v1/events/event-jaunita-runolfsdottir-7", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1286356553 data-indent-pad=\"  \"><span class=sf-dump-note>array:11</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"853 characters\">ajs_anonymous_id=%2205cf8d6b-8a14-40b9-9d4f-c4ebd1620a6e%22; selected_locale=en; __stripe_mid=fd60eaa0-e90c-4914-bb27-ce58ed87fc6066be49; XSRF-TOKEN=eyJpdiI6ImMydmhjT0cwcWgrRk9PSndDOTNQVnc9PSIsInZhbHVlIjoid2k4ZnQ4RkNOM0tjdXFyNXZsT2pFb2UxdnEybG4zL2ZXTDlxZEFLZlNoSG9IVmZsTVZ6NVpLRHdJMVpNSDZEdW9KaHVIYkYxUnBvc3FvenA1UG42cWJNNE1KZUVjRkszYkNSUUQzY3ZpNTNtRXpEcFVSQnMxd1lYVHBGeFR3am8iLCJtYWMiOiJlNWNiOGRmM2FiMzg5ZTY4MDQwN2RkMzhhNDZiYmM4OGZmY2VhZjk3OGZkMGU3ZmM1NmFiYjEzNWZlMjNmMDk2IiwidGFnIjoiIn0%3D; ticketgol_session=eyJpdiI6Im1wdGdsbW5BZENwMEhzK293dmEySlE9PSIsInZhbHVlIjoiRVhjcVpSVXVWY0pPbktlSTNkWGlsVlFGZTNQRXNFbU1hUkpBekJSR2h4d0VKSjZDR3dxY0lIM1I1Y0JyNzVUZGhVMFdoNlNrdnpweWpHL0NhS0NWV3BOSDNxT0J4RSs4djhiZmlKOEU3aVJ1SmlqU1Z3ZmJST0V4YzdFRUV6QzYiLCJtYWMiOiI3YWVmZmZiOGVlZDI2MDI4Y2IwZjMzYTJiYzY0YmQ0ZjgxZjk2NThiNTM5MGY0OTEwMzVkOGU0ODg2MjNlOTdlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">en-GB,en;q=0.9,es;q=0.8,en-US;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"63 characters\">http://ticketgol.test/ticket/sell/event-jaunita-runolfsdottir-7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-locale</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6ImMydmhjT0cwcWgrRk9PSndDOTNQVnc9PSIsInZhbHVlIjoid2k4ZnQ4RkNOM0tjdXFyNXZsT2pFb2UxdnEybG4zL2ZXTDlxZEFLZlNoSG9IVmZsTVZ6NVpLRHdJMVpNSDZEdW9KaHVIYkYxUnBvc3FvenA1UG42cWJNNE1KZUVjRkszYkNSUUQzY3ZpNTNtRXpEcFVSQnMxd1lYVHBGeFR3am8iLCJtYWMiOiJlNWNiOGRmM2FiMzg5ZTY4MDQwN2RkMzhhNDZiYmM4OGZmY2VhZjk3OGZkMGU3ZmM1NmFiYjEzNWZlMjNmMDk2IiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">ticketgol.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1286356553\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1948619228 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>ajs_anonymous_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>selected_locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0exJBrfNoQamXrSxBiH5KGFg0j8Wpa6NfLh3FPoe</span>\"\n  \"<span class=sf-dump-key>ticketgol_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0SPVygoyCBWzh93wW1oy7sqhnBQ7iBtsSbwRhUjx</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1948619228\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-958729478 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 02 Jul 2025 10:57:03 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6Inp6eWZRVCtTclN4MU5JaFVjQUFUUEE9PSIsInZhbHVlIjoiUVJBZEszNWpsSnphRnJ2bmVVek1NOHZmdXJCdjBUZGdSMTYxN21JNVlPSnZmM0pxeStlY3RKc1AxOWtJZzE2Z1l5WFN5Nm1lOUowdEdUWjRhNkhESThNQ0tjYmw4djdCRVBLUi9GaU9KMGlDTW1iLzVOc29YejRZUHBRemdvSm0iLCJtYWMiOiJjNTA0ZDA0NDBmNmE1MmRlYjhlNjc0NWY3YzQzMDRiYTRmYjlhMDJmMzQxNGUxOThiNzIwNzBjZWYwOTkxYTU3IiwidGFnIjoiIn0%3D; expires=Wed, 02 Jul 2025 12:57:03 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"445 characters\">ticketgol_session=eyJpdiI6Im5ubHo3b3VDeExJVTR3bXgzdlE0b0E9PSIsInZhbHVlIjoiYTRDSSsrSUVrckxyWjR1M2hjYUh6bTJXRU5DSDlZN3lSNnh3SWR4WDc3anNLajlYbU1DWFN1anRuNWQxWUgrWlVtNmU0YW9sZkE0VnkycGorQ1p4Q3pONnFWRTNMeVVvT2x2YTRkVXZaaWJGaFRQTHBpK2lxOEtWc0xwbXFoWjEiLCJtYWMiOiJhMmI4NjQ1ZWQ5YjdlOTg5YTkyMzczYmY2M2I2MjQyNTM1OWIzY2MyNjUwOTJkMTBhOTA2YmI5ZTI0MWVlYTViIiwidGFnIjoiIn0%3D; expires=Wed, 02 Jul 2025 12:57:03 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6Inp6eWZRVCtTclN4MU5JaFVjQUFUUEE9PSIsInZhbHVlIjoiUVJBZEszNWpsSnphRnJ2bmVVek1NOHZmdXJCdjBUZGdSMTYxN21JNVlPSnZmM0pxeStlY3RKc1AxOWtJZzE2Z1l5WFN5Nm1lOUowdEdUWjRhNkhESThNQ0tjYmw4djdCRVBLUi9GaU9KMGlDTW1iLzVOc29YejRZUHBRemdvSm0iLCJtYWMiOiJjNTA0ZDA0NDBmNmE1MmRlYjhlNjc0NWY3YzQzMDRiYTRmYjlhMDJmMzQxNGUxOThiNzIwNzBjZWYwOTkxYTU3IiwidGFnIjoiIn0%3D; expires=Wed, 02-Jul-2025 12:57:03 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"417 characters\">ticketgol_session=eyJpdiI6Im5ubHo3b3VDeExJVTR3bXgzdlE0b0E9PSIsInZhbHVlIjoiYTRDSSsrSUVrckxyWjR1M2hjYUh6bTJXRU5DSDlZN3lSNnh3SWR4WDc3anNLajlYbU1DWFN1anRuNWQxWUgrWlVtNmU0YW9sZkE0VnkycGorQ1p4Q3pONnFWRTNMeVVvT2x2YTRkVXZaaWJGaFRQTHBpK2lxOEtWc0xwbXFoWjEiLCJtYWMiOiJhMmI4NjQ1ZWQ5YjdlOTg5YTkyMzczYmY2M2I2MjQyNTM1OWIzY2MyNjUwOTJkMTBhOTA2YmI5ZTI0MWVlYTViIiwidGFnIjoiIn0%3D; expires=Wed, 02-Jul-2025 12:57:03 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-958729478\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0exJBrfNoQamXrSxBiH5KGFg0j8Wpa6NfLh3FPoe</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://ticketgol.test/my-account/orders</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$V1wR64GMQolTMApE3wzlyewL8P9bsAC3afZOgIhj9frmCuqzY..5e</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}