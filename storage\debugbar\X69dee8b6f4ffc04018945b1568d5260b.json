{"__meta": {"id": "X69dee8b6f4ffc04018945b1568d5260b", "datetime": "2025-07-02 11:12:25", "utime": **********.877588, "method": "GET", "uri": "/api/v1/events/event-ms-josie-wunsch-iii-6", "ip": "127.0.0.1"}, "php": {"version": "8.4.6", "interface": "cgi-fcgi"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.74646, "end": **********.877602, "duration": 0.13114213943481445, "duration_str": "131ms", "measures": [{"label": "Booting", "start": **********.74646, "relative_start": 0, "end": **********.79992, "relative_end": **********.79992, "duration": 0.053460121154785156, "duration_str": "53.46ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.799931, "relative_start": 0.05347108840942383, "end": **********.877606, "relative_end": 3.814697265625e-06, "duration": 0.07767486572265625, "duration_str": "77.67ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 7347904, "peak_usage_str": "7MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET api/v1/events/{slug}", "middleware": "api, set-locale", "controller": "App\\Http\\Controllers\\Api\\V1\\EventController@show", "namespace": null, "prefix": "api/v1/events", "where": [], "as": "api.events.show", "file": "<a href=\"phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FHttp%2FControllers%2FApi%2FV1%2FEventController.php&line=111\" onclick=\"\">app/Http/Controllers/Api/V1/EventController.php:111-124</a>"}, "queries": {"nb_statements": 26, "nb_visible_statements": 26, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.021769999999999994, "accumulated_duration_str": "21.77ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `sessions` where `id` = '0SPVygoyCBWzh93wW1oy7sqhnBQ7iBtsSbwRhUjx' limit 1", "type": "query", "params": [], "bindings": ["0SPVygoyCBWzh93wW1oy7sqhnBQ7iBtsSbwRhUjx"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 147}], "start": **********.803602, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:97", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=97", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "97"}, "connection": "ticketgol", "explain": null, "start_percent": 0, "width_percent": 2.71}, {"sql": "select * from `users` where `id` = 7 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "auth.session", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\sanctum\\src\\Http\\Middleware\\AuthenticateSession.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.805841, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ticketgol", "explain": null, "start_percent": 2.71, "width_percent": 3.078}, {"sql": "select `id`, `date`, `time`, `timezone`, `category`, `stadium_id`, `league_id`, `home_club_id`, `guest_club_id`, `is_feature_event`, (select MIN(price) from `tickets` where `event_id` = `events`.`id` and `tickets`.`deleted_at` is null) as `min_price`, (select MAX(price) from `tickets` where `event_id` = `events`.`id` and `tickets`.`deleted_at` is null) as `max_price` from `events` where exists (select * from `slugs` where `events`.`id` = `slugs`.`sluggable_id` and `slugs`.`sluggable_type` = 'App\\\\Models\\\\Event' and `slug` = 'event-ms-josie-wunsch-iii-6' and `locale` = 'en' and `locale` = 'en') and `is_published` = 1 and `events`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["App\\Models\\Event", "event-ms-josie-wunsch-iii-6", "en", "en", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, {"index": 17, "namespace": null, "name": "app/Services/EventService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\EventService.php", "line": 71}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Api/V1/EventController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\EventController.php", "line": 114}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 20, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.8101962, "duration": 0.00134, "duration_str": "1.34ms", "memory": 0, "memory_str": null, "filename": "EventRepository.php:222", "source": {"index": 16, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FEventRepository.php&line=222", "ajax": false, "filename": "EventRepository.php", "line": "222"}, "connection": "ticketgol", "explain": null, "start_percent": 5.788, "width_percent": 6.155}, {"sql": "select `event_id`, `locale`, `name`, `description`, `meta_title`, `meta_description`, `meta_keywords` from `event_translations` where `locale` = 'en' and `event_translations`.`event_id` in (6)", "type": "query", "params": [], "bindings": ["en"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, {"index": 22, "namespace": null, "name": "app/Services/EventService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\EventService.php", "line": 71}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/Api/V1/EventController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\EventController.php", "line": 114}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 25, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.813708, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "EventRepository.php:222", "source": {"index": 21, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FEventRepository.php&line=222", "ajax": false, "filename": "EventRepository.php", "line": "222"}, "connection": "ticketgol", "explain": null, "start_percent": 11.943, "width_percent": 4.685}, {"sql": "select `id`, `address_line_1`, `address_line_2`, `postcode`, `country_id` from `stadiums` where `stadiums`.`id` in (6) and `stadiums`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, {"index": 22, "namespace": null, "name": "app/Services/EventService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\EventService.php", "line": 71}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/Api/V1/EventController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\EventController.php", "line": 114}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 25, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.816569, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "EventRepository.php:222", "source": {"index": 21, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FEventRepository.php&line=222", "ajax": false, "filename": "EventRepository.php", "line": "222"}, "connection": "ticketgol", "explain": null, "start_percent": 16.628, "width_percent": 2.389}, {"sql": "select `stadium_id`, `locale`, `name` from `stadium_translations` where `locale` = 'en' and `stadium_translations`.`stadium_id` in (6)", "type": "query", "params": [], "bindings": ["en"], "hints": null, "show_copy": true, "backtrace": [{"index": 26, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, {"index": 27, "namespace": null, "name": "app/Services/EventService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\EventService.php", "line": 71}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/Api/V1/EventController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\EventController.php", "line": 114}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 30, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.818718, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "EventRepository.php:222", "source": {"index": 26, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FEventRepository.php&line=222", "ajax": false, "filename": "EventRepository.php", "line": "222"}, "connection": "ticketgol", "explain": null, "start_percent": 19.017, "width_percent": 3.445}, {"sql": "select `id`, `shortcode` from `countries` where `countries`.`id` in (112) and `countries`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 26, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, {"index": 27, "namespace": null, "name": "app/Services/EventService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\EventService.php", "line": 71}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/Api/V1/EventController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\EventController.php", "line": 114}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 30, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.821029, "duration": 0.0019299999999999999, "duration_str": "1.93ms", "memory": 0, "memory_str": null, "filename": "EventRepository.php:222", "source": {"index": 26, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FEventRepository.php&line=222", "ajax": false, "filename": "EventRepository.php", "line": "222"}, "connection": "ticketgol", "explain": null, "start_percent": 22.462, "width_percent": 8.865}, {"sql": "select `country_id`, `locale`, `name` from `country_translations` where `locale` = 'en' and `country_translations`.`country_id` in (112)", "type": "query", "params": [], "bindings": ["en"], "hints": null, "show_copy": true, "backtrace": [{"index": 31, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, {"index": 32, "namespace": null, "name": "app/Services/EventService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\EventService.php", "line": 71}, {"index": 33, "namespace": null, "name": "app/Http/Controllers/Api/V1/EventController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\EventController.php", "line": 114}, {"index": 34, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 35, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.825043, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "EventRepository.php:222", "source": {"index": 31, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FEventRepository.php&line=222", "ajax": false, "filename": "EventRepository.php", "line": "222"}, "connection": "ticketgol", "explain": null, "start_percent": 31.328, "width_percent": 3.95}, {"sql": "select `sluggable_id`, `slug`, `locale` from `slugs` where `locale` = 'en' and `slugs`.`sluggable_id` in (6) and `slugs`.`sluggable_type` = 'App\\\\Models\\\\Stadium'", "type": "query", "params": [], "bindings": ["en", "App\\Models\\Stadium"], "hints": null, "show_copy": true, "backtrace": [{"index": 26, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, {"index": 27, "namespace": null, "name": "app/Services/EventService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\EventService.php", "line": 71}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/Api/V1/EventController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\EventController.php", "line": 114}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 30, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.8280518, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "EventRepository.php:222", "source": {"index": 26, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FEventRepository.php&line=222", "ajax": false, "filename": "EventRepository.php", "line": "222"}, "connection": "ticketgol", "explain": null, "start_percent": 35.278, "width_percent": 2.71}, {"sql": "select `id` from `leagues` where `leagues`.`id` in (1) and `leagues`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, {"index": 22, "namespace": null, "name": "app/Services/EventService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\EventService.php", "line": 71}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/Api/V1/EventController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\EventController.php", "line": 114}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 25, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.8304288, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "EventRepository.php:222", "source": {"index": 21, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FEventRepository.php&line=222", "ajax": false, "filename": "EventRepository.php", "line": "222"}, "connection": "ticketgol", "explain": null, "start_percent": 37.988, "width_percent": 1.975}, {"sql": "select `league_id`, `locale`, `name` from `league_translations` where `locale` = 'en' and `league_translations`.`league_id` in (1)", "type": "query", "params": [], "bindings": ["en"], "hints": null, "show_copy": true, "backtrace": [{"index": 26, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, {"index": 27, "namespace": null, "name": "app/Services/EventService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\EventService.php", "line": 71}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/Api/V1/EventController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\EventController.php", "line": 114}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 30, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.832804, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "EventRepository.php:222", "source": {"index": 26, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FEventRepository.php&line=222", "ajax": false, "filename": "EventRepository.php", "line": "222"}, "connection": "ticketgol", "explain": null, "start_percent": 39.963, "width_percent": 2.435}, {"sql": "select `sluggable_id`, `slug`, `locale` from `slugs` where `locale` = 'en' and `slugs`.`sluggable_id` in (1) and `slugs`.`sluggable_type` = 'App\\\\Models\\\\League'", "type": "query", "params": [], "bindings": ["en", "App\\Models\\League"], "hints": null, "show_copy": true, "backtrace": [{"index": 26, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, {"index": 27, "namespace": null, "name": "app/Services/EventService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\EventService.php", "line": 71}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/Api/V1/EventController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\EventController.php", "line": 114}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 30, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.83458, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "EventRepository.php:222", "source": {"index": 26, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FEventRepository.php&line=222", "ajax": false, "filename": "EventRepository.php", "line": "222"}, "connection": "ticketgol", "explain": null, "start_percent": 42.398, "width_percent": 1.562}, {"sql": "select `id` from `clubs` where `clubs`.`id` in (2) and `clubs`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, {"index": 22, "namespace": null, "name": "app/Services/EventService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\EventService.php", "line": 71}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/Api/V1/EventController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\EventController.php", "line": 114}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 25, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.836309, "duration": 0.00113, "duration_str": "1.13ms", "memory": 0, "memory_str": null, "filename": "EventRepository.php:222", "source": {"index": 21, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FEventRepository.php&line=222", "ajax": false, "filename": "EventRepository.php", "line": "222"}, "connection": "ticketgol", "explain": null, "start_percent": 43.96, "width_percent": 5.191}, {"sql": "select `club_id`, `locale`, `name` from `club_translations` where `locale` = 'en' and `club_translations`.`club_id` in (2)", "type": "query", "params": [], "bindings": ["en"], "hints": null, "show_copy": true, "backtrace": [{"index": 26, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, {"index": 27, "namespace": null, "name": "app/Services/EventService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\EventService.php", "line": 71}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/Api/V1/EventController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\EventController.php", "line": 114}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 30, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.839252, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "EventRepository.php:222", "source": {"index": 26, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FEventRepository.php&line=222", "ajax": false, "filename": "EventRepository.php", "line": "222"}, "connection": "ticketgol", "explain": null, "start_percent": 49.15, "width_percent": 2.159}, {"sql": "select `sluggable_id`, `slug`, `locale` from `slugs` where `locale` = 'en' and `slugs`.`sluggable_id` in (2) and `slugs`.`sluggable_type` = 'App\\\\Models\\\\Club'", "type": "query", "params": [], "bindings": ["en", "App\\Models\\Club"], "hints": null, "show_copy": true, "backtrace": [{"index": 26, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, {"index": 27, "namespace": null, "name": "app/Services/EventService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\EventService.php", "line": 71}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/Api/V1/EventController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\EventController.php", "line": 114}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 30, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.841829, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "EventRepository.php:222", "source": {"index": 26, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FEventRepository.php&line=222", "ajax": false, "filename": "EventRepository.php", "line": "222"}, "connection": "ticketgol", "explain": null, "start_percent": 51.309, "width_percent": 2.848}, {"sql": "select `id` from `clubs` where `clubs`.`id` in (10) and `clubs`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, {"index": 22, "namespace": null, "name": "app/Services/EventService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\EventService.php", "line": 71}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/Api/V1/EventController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\EventController.php", "line": 114}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 25, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.844496, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "EventRepository.php:222", "source": {"index": 21, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FEventRepository.php&line=222", "ajax": false, "filename": "EventRepository.php", "line": "222"}, "connection": "ticketgol", "explain": null, "start_percent": 54.157, "width_percent": 2.205}, {"sql": "select `club_id`, `locale`, `name` from `club_translations` where `locale` = 'en' and `club_translations`.`club_id` in (10)", "type": "query", "params": [], "bindings": ["en"], "hints": null, "show_copy": true, "backtrace": [{"index": 26, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, {"index": 27, "namespace": null, "name": "app/Services/EventService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\EventService.php", "line": 71}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/Api/V1/EventController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\EventController.php", "line": 114}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 30, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.846345, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "EventRepository.php:222", "source": {"index": 26, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FEventRepository.php&line=222", "ajax": false, "filename": "EventRepository.php", "line": "222"}, "connection": "ticketgol", "explain": null, "start_percent": 56.362, "width_percent": 1.746}, {"sql": "select `sluggable_id`, `slug`, `locale` from `slugs` where `locale` = 'en' and `slugs`.`sluggable_id` in (10) and `slugs`.`sluggable_type` = 'App\\\\Models\\\\Club'", "type": "query", "params": [], "bindings": ["en", "App\\Models\\Club"], "hints": null, "show_copy": true, "backtrace": [{"index": 26, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, {"index": 27, "namespace": null, "name": "app/Services/EventService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\EventService.php", "line": 71}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/Api/V1/EventController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\EventController.php", "line": 114}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 30, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.848096, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "EventRepository.php:222", "source": {"index": 26, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FEventRepository.php&line=222", "ajax": false, "filename": "EventRepository.php", "line": "222"}, "connection": "ticketgol", "explain": null, "start_percent": 58.107, "width_percent": 2.618}, {"sql": "select `stadium_sectors`.`id`, `event_stadium_sectors`.`event_id` as `pivot_event_id`, `event_stadium_sectors`.`stadium_sector_id` as `pivot_stadium_sector_id`, `event_stadium_sectors`.`created_at` as `pivot_created_at`, `event_stadium_sectors`.`updated_at` as `pivot_updated_at` from `stadium_sectors` inner join `event_stadium_sectors` on `stadium_sectors`.`id` = `event_stadium_sectors`.`stadium_sector_id` where `event_stadium_sectors`.`event_id` in (6) and `stadium_sectors`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, {"index": 21, "namespace": null, "name": "app/Services/EventService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\EventService.php", "line": 71}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Api/V1/EventController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\EventController.php", "line": 114}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 24, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.850692, "duration": 0.00175, "duration_str": "1.75ms", "memory": 0, "memory_str": null, "filename": "EventRepository.php:222", "source": {"index": 20, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FEventRepository.php&line=222", "ajax": false, "filename": "EventRepository.php", "line": "222"}, "connection": "ticketgol", "explain": null, "start_percent": 60.726, "width_percent": 8.039}, {"sql": "select `stadium_sector_id`, `locale`, `name` from `stadium_sector_translations` where `locale` = 'en' and `stadium_sector_translations`.`stadium_sector_id` in (53, 57, 59)", "type": "query", "params": [], "bindings": ["en"], "hints": null, "show_copy": true, "backtrace": [{"index": 25, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, {"index": 26, "namespace": null, "name": "app/Services/EventService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\EventService.php", "line": 71}, {"index": 27, "namespace": null, "name": "app/Http/Controllers/Api/V1/EventController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\EventController.php", "line": 114}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 29, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.854856, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "EventRepository.php:222", "source": {"index": 25, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FEventRepository.php&line=222", "ajax": false, "filename": "EventRepository.php", "line": "222"}, "connection": "ticketgol", "explain": null, "start_percent": 68.764, "width_percent": 4.18}, {"sql": "select `restrictions`.`id`, `event_restrictions`.`event_id` as `pivot_event_id`, `event_restrictions`.`restriction_id` as `pivot_restriction_id` from `restrictions` inner join `event_restrictions` on `restrictions`.`id` = `event_restrictions`.`restriction_id` where `event_restrictions`.`event_id` in (6) and `restrictions`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, {"index": 21, "namespace": null, "name": "app/Services/EventService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\EventService.php", "line": 71}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Api/V1/EventController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\EventController.php", "line": 114}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 24, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.857897, "duration": 0.00267, "duration_str": "2.67ms", "memory": 0, "memory_str": null, "filename": "EventRepository.php:222", "source": {"index": 20, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FEventRepository.php&line=222", "ajax": false, "filename": "EventRepository.php", "line": "222"}, "connection": "ticketgol", "explain": null, "start_percent": 72.944, "width_percent": 12.265}, {"sql": "select `restriction_id`, `locale`, `name` from `restriction_translations` where `locale` = 'en' and `restriction_translations`.`restriction_id` in (2, 5, 6)", "type": "query", "params": [], "bindings": ["en"], "hints": null, "show_copy": true, "backtrace": [{"index": 25, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, {"index": 26, "namespace": null, "name": "app/Services/EventService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\EventService.php", "line": 71}, {"index": 27, "namespace": null, "name": "app/Http/Controllers/Api/V1/EventController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\EventController.php", "line": 114}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 29, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.863077, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "EventRepository.php:222", "source": {"index": 25, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FEventRepository.php&line=222", "ajax": false, "filename": "EventRepository.php", "line": "222"}, "connection": "ticketgol", "explain": null, "start_percent": 85.209, "width_percent": 4.318}, {"sql": "select * from `media` where `media`.`model_id` in (6) and `media`.`model_type` = 'App\\\\Models\\\\Event'", "type": "query", "params": [], "bindings": ["App\\Models\\Event"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, {"index": 22, "namespace": null, "name": "app/Services/EventService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\EventService.php", "line": 71}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/Api/V1/EventController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\EventController.php", "line": 114}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 25, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.865721, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "EventRepository.php:222", "source": {"index": 21, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FEventRepository.php&line=222", "ajax": false, "filename": "EventRepository.php", "line": "222"}, "connection": "ticketgol", "explain": null, "start_percent": 89.527, "width_percent": 1.883}, {"sql": "select `sluggable_id`, `slug`, `locale` from `slugs` where `locale` = 'en' and `slugs`.`sluggable_id` in (6) and `slugs`.`sluggable_type` = 'App\\\\Models\\\\Event'", "type": "query", "params": [], "bindings": ["en", "App\\Models\\Event"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, {"index": 22, "namespace": null, "name": "app/Services/EventService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\EventService.php", "line": 71}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/Api/V1/EventController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\EventController.php", "line": 114}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 25, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.867527, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "EventRepository.php:222", "source": {"index": 21, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FEventRepository.php&line=222", "ajax": false, "filename": "EventRepository.php", "line": "222"}, "connection": "ticketgol", "explain": null, "start_percent": 91.41, "width_percent": 1.7}, {"sql": "select tickets.id, tickets.quantity - COALESCE(SUM(r.quantity), 0) as available_quantity from `tickets` left join `ticket_reservations` as `r` on `r`.`ticket_id` = `tickets`.`id` and `status` in ('active', 'processing') where `tickets`.`event_id` = 6 and `tickets`.`event_id` is not null and `tickets`.`deleted_at` is null and `tickets`.`deleted_at` is null group by `tickets`.`id`, `tickets`.`quantity`", "type": "query", "params": [], "bindings": ["active", "processing", 6], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Services/EventService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\EventService.php", "line": 82}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Api/V1/EventController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\EventController.php", "line": 114}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 20, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}, {"index": 21, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingRoutingDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingRoutingDispatcher.php", "line": 35}], "start": **********.870154, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "EventService.php:82", "source": {"index": 17, "namespace": null, "name": "app/Services/EventService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\EventService.php", "line": 82}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FServices%2FEventService.php&line=82", "ajax": false, "filename": "EventService.php", "line": "82"}, "connection": "ticketgol", "explain": null, "start_percent": 93.11, "width_percent": 3.629}, {"sql": "update `sessions` set `payload` = 'YTo2OntzOjY6Il90b2tlbiI7czo0MDoiMGV4SkJyZk5vUWFtWHJTeEJpSDVLR0ZnMGo4V3BhNk5mTGgzRlBvZSI7czozOiJ1cmwiO2E6MDp7fXM6OToiX3ByZXZpb3VzIjthOjE6e3M6MzoidXJsIjtzOjI4OiJodHRwOi8vdGlja2V0Z29sLnRlc3QvZXZlbnRzIjt9czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319czo1MDoibG9naW5fd2ViXzU5YmEzNmFkZGMyYjJmOTQwMTU4MGYwMTRjN2Y1OGVhNGUzMDk4OWQiO2k6NztzOjE3OiJwYXNzd29yZF9oYXNoX3dlYiI7czo2MDoiJDJ5JDEyJFYxd1I2NEdNUW9sVE1BcEUzd3pseWV3TDhQOWJzQUMzYWZaT2dJaGo5ZnJtQ3VxelkuLjVlIjt9', `last_activity` = **********, `user_id` = 7, `ip_address` = '127.0.0.1', `user_agent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' where `id` = '0SPVygoyCBWzh93wW1oy7sqhnBQ7iBtsSbwRhUjx'", "type": "query", "params": [], "bindings": ["YTo2OntzOjY6Il90b2tlbiI7czo0MDoiMGV4SkJyZk5vUWFtWHJTeEJpSDVLR0ZnMGo4V3BhNk5mTGgzRlBvZSI7czozOiJ1cmwiO2E6MDp7fXM6OToiX3ByZXZpb3VzIjthOjE6e3M6MzoidXJsIjtzOjI4OiJodHRwOi8vdGlja2V0Z29sLnRlc3QvZXZlbnRzIjt9czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319czo1MDoibG9naW5fd2ViXzU5YmEzNmFkZGMyYjJmOTQwMTU4MGYwMTRjN2Y1OGVhNGUzMDk4OWQiO2k6NztzOjE3OiJwYXNzd29yZF9oYXNoX3dlYiI7czo2MDoiJDJ5JDEyJFYxd1I2NEdNUW9sVE1BcEUzd3pseWV3TDhQOWJzQUMzYWZaT2dJaGo5ZnJtQ3VxelkuLjVlIjt9", **********, 7, "127.0.0.1", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "0SPVygoyCBWzh93wW1oy7sqhnBQ7iBtsSbwRhUjx"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 173}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 140}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 175}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 245}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 130}], "start": **********.875967, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:173", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 173}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=173", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "173"}, "connection": "ticketgol", "explain": null, "start_percent": 96.739, "width_percent": 3.261}]}, "models": {"data": {"App\\Models\\Slug": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FSlug.php&line=1", "ajax": false, "filename": "Slug.php", "line": "?"}}, "App\\Models\\StadiumSector": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FStadiumSector.php&line=1", "ajax": false, "filename": "StadiumSector.php", "line": "?"}}, "App\\Models\\StadiumSectorTranslation": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FStadiumSectorTranslation.php&line=1", "ajax": false, "filename": "StadiumSectorTranslation.php", "line": "?"}}, "App\\Models\\Restriction": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FRestriction.php&line=1", "ajax": false, "filename": "Restriction.php", "line": "?"}}, "App\\Models\\RestrictionTranslation": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FRestrictionTranslation.php&line=1", "ajax": false, "filename": "RestrictionTranslation.php", "line": "?"}}, "App\\Models\\Club": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FClub.php&line=1", "ajax": false, "filename": "Club.php", "line": "?"}}, "App\\Models\\ClubTranslation": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FClubTranslation.php&line=1", "ajax": false, "filename": "ClubTranslation.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Event": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FEvent.php&line=1", "ajax": false, "filename": "Event.php", "line": "?"}}, "App\\Models\\EventTranslation": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FEventTranslation.php&line=1", "ajax": false, "filename": "EventTranslation.php", "line": "?"}}, "App\\Models\\Stadium": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FStadium.php&line=1", "ajax": false, "filename": "Stadium.php", "line": "?"}}, "App\\Models\\StadiumTranslation": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FStadiumTranslation.php&line=1", "ajax": false, "filename": "StadiumTranslation.php", "line": "?"}}, "App\\Models\\Country": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FCountry.php&line=1", "ajax": false, "filename": "Country.php", "line": "?"}}, "App\\Models\\CountryTranslation": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FCountryTranslation.php&line=1", "ajax": false, "filename": "CountryTranslation.php", "line": "?"}}, "App\\Models\\League": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FLeague.php&line=1", "ajax": false, "filename": "League.php", "line": "?"}}, "App\\Models\\LeagueTranslation": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FLeagueTranslation.php&line=1", "ajax": false, "filename": "LeagueTranslation.php", "line": "?"}}}, "count": 30, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "0exJBrfNoQamXrSxBiH5KGFg0j8Wpa6NfLh3FPoe", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://ticketgol.test/events\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "7", "password_hash_web": "$2y$12$V1wR64GMQolTMApE3wzlyewL8P9bsAC3afZOgIhj9frmCuqzY..5e"}, "request": {"telescope": "<a href=\"http://ticketgol.test/_debugbar/telescope/9f4b3bcb-fd7e-47c7-a262-61a86e84946d\" target=\"_blank\">View in Telescope</a>", "path_info": "/api/v1/events/event-ms-josie-wunsch-iii-6", "status_code": "<pre class=sf-dump id=sf-dump-1614856323 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1614856323\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1934605547 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1934605547\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-954698015 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-954698015\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-784752530 data-indent-pad=\"  \"><span class=sf-dump-note>array:11</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"853 characters\">ajs_anonymous_id=%2205cf8d6b-8a14-40b9-9d4f-c4ebd1620a6e%22; selected_locale=en; __stripe_mid=fd60eaa0-e90c-4914-bb27-ce58ed87fc6066be49; XSRF-TOKEN=eyJpdiI6ImNMa1pGeTlYbHQ0ZUU5NWl5cS9GYXc9PSIsInZhbHVlIjoiV1ptTTEwOFVnT3VPcUJjdlZQZkRzQ3NFRnJ6Zm1ieXdXbHJnVjRpcGZEQlN5UDc0ZUlpMEVCb2hpWlMrcjlJMy9IYXlkdGlXckFCaHc2bVJQQytEZUxmbWY4dTNZY25ud3RUZHYvK2dxQndZandlbzQ1ZTRmZUdBcGlWTXgrT2YiLCJtYWMiOiJiNmZhMDMzYWNkMDU2MjEyYjNmZjk5YTA4NjBmZGQyZjYyNTcwZjA3MTk2ZWMxOWVlZmFmYjIzNjRlMDJmZjc0IiwidGFnIjoiIn0%3D; ticketgol_session=eyJpdiI6IkRPa28yN2xUakh3N0luVnlaUmxZbVE9PSIsInZhbHVlIjoicm9neXFDSWRhM1JxUmloNnZWRi9LUXFwRlVXRDJXR1ppN1VOZ0RlL1FQWjFwYTY3WjRyYklGd2FObW9HNG5oQ1J5U094Z09xeHlneHRhdEJ5L2NObmpQUmZZM3N0UXNEUExVY0JPOXF2ZFhIbWc0MnNMcGlzbFBWODQzTTIrVFkiLCJtYWMiOiJjOWIxMTM4M2UzOWY1ZTdiOGNmMjVjMzEyODBkMzNiNmFjYzYyZmI5ZDEwMDVmOTE2ZTM2NmQ2ZTI2YWZiZmQ3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">en-GB,en;q=0.9,es;q=0.8,en-US;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"49 characters\">http://ticketgol.test/event-ms-josie-wunsch-iii-6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-locale</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6ImNMa1pGeTlYbHQ0ZUU5NWl5cS9GYXc9PSIsInZhbHVlIjoiV1ptTTEwOFVnT3VPcUJjdlZQZkRzQ3NFRnJ6Zm1ieXdXbHJnVjRpcGZEQlN5UDc0ZUlpMEVCb2hpWlMrcjlJMy9IYXlkdGlXckFCaHc2bVJQQytEZUxmbWY4dTNZY25ud3RUZHYvK2dxQndZandlbzQ1ZTRmZUdBcGlWTXgrT2YiLCJtYWMiOiJiNmZhMDMzYWNkMDU2MjEyYjNmZjk5YTA4NjBmZGQyZjYyNTcwZjA3MTk2ZWMxOWVlZmFmYjIzNjRlMDJmZjc0IiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">ticketgol.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-784752530\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1901340352 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>ajs_anonymous_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>selected_locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0exJBrfNoQamXrSxBiH5KGFg0j8Wpa6NfLh3FPoe</span>\"\n  \"<span class=sf-dump-key>ticketgol_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0SPVygoyCBWzh93wW1oy7sqhnBQ7iBtsSbwRhUjx</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1901340352\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1416058448 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 02 Jul 2025 11:12:25 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IlVDSkgvU3lLWkliNENSM01CUUEvbmc9PSIsInZhbHVlIjoiZ0puQTFoNXllMjZMY0dCa3hWaWZlWVBXMTYxdElhQ1JXZzFIaE9TQm1KcWF1Mi84WWZoMDFRY2ZUekVvZGdyU1AxQUhXQWlObHZ3VmZVb3luM0E4TmtmOCtHay9iNHM1N3ZOSHlsUTFVUWRoV2pJR2xqZzVnV1UxTi81cm1CM20iLCJtYWMiOiJjNjNiZWZjOGE3NWFlZmEzMmU0Zjc4OTczMzMwM2I1YjQ1M2QzNDY2N2Q0MGQ4NzA3NDMxNmFiZDBiMTMxOWExIiwidGFnIjoiIn0%3D; expires=Wed, 02 Jul 2025 13:12:25 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"445 characters\">ticketgol_session=eyJpdiI6IkVCTWFqMHZiZGxlYll5a2NxNkZ4MUE9PSIsInZhbHVlIjoiMjhrTFhSZTVoNVBGbDBiWkpyeDIxQXBUOWJ1aDlEQzc2SUhDZk5aampaSXVKemh6WnhFY3lYWGhsaEpvb0Urb0hIK1RyNGM1WHNBZjVHUXBpY1ZLUlNPQlYzR1gzeXAvaVBESFFycWRsSldad3ZTakFqWXVjWURjdndqK0dubEIiLCJtYWMiOiJkNWQyOTFkOTg2M2NhOGM1YTcwNWM2OTQzZTJmYWZkZTUzMWY3MWI3ODE2Y2ZmMWU2ODM2ODQ1NGRhM2YyY2Y1IiwidGFnIjoiIn0%3D; expires=Wed, 02 Jul 2025 13:12:25 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IlVDSkgvU3lLWkliNENSM01CUUEvbmc9PSIsInZhbHVlIjoiZ0puQTFoNXllMjZMY0dCa3hWaWZlWVBXMTYxdElhQ1JXZzFIaE9TQm1KcWF1Mi84WWZoMDFRY2ZUekVvZGdyU1AxQUhXQWlObHZ3VmZVb3luM0E4TmtmOCtHay9iNHM1N3ZOSHlsUTFVUWRoV2pJR2xqZzVnV1UxTi81cm1CM20iLCJtYWMiOiJjNjNiZWZjOGE3NWFlZmEzMmU0Zjc4OTczMzMwM2I1YjQ1M2QzNDY2N2Q0MGQ4NzA3NDMxNmFiZDBiMTMxOWExIiwidGFnIjoiIn0%3D; expires=Wed, 02-Jul-2025 13:12:25 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"417 characters\">ticketgol_session=eyJpdiI6IkVCTWFqMHZiZGxlYll5a2NxNkZ4MUE9PSIsInZhbHVlIjoiMjhrTFhSZTVoNVBGbDBiWkpyeDIxQXBUOWJ1aDlEQzc2SUhDZk5aampaSXVKemh6WnhFY3lYWGhsaEpvb0Urb0hIK1RyNGM1WHNBZjVHUXBpY1ZLUlNPQlYzR1gzeXAvaVBESFFycWRsSldad3ZTakFqWXVjWURjdndqK0dubEIiLCJtYWMiOiJkNWQyOTFkOTg2M2NhOGM1YTcwNWM2OTQzZTJmYWZkZTUzMWY3MWI3ODE2Y2ZmMWU2ODM2ODQ1NGRhM2YyY2Y1IiwidGFnIjoiIn0%3D; expires=Wed, 02-Jul-2025 13:12:25 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1416058448\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-176836258 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0exJBrfNoQamXrSxBiH5KGFg0j8Wpa6NfLh3FPoe</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"28 characters\">http://ticketgol.test/events</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$V1wR64GMQolTMApE3wzlyewL8P9bsAC3afZOgIhj9frmCuqzY..5e</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-176836258\", {\"maxDepth\":0})</script>\n"}}