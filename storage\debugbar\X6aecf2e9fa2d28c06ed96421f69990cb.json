{"__meta": {"id": "X6aecf2e9fa2d28c06ed96421f69990cb", "datetime": "2025-07-03 08:35:36", "utime": **********.709945, "method": "POST", "uri": "/api/v1/tickets", "ip": "127.0.0.1"}, "php": {"version": "8.4.6", "interface": "cgi-fcgi"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.580462, "end": **********.709959, "duration": 0.12949705123901367, "duration_str": "129ms", "measures": [{"label": "Booting", "start": **********.580462, "relative_start": 0, "end": **********.651544, "relative_end": **********.651544, "duration": 0.07108211517333984, "duration_str": "71.08ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.651555, "relative_start": 0.07109308242797852, "end": **********.709962, "relative_end": 2.86102294921875e-06, "duration": 0.058406829833984375, "duration_str": "58.41ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 6222392, "peak_usage_str": "6MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST api/v1/tickets", "middleware": "api, set-locale", "controller": "App\\Http\\Controllers\\Api\\V1\\TicketController@index", "namespace": null, "prefix": "api/v1/tickets", "where": [], "as": "api.tickets.index", "file": "<a href=\"phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FHttp%2FControllers%2FApi%2FV1%2FTicketController.php&line=29\" onclick=\"\">app/Http/Controllers/Api/V1/TicketController.php:29-43</a>"}, "queries": {"nb_statements": 6, "nb_visible_statements": 6, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.020589999999999997, "accumulated_duration_str": "20.59ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `sessions` where `id` = 'N75WHSWBQig1s7jRbuIicGDoQiErAj8tdVS6z3Wn' limit 1", "type": "query", "params": [], "bindings": ["N75WHSWBQig1s7jRbuIicGDoQiErAj8tdVS6z3Wn"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 147}], "start": **********.655338, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:97", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=97", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "97"}, "connection": "ticketgol", "explain": null, "start_percent": 0, "width_percent": 3.983}, {"sql": "select * from `users` where `id` = 7 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "auth.session", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\sanctum\\src\\Http\\Middleware\\AuthenticateSession.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.6587012, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ticketgol", "explain": null, "start_percent": 3.983, "width_percent": 3.934}, {"sql": "select count(*) as aggregate from `events` where `id` = 7", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 982}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 953}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 682}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 477}], "start": **********.673953, "duration": 0.00298, "duration_str": "2.98ms", "memory": 0, "memory_str": null, "filename": "DatabasePresenceVerifier.php:54", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FValidation%2FDatabasePresenceVerifier.php&line=54", "ajax": false, "filename": "DatabasePresenceVerifier.php", "line": "54"}, "connection": "ticketgol", "explain": null, "start_percent": 7.916, "width_percent": 14.473}, {"sql": "select count(*) as aggregate from (select `tickets`.`id`, `price`, `quantity`, `ticket_type`, `quantity_split_type`, `sct`.`name` as `sector_name`, (tickets.quantity - COALESCE(r.total_reserved_qty, 0)) as remain_qty from `tickets` left join `stadium_sector_translations` as `sct` on `tickets`.`sector_id` = `sct`.`stadium_sector_id` left join (select `ticket_id`, SUM(quantity) as total_reserved_qty from `ticket_reservations` where `status` in ('active', 'processing') group by `ticket_id`) as `r` on `tickets`.`id` = `r`.`ticket_id` where `sct`.`locale` = 'en' and `event_id` = 7 and `price` >= '273.00' and `price` <= '812.42' and `tickets`.`deleted_at` is null having `remain_qty` > 0) as `aggregate_table`", "type": "query", "params": [], "bindings": ["active", "processing", "en", 7, "273.00", "812.42", 0], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/TicketService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\TicketService.php", "line": 47}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/V1/TicketController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\TicketController.php", "line": 34}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 19, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}, {"index": 20, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingRoutingDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingRoutingDispatcher.php", "line": 35}], "start": **********.685287, "duration": 0.00934, "duration_str": "9.34ms", "memory": 0, "memory_str": null, "filename": "TicketService.php:47", "source": {"index": 16, "namespace": null, "name": "app/Services/TicketService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\TicketService.php", "line": 47}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FServices%2FTicketService.php&line=47", "ajax": false, "filename": "TicketService.php", "line": "47"}, "connection": "ticketgol", "explain": null, "start_percent": 22.39, "width_percent": 45.362}, {"sql": "select `tickets`.`id`, `price`, `quantity`, `ticket_type`, `quantity_split_type`, `sct`.`name` as `sector_name`, (tickets.quantity - COALESCE(r.total_reserved_qty, 0)) as remain_qty from `tickets` left join `stadium_sector_translations` as `sct` on `tickets`.`sector_id` = `sct`.`stadium_sector_id` left join (select `ticket_id`, SUM(quantity) as total_reserved_qty from `ticket_reservations` where `status` in ('active', 'processing') group by `ticket_id`) as `r` on `tickets`.`id` = `r`.`ticket_id` where `sct`.`locale` = 'en' and `event_id` = 7 and `price` >= '273.00' and `price` <= '812.42' and `tickets`.`deleted_at` is null having `remain_qty` > 0 order by `remain_qty` asc limit 10 offset 0", "type": "query", "params": [], "bindings": ["active", "processing", "en", 7, "273.00", "812.42", 0], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/TicketService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\TicketService.php", "line": 47}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/V1/TicketController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\TicketController.php", "line": 34}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 19, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}, {"index": 20, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingRoutingDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingRoutingDispatcher.php", "line": 35}], "start": **********.697393, "duration": 0.0019299999999999999, "duration_str": "1.93ms", "memory": 0, "memory_str": null, "filename": "TicketService.php:47", "source": {"index": 16, "namespace": null, "name": "app/Services/TicketService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\TicketService.php", "line": 47}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FServices%2FTicketService.php&line=47", "ajax": false, "filename": "TicketService.php", "line": "47"}, "connection": "ticketgol", "explain": null, "start_percent": 67.751, "width_percent": 9.373}, {"sql": "update `sessions` set `payload` = 'YTo1OntzOjY6Il90b2tlbiI7czo0MDoiS0xFOGV2OWtGMGp0VkVVUzQzaXViM3hnRVFYUmJtV09KUnFqNVoxSiI7czo2OiJfZmxhc2giO2E6Mjp7czozOiJuZXciO2E6MDp7fXM6Mzoib2xkIjthOjA6e319czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6Mzk6Imh0dHA6Ly90aWNrZXRnb2wudGVzdC9teS1hY2NvdW50L29yZGVycyI7fXM6NTA6ImxvZ2luX3dlYl81OWJhMzZhZGRjMmIyZjk0MDE1ODBmMDE0YzdmNThlYTRlMzA5ODlkIjtpOjc7czoxNzoicGFzc3dvcmRfaGFzaF93ZWIiO3M6NjA6IiQyeSQxMiRWMXdSNjRHTVFvbFRNQXBFM3d6bHlld0w4UDlic0FDM2FmWk9nSWhqOWZybUN1cXpZLi41ZSI7fQ==', `last_activity` = **********, `user_id` = 7, `ip_address` = '127.0.0.1', `user_agent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' where `id` = 'N75WHSWBQig1s7jRbuIicGDoQiErAj8tdVS6z3Wn'", "type": "query", "params": [], "bindings": ["YTo1OntzOjY6Il90b2tlbiI7czo0MDoiS0xFOGV2OWtGMGp0VkVVUzQzaXViM3hnRVFYUmJtV09KUnFqNVoxSiI7czo2OiJfZmxhc2giO2E6Mjp7czozOiJuZXciO2E6MDp7fXM6Mzoib2xkIjthOjA6e319czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6Mzk6Imh0dHA6Ly90aWNrZXRnb2wudGVzdC9teS1hY2NvdW50L29yZGVycyI7fXM6NTA6ImxvZ2luX3dlYl81OWJhMzZhZGRjMmIyZjk0MDE1ODBmMDE0YzdmNThlYTRlMzA5ODlkIjtpOjc7czoxNzoicGFzc3dvcmRfaGFzaF93ZWIiO3M6NjA6IiQyeSQxMiRWMXdSNjRHTVFvbFRNQXBFM3d6bHlld0w4UDlic0FDM2FmWk9nSWhqOWZybUN1cXpZLi41ZSI7fQ==", **********, 7, "127.0.0.1", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "N75WHSWBQig1s7jRbuIicGDoQiErAj8tdVS6z3Wn"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 173}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 140}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 175}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 245}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 130}], "start": **********.704372, "duration": 0.00471, "duration_str": "4.71ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:173", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 173}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=173", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "173"}, "connection": "ticketgol", "explain": null, "start_percent": 77.125, "width_percent": 22.875}]}, "models": {"data": {"App\\Models\\Ticket": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FTicket.php&line=1", "ajax": false, "filename": "Ticket.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 5, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "KLE8ev9kF0jtVEUS43iub3xgEQXRbmWOJRqj5Z1J", "_flash": "array:2 [\n  \"new\" => []\n  \"old\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://ticketgol.test/my-account/orders\"\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "7", "password_hash_web": "$2y$12$V1wR64GMQolTMApE3wzlyewL8P9bsAC3afZOgIhj9frmCuqzY..5e"}, "request": {"telescope": "<a href=\"http://ticketgol.test/_debugbar/telescope/9f4d06b2-ad1b-4938-bd2b-afb68c8131bc\" target=\"_blank\">View in Telescope</a>", "path_info": "/api/v1/tickets", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>priceRange</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">273.00</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"6 characters\">812.42</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sector</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>ticketType</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>sort</span>\" => \"<span class=sf-dump-str title=\"14 characters\">remain_qty_asc</span>\"\n  \"<span class=sf-dump-key>eventId</span>\" => <span class=sf-dump-num>7</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1425568804 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"853 characters\">ajs_anonymous_id=%2205cf8d6b-8a14-40b9-9d4f-c4ebd1620a6e%22; selected_locale=en; __stripe_mid=fd60eaa0-e90c-4914-bb27-ce58ed87fc6066be49; XSRF-TOKEN=eyJpdiI6Imt3T0JaKzV2NUlFazFKb3l6MUs4T3c9PSIsInZhbHVlIjoiT1FUN2lwaE1CNUdoOVFndEJ5MFdjTW5DblpCWlZiRjJyZ3FUYnJGRkt3Y3ZuRmU3S2pIRFdxL1lGL3ZnbFV5QnVJN2FoRHNIV0ZGaVZxYWtPOU5hczlGT0ZRQzI3V2EyTUtJUllFVEZQUFRrQ2IwM3ZVZTZuYmVXcGpSNjJ1MGkiLCJtYWMiOiJjNjBhYzk5ZTBjOWFiOTk4YjUwNTU3MTUzNmI0MTg1MmVlZWYyYzJkZjk0Y2YwOWJhYTMyYjU4NzA5ZTZiYTM2IiwidGFnIjoiIn0%3D; ticketgol_session=eyJpdiI6Ik9UWUJjdmsrUUFCYVpTcFJmdVkwU0E9PSIsInZhbHVlIjoiOTB1WlRLMkxvNXg0WStkd1pFeVJBamFUbVpuVkdBSUs3S2JsOGhkRERnWE5WcGVjUnpmemhvUjVibW52TVhQaEg2V0pDbFlFaTY0UTFsVnVwekxHWXFpM0VIRlNHb25nTk9JMHdwQjhuQVZQZ1FNbzJiQ1ptSGtvZHpBaTJ5MXEiLCJtYWMiOiI1YjcwMjU1Y2QyMTU1MmVkM2YwYTdiZDE4ODU5ZGFiNDJiN2Q2YTgzZDZmMTMwNTVkZTRkZDQyZGJjMmI3OWQ0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">en-GB,en;q=0.9,es;q=0.8,en-US;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"51 characters\">http://ticketgol.test/event-jaunita-runolfsdottir-7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://ticketgol.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-locale</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6Imt3T0JaKzV2NUlFazFKb3l6MUs4T3c9PSIsInZhbHVlIjoiT1FUN2lwaE1CNUdoOVFndEJ5MFdjTW5DblpCWlZiRjJyZ3FUYnJGRkt3Y3ZuRmU3S2pIRFdxL1lGL3ZnbFV5QnVJN2FoRHNIV0ZGaVZxYWtPOU5hczlGT0ZRQzI3V2EyTUtJUllFVEZQUFRrQ2IwM3ZVZTZuYmVXcGpSNjJ1MGkiLCJtYWMiOiJjNjBhYzk5ZTBjOWFiOTk4YjUwNTU3MTUzNmI0MTg1MmVlZWYyYzJkZjk0Y2YwOWJhYTMyYjU4NzA5ZTZiYTM2IiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">112</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">ticketgol.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1425568804\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-244989968 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>ajs_anonymous_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>selected_locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">KLE8ev9kF0jtVEUS43iub3xgEQXRbmWOJRqj5Z1J</span>\"\n  \"<span class=sf-dump-key>ticketgol_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">N75WHSWBQig1s7jRbuIicGDoQiErAj8tdVS6z3Wn</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-244989968\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1817701872 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 03 Jul 2025 08:35:36 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6InJCZ243Z2MvbXFraGExOXEveWpEbWc9PSIsInZhbHVlIjoiMXlQU1I5QkVRU3M0K09Bald4NXp6Nkh1dGdEdWNMb0FsSXU5YTFzZ3NVMUNsWUlPbUtCQjJ3RlJ5MjhtcFJCOFlZbjdmR1NkdVZEaitaaTAwUjd1YjdVMmUzQU9ZK3VzNndPNThUbVZkWVVmYXVVd1Q2Uld4ZDdxQnozN2cwamQiLCJtYWMiOiI4OWZkMzI1N2NkNGZkNTYxYWRjZjExZjdlODI2Y2ZkNTY4YmI5OGMzYjllNzM2MjdjOGQyOWVlMjQ2MDMwZTZhIiwidGFnIjoiIn0%3D; expires=Thu, 03 Jul 2025 10:35:36 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"445 characters\">ticketgol_session=eyJpdiI6InFzRHJZakFzdmp0UnVmeTBnKzlXTnc9PSIsInZhbHVlIjoiZjQ3aDg4NTZ3NkxpazExeFJpMVlNVzY0R0xsakxpNXk4b1dab0x4dXNUc3lZUGpKZTVxelhzenFmZzd6OXcvOVA3clg1WDZ1dTBXMkN3YWs3a3VjMStublNEY05PYWttMS9TcXNTVyszSi9SMGg4ZHNuRG9INEh4SnRpWW8xRG8iLCJtYWMiOiI0MDRkOGUzZjY4YzI0NmEwZDhiMjRjNzk0NjI4MDIxMzA4YjA1ZjEzZTEwNTNkMWE5ZDhjMjVmOWFmYmFiZDM1IiwidGFnIjoiIn0%3D; expires=Thu, 03 Jul 2025 10:35:36 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6InJCZ243Z2MvbXFraGExOXEveWpEbWc9PSIsInZhbHVlIjoiMXlQU1I5QkVRU3M0K09Bald4NXp6Nkh1dGdEdWNMb0FsSXU5YTFzZ3NVMUNsWUlPbUtCQjJ3RlJ5MjhtcFJCOFlZbjdmR1NkdVZEaitaaTAwUjd1YjdVMmUzQU9ZK3VzNndPNThUbVZkWVVmYXVVd1Q2Uld4ZDdxQnozN2cwamQiLCJtYWMiOiI4OWZkMzI1N2NkNGZkNTYxYWRjZjExZjdlODI2Y2ZkNTY4YmI5OGMzYjllNzM2MjdjOGQyOWVlMjQ2MDMwZTZhIiwidGFnIjoiIn0%3D; expires=Thu, 03-Jul-2025 10:35:36 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"417 characters\">ticketgol_session=eyJpdiI6InFzRHJZakFzdmp0UnVmeTBnKzlXTnc9PSIsInZhbHVlIjoiZjQ3aDg4NTZ3NkxpazExeFJpMVlNVzY0R0xsakxpNXk4b1dab0x4dXNUc3lZUGpKZTVxelhzenFmZzd6OXcvOVA3clg1WDZ1dTBXMkN3YWs3a3VjMStublNEY05PYWttMS9TcXNTVyszSi9SMGg4ZHNuRG9INEh4SnRpWW8xRG8iLCJtYWMiOiI0MDRkOGUzZjY4YzI0NmEwZDhiMjRjNzk0NjI4MDIxMzA4YjA1ZjEzZTEwNTNkMWE5ZDhjMjVmOWFmYmFiZDM1IiwidGFnIjoiIn0%3D; expires=Thu, 03-Jul-2025 10:35:36 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1817701872\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">KLE8ev9kF0jtVEUS43iub3xgEQXRbmWOJRqj5Z1J</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>new</span>\" => []\n    \"<span class=sf-dump-key>old</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://ticketgol.test/my-account/orders</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$V1wR64GMQolTMApE3wzlyewL8P9bsAC3afZOgIhj9frmCuqzY..5e</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}