{"__meta": {"id": "X7d7c2acef65db3a5bd528929ffc7493f", "datetime": "2025-07-02 19:15:27", "utime": **********.939515, "method": "GET", "uri": "/api/v1/home", "ip": "127.0.0.1"}, "php": {"version": "8.4.6", "interface": "cgi-fcgi"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.80065, "end": **********.939531, "duration": 0.13888120651245117, "duration_str": "139ms", "measures": [{"label": "Booting", "start": **********.80065, "relative_start": 0, "end": **********.865165, "relative_end": **********.865165, "duration": 0.0645151138305664, "duration_str": "64.52ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.865183, "relative_start": 0.06453323364257812, "end": **********.939534, "relative_end": 2.86102294921875e-06, "duration": 0.07435083389282227, "duration_str": "74.35ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 6771840, "peak_usage_str": "6MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET api/v1/home", "middleware": "api, set-locale", "controller": "App\\Http\\Controllers\\Api\\V1\\HomeController@index", "namespace": null, "prefix": "api/v1/home", "where": [], "as": "api.home.index", "file": "<a href=\"phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FHttp%2FControllers%2FApi%2FV1%2FHomeController.php&line=24\" onclick=\"\">app/Http/Controllers/Api/V1/HomeController.php:24-40</a>"}, "queries": {"nb_statements": 18, "nb_visible_statements": 18, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.019020000000000002, "accumulated_duration_str": "19.02ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `sessions` where `id` = 'Nmg37lihcVT6zZMhXS82xXUo71zj9zMJaGTKWQUz' limit 1", "type": "query", "params": [], "bindings": ["Nmg37lihcVT6zZMhXS82xXUo71zj9zMJaGTKWQUz"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 147}], "start": **********.871392, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:97", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=97", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "97"}, "connection": "ticketgol", "explain": null, "start_percent": 0, "width_percent": 3.838}, {"sql": "select count(*) as aggregate from (select `events`.`id`, `date`, `events`.`stadium_id`, `events`.`league_id`, `et`.`name` as `event_name`, `st`.`name` as `stadium_name`, `lt`.`name` as `league_name`, (select MIN(price) from `tickets` where `event_id` = `events`.`id` and `tickets`.`deleted_at` is null) as `min_price` from `events` left join `event_translations` as `et` on `events`.`id` = `et`.`event_id` and `et`.`locale` = 'en' left join `stadium_translations` as `st` on `events`.`stadium_id` = `st`.`stadium_id` and `st`.`locale` = 'en' left join `league_translations` as `lt` on `events`.`league_id` = `lt`.`league_id` and `lt`.`locale` = 'en' left join `club_translations` as `hc` on `events`.`home_club_id` = `hc`.`club_id` and `hc`.`locale` = 'en' left join `club_translations` as `gc` on `events`.`guest_club_id` = `gc`.`club_id` and `gc`.`locale` = 'en' where `is_published` = 1 and `date` >= '2025-07-02' and `is_feature_event` = 0 and `events`.`deleted_at` is null having `min_price` is not null) as `aggregate_table`", "type": "query", "params": [], "bindings": ["en", "en", "en", "en", "en", 1, "2025-07-02", 0], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/HomeService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\HomeService.php", "line": 35}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/V1/HomeController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\HomeController.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 19, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}, {"index": 20, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingRoutingDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingRoutingDispatcher.php", "line": 35}], "start": **********.878782, "duration": 0.00496, "duration_str": "4.96ms", "memory": 0, "memory_str": null, "filename": "HomeService.php:35", "source": {"index": 16, "namespace": null, "name": "app/Services/HomeService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\HomeService.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FServices%2FHomeService.php&line=35", "ajax": false, "filename": "HomeService.php", "line": "35"}, "connection": "ticketgol", "explain": null, "start_percent": 3.838, "width_percent": 26.078}, {"sql": "select `events`.`id`, `date`, `events`.`stadium_id`, `events`.`league_id`, `et`.`name` as `event_name`, `st`.`name` as `stadium_name`, `lt`.`name` as `league_name`, (select MIN(price) from `tickets` where `event_id` = `events`.`id` and `tickets`.`deleted_at` is null) as `min_price` from `events` left join `event_translations` as `et` on `events`.`id` = `et`.`event_id` and `et`.`locale` = 'en' left join `stadium_translations` as `st` on `events`.`stadium_id` = `st`.`stadium_id` and `st`.`locale` = 'en' left join `league_translations` as `lt` on `events`.`league_id` = `lt`.`league_id` and `lt`.`locale` = 'en' left join `club_translations` as `hc` on `events`.`home_club_id` = `hc`.`club_id` and `hc`.`locale` = 'en' left join `club_translations` as `gc` on `events`.`guest_club_id` = `gc`.`club_id` and `gc`.`locale` = 'en' where `is_published` = 1 and `date` >= '2025-07-02' and `is_feature_event` = 0 and `events`.`deleted_at` is null having `min_price` is not null order by `date` asc limit 8 offset 0", "type": "query", "params": [], "bindings": ["en", "en", "en", "en", "en", 1, "2025-07-02", 0], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/HomeService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\HomeService.php", "line": 35}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/V1/HomeController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\HomeController.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 19, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}, {"index": 20, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingRoutingDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingRoutingDispatcher.php", "line": 35}], "start": **********.885771, "duration": 0.00172, "duration_str": "1.72ms", "memory": 0, "memory_str": null, "filename": "HomeService.php:35", "source": {"index": 16, "namespace": null, "name": "app/Services/HomeService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\HomeService.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FServices%2FHomeService.php&line=35", "ajax": false, "filename": "HomeService.php", "line": "35"}, "connection": "ticketgol", "explain": null, "start_percent": 29.916, "width_percent": 9.043}, {"sql": "select * from `media` where `media`.`model_id` in (4, 5, 6, 7, 9) and `media`.`model_type` = 'App\\\\Models\\\\Event'", "type": "query", "params": [], "bindings": ["App\\Models\\Event"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Services/HomeService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\HomeService.php", "line": 35}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Api/V1/HomeController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\HomeController.php", "line": 27}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 24, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}, {"index": 25, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingRoutingDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingRoutingDispatcher.php", "line": 35}], "start": **********.8902469, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "HomeService.php:35", "source": {"index": 21, "namespace": null, "name": "app/Services/HomeService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\HomeService.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FServices%2FHomeService.php&line=35", "ajax": false, "filename": "HomeService.php", "line": "35"}, "connection": "ticketgol", "explain": null, "start_percent": 38.959, "width_percent": 4.154}, {"sql": "select `id` from `stadiums` where `stadiums`.`id` in (1, 5, 6) and `stadiums`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Services/HomeService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\HomeService.php", "line": 35}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Api/V1/HomeController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\HomeController.php", "line": 27}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 24, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}, {"index": 25, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingRoutingDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingRoutingDispatcher.php", "line": 35}], "start": **********.893574, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "HomeService.php:35", "source": {"index": 21, "namespace": null, "name": "app/Services/HomeService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\HomeService.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FServices%2FHomeService.php&line=35", "ajax": false, "filename": "HomeService.php", "line": "35"}, "connection": "ticketgol", "explain": null, "start_percent": 43.113, "width_percent": 4.311}, {"sql": "select `sluggable_id`, `slug`, `locale` from `slugs` where `locale` = 'en' and `slugs`.`sluggable_id` in (1, 5, 6) and `slugs`.`sluggable_type` = 'App\\\\Models\\\\Stadium'", "type": "query", "params": [], "bindings": ["en", "App\\Models\\Stadium"], "hints": null, "show_copy": true, "backtrace": [{"index": 26, "namespace": null, "name": "app/Services/HomeService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\HomeService.php", "line": 35}, {"index": 27, "namespace": null, "name": "app/Http/Controllers/Api/V1/HomeController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\HomeController.php", "line": 27}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 29, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}, {"index": 30, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingRoutingDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingRoutingDispatcher.php", "line": 35}], "start": **********.897223, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "HomeService.php:35", "source": {"index": 26, "namespace": null, "name": "app/Services/HomeService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\HomeService.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FServices%2FHomeService.php&line=35", "ajax": false, "filename": "HomeService.php", "line": "35"}, "connection": "ticketgol", "explain": null, "start_percent": 47.424, "width_percent": 4.837}, {"sql": "select `id` from `leagues` where `leagues`.`id` in (1, 2, 3, 4, 7) and `leagues`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Services/HomeService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\HomeService.php", "line": 35}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Api/V1/HomeController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\HomeController.php", "line": 27}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 24, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}, {"index": 25, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingRoutingDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingRoutingDispatcher.php", "line": 35}], "start": **********.900586, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "HomeService.php:35", "source": {"index": 21, "namespace": null, "name": "app/Services/HomeService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\HomeService.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FServices%2FHomeService.php&line=35", "ajax": false, "filename": "HomeService.php", "line": "35"}, "connection": "ticketgol", "explain": null, "start_percent": 52.261, "width_percent": 2.839}, {"sql": "select `sluggable_id`, `slug`, `locale` from `slugs` where `locale` = 'en' and `slugs`.`sluggable_id` in (1, 2, 3, 4, 7) and `slugs`.`sluggable_type` = 'App\\\\Models\\\\League'", "type": "query", "params": [], "bindings": ["en", "App\\Models\\League"], "hints": null, "show_copy": true, "backtrace": [{"index": 26, "namespace": null, "name": "app/Services/HomeService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\HomeService.php", "line": 35}, {"index": 27, "namespace": null, "name": "app/Http/Controllers/Api/V1/HomeController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\HomeController.php", "line": 27}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 29, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}, {"index": 30, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingRoutingDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingRoutingDispatcher.php", "line": 35}], "start": **********.903147, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "HomeService.php:35", "source": {"index": 26, "namespace": null, "name": "app/Services/HomeService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\HomeService.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FServices%2FHomeService.php&line=35", "ajax": false, "filename": "HomeService.php", "line": "35"}, "connection": "ticketgol", "explain": null, "start_percent": 55.1, "width_percent": 3.575}, {"sql": "select `sluggable_id`, `slug`, `locale` from `slugs` where `locale` = 'en' and `slugs`.`sluggable_id` in (4, 5, 6, 7, 9) and `slugs`.`sluggable_type` = 'App\\\\Models\\\\Event'", "type": "query", "params": [], "bindings": ["en", "App\\Models\\Event"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Services/HomeService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\HomeService.php", "line": 35}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Api/V1/HomeController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\HomeController.php", "line": 27}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 24, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}, {"index": 25, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingRoutingDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingRoutingDispatcher.php", "line": 35}], "start": **********.9054549, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "HomeService.php:35", "source": {"index": 21, "namespace": null, "name": "app/Services/HomeService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\HomeService.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FServices%2FHomeService.php&line=35", "ajax": false, "filename": "HomeService.php", "line": "35"}, "connection": "ticketgol", "explain": null, "start_percent": 58.675, "width_percent": 3.102}, {"sql": "select count(*) as aggregate from (select `events`.`id`, `date`, `events`.`stadium_id`, `events`.`league_id`, `et`.`name` as `event_name`, `st`.`name` as `stadium_name`, `lt`.`name` as `league_name`, (select MIN(price) from `tickets` where `event_id` = `events`.`id` and `tickets`.`deleted_at` is null) as `min_price` from `events` left join `event_translations` as `et` on `events`.`id` = `et`.`event_id` and `et`.`locale` = 'en' left join `stadium_translations` as `st` on `events`.`stadium_id` = `st`.`stadium_id` and `st`.`locale` = 'en' left join `league_translations` as `lt` on `events`.`league_id` = `lt`.`league_id` and `lt`.`locale` = 'en' left join `club_translations` as `hc` on `events`.`home_club_id` = `hc`.`club_id` and `hc`.`locale` = 'en' left join `club_translations` as `gc` on `events`.`guest_club_id` = `gc`.`club_id` and `gc`.`locale` = 'en' where `is_published` = 1 and `date` >= '2025-07-02' and `is_feature_event` = 1 and `events`.`deleted_at` is null having `min_price` is not null) as `aggregate_table`", "type": "query", "params": [], "bindings": ["en", "en", "en", "en", "en", 1, "2025-07-02", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/HomeService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\HomeService.php", "line": 48}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/V1/HomeController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\HomeController.php", "line": 28}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 19, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}, {"index": 20, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingRoutingDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingRoutingDispatcher.php", "line": 35}], "start": **********.9090989, "duration": 0.0013, "duration_str": "1.3ms", "memory": 0, "memory_str": null, "filename": "HomeService.php:48", "source": {"index": 16, "namespace": null, "name": "app/Services/HomeService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\HomeService.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FServices%2FHomeService.php&line=48", "ajax": false, "filename": "HomeService.php", "line": "48"}, "connection": "ticketgol", "explain": null, "start_percent": 61.777, "width_percent": 6.835}, {"sql": "select `events`.`id`, `date`, `events`.`stadium_id`, `events`.`league_id`, `et`.`name` as `event_name`, `st`.`name` as `stadium_name`, `lt`.`name` as `league_name`, (select MIN(price) from `tickets` where `event_id` = `events`.`id` and `tickets`.`deleted_at` is null) as `min_price` from `events` left join `event_translations` as `et` on `events`.`id` = `et`.`event_id` and `et`.`locale` = 'en' left join `stadium_translations` as `st` on `events`.`stadium_id` = `st`.`stadium_id` and `st`.`locale` = 'en' left join `league_translations` as `lt` on `events`.`league_id` = `lt`.`league_id` and `lt`.`locale` = 'en' left join `club_translations` as `hc` on `events`.`home_club_id` = `hc`.`club_id` and `hc`.`locale` = 'en' left join `club_translations` as `gc` on `events`.`guest_club_id` = `gc`.`club_id` and `gc`.`locale` = 'en' where `is_published` = 1 and `date` >= '2025-07-02' and `is_feature_event` = 1 and `events`.`deleted_at` is null having `min_price` is not null limit 8 offset 0", "type": "query", "params": [], "bindings": ["en", "en", "en", "en", "en", 1, "2025-07-02", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/HomeService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\HomeService.php", "line": 48}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/V1/HomeController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\HomeController.php", "line": 28}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 19, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}, {"index": 20, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingRoutingDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingRoutingDispatcher.php", "line": 35}], "start": **********.9119859, "duration": 0.00126, "duration_str": "1.26ms", "memory": 0, "memory_str": null, "filename": "HomeService.php:48", "source": {"index": 16, "namespace": null, "name": "app/Services/HomeService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\HomeService.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FServices%2FHomeService.php&line=48", "ajax": false, "filename": "HomeService.php", "line": "48"}, "connection": "ticketgol", "explain": null, "start_percent": 68.612, "width_percent": 6.625}, {"sql": "select * from `media` where `media`.`model_id` in (1, 2, 3) and `media`.`model_type` = 'App\\\\Models\\\\Event'", "type": "query", "params": [], "bindings": ["App\\Models\\Event"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Services/HomeService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\HomeService.php", "line": 48}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Api/V1/HomeController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\HomeController.php", "line": 28}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 24, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}, {"index": 25, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingRoutingDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingRoutingDispatcher.php", "line": 35}], "start": **********.915205, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "HomeService.php:48", "source": {"index": 21, "namespace": null, "name": "app/Services/HomeService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\HomeService.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FServices%2FHomeService.php&line=48", "ajax": false, "filename": "HomeService.php", "line": "48"}, "connection": "ticketgol", "explain": null, "start_percent": 75.237, "width_percent": 3.049}, {"sql": "select `id` from `stadiums` where `stadiums`.`id` in (6, 7, 9) and `stadiums`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Services/HomeService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\HomeService.php", "line": 48}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Api/V1/HomeController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\HomeController.php", "line": 28}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 24, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}, {"index": 25, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingRoutingDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingRoutingDispatcher.php", "line": 35}], "start": **********.917186, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "HomeService.php:48", "source": {"index": 21, "namespace": null, "name": "app/Services/HomeService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\HomeService.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FServices%2FHomeService.php&line=48", "ajax": false, "filename": "HomeService.php", "line": "48"}, "connection": "ticketgol", "explain": null, "start_percent": 78.286, "width_percent": 3.102}, {"sql": "select `sluggable_id`, `slug`, `locale` from `slugs` where `locale` = 'en' and `slugs`.`sluggable_id` in (6, 7, 9) and `slugs`.`sluggable_type` = 'App\\\\Models\\\\Stadium'", "type": "query", "params": [], "bindings": ["en", "App\\Models\\Stadium"], "hints": null, "show_copy": true, "backtrace": [{"index": 26, "namespace": null, "name": "app/Services/HomeService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\HomeService.php", "line": 48}, {"index": 27, "namespace": null, "name": "app/Http/Controllers/Api/V1/HomeController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\HomeController.php", "line": 28}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 29, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}, {"index": 30, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingRoutingDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingRoutingDispatcher.php", "line": 35}], "start": **********.919485, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "HomeService.php:48", "source": {"index": 26, "namespace": null, "name": "app/Services/HomeService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\HomeService.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FServices%2FHomeService.php&line=48", "ajax": false, "filename": "HomeService.php", "line": "48"}, "connection": "ticketgol", "explain": null, "start_percent": 81.388, "width_percent": 3.523}, {"sql": "select `id` from `leagues` where `leagues`.`id` in (3, 7) and `leagues`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Services/HomeService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\HomeService.php", "line": 48}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Api/V1/HomeController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\HomeController.php", "line": 28}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 24, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}, {"index": 25, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingRoutingDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingRoutingDispatcher.php", "line": 35}], "start": **********.921973, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "HomeService.php:48", "source": {"index": 21, "namespace": null, "name": "app/Services/HomeService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\HomeService.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FServices%2FHomeService.php&line=48", "ajax": false, "filename": "HomeService.php", "line": "48"}, "connection": "ticketgol", "explain": null, "start_percent": 84.911, "width_percent": 2.787}, {"sql": "select `sluggable_id`, `slug`, `locale` from `slugs` where `locale` = 'en' and `slugs`.`sluggable_id` in (3, 7) and `slugs`.`sluggable_type` = 'App\\\\Models\\\\League'", "type": "query", "params": [], "bindings": ["en", "App\\Models\\League"], "hints": null, "show_copy": true, "backtrace": [{"index": 26, "namespace": null, "name": "app/Services/HomeService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\HomeService.php", "line": 48}, {"index": 27, "namespace": null, "name": "app/Http/Controllers/Api/V1/HomeController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\HomeController.php", "line": 28}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 29, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}, {"index": 30, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingRoutingDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingRoutingDispatcher.php", "line": 35}], "start": **********.924234, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "HomeService.php:48", "source": {"index": 26, "namespace": null, "name": "app/Services/HomeService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\HomeService.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FServices%2FHomeService.php&line=48", "ajax": false, "filename": "HomeService.php", "line": "48"}, "connection": "ticketgol", "explain": null, "start_percent": 87.697, "width_percent": 3.68}, {"sql": "select `sluggable_id`, `slug`, `locale` from `slugs` where `locale` = 'en' and `slugs`.`sluggable_id` in (1, 2, 3) and `slugs`.`sluggable_type` = 'App\\\\Models\\\\Event'", "type": "query", "params": [], "bindings": ["en", "App\\Models\\Event"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Services/HomeService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\HomeService.php", "line": 48}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Api/V1/HomeController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\HomeController.php", "line": 28}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 24, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}, {"index": 25, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingRoutingDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingRoutingDispatcher.php", "line": 35}], "start": **********.927368, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "HomeService.php:48", "source": {"index": 21, "namespace": null, "name": "app/Services/HomeService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\HomeService.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FServices%2FHomeService.php&line=48", "ajax": false, "filename": "HomeService.php", "line": "48"}, "connection": "ticketgol", "explain": null, "start_percent": 91.377, "width_percent": 3.733}, {"sql": "update `sessions` set `payload` = 'YTozOntzOjY6Il90b2tlbiI7czo0MDoiemlaUjFWR2NuZmhMUFRFWkJLazcycXM0aWs4M0xWM3lyT2lGajhzVCI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6MjE6Imh0dHA6Ly90aWNrZXRnb2wudGVzdCI7fXM6NjoiX2ZsYXNoIjthOjI6e3M6Mzoib2xkIjthOjA6e31zOjM6Im5ldyI7YTowOnt9fX0=', `last_activity` = **********, `user_id` = null, `ip_address` = '127.0.0.1', `user_agent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' where `id` = 'Nmg37lihcVT6zZMhXS82xXUo71zj9zMJaGTKWQUz'", "type": "query", "params": [], "bindings": ["YTozOntzOjY6Il90b2tlbiI7czo0MDoiemlaUjFWR2NuZmhMUFRFWkJLazcycXM0aWs4M0xWM3lyT2lGajhzVCI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6MjE6Imh0dHA6Ly90aWNrZXRnb2wudGVzdCI7fXM6NjoiX2ZsYXNoIjthOjI6e3M6Mzoib2xkIjthOjA6e31zOjM6Im5ldyI7YTowOnt9fX0=", **********, null, "127.0.0.1", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "Nmg37lihcVT6zZMhXS82xXUo71zj9zMJaGTKWQUz"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 173}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 140}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 175}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 245}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 130}], "start": **********.9375699, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:173", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 173}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=173", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "173"}, "connection": "ticketgol", "explain": null, "start_percent": 95.11, "width_percent": 4.89}]}, "models": {"data": {"App\\Models\\Slug": {"value": 21, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FSlug.php&line=1", "ajax": false, "filename": "Slug.php", "line": "?"}}, "App\\Models\\Event": {"value": 8, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FEvent.php&line=1", "ajax": false, "filename": "Event.php", "line": "?"}}, "App\\Models\\League": {"value": 7, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FLeague.php&line=1", "ajax": false, "filename": "League.php", "line": "?"}}, "App\\Models\\Stadium": {"value": 6, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FStadium.php&line=1", "ajax": false, "filename": "Stadium.php", "line": "?"}}, "Spatie\\MediaLibrary\\MediaCollections\\Models\\Media": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Fspatie%2Flaravel-medialibrary%2Fsrc%2FMediaCollections%2FModels%2FMedia.php&line=1", "ajax": false, "filename": "Media.php", "line": "?"}}}, "count": 44, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "ziZR1VGcnfhLPTEZBKk72qs4ik83LV3yrOiFj8sT", "_previous": "array:1 [\n  \"url\" => \"http://ticketgol.test\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]"}, "request": {"telescope": "<a href=\"http://ticketgol.test/_debugbar/telescope/9f4be88b-1ba5-41c5-8e30-2a0a98b7764d\" target=\"_blank\">View in Telescope</a>", "path_info": "/api/v1/home", "status_code": "<pre class=sf-dump id=sf-dump-106739050 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-106739050\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1250742498 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1250742498\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2142464338 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2142464338\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1155341526 data-indent-pad=\"  \"><span class=sf-dump-note>array:11</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"735 characters\">selected_locale=en; XSRF-TOKEN=eyJpdiI6Im9wYit1YXZROEZjS1pHYm80a3VKYlE9PSIsInZhbHVlIjoiOGV6NEtEOWQwOUF6aTg2bFo3L3hSRHBMMjRvbXZZTjZLRFhaR0lpK1ZmYk5ETmNqZjJ4VUVGU2ExSjlqZFQzUzM1dlBSemFCSE1vQ1lVaHhVSkE1QWhiQ0dGVHJCTmY0ZWc1d1RTY0VzdS9HV0RyejdoL1RJMktmd3dZVEhCODYiLCJtYWMiOiI4MjkyNWFmNWQwZjY1NDQ1NzdlZTM0YWI0YjcxNWY3ZTIzM2ZlYWQyYmRlNWQ1ZmMwMTcxN2FhOGRiM2Y1YWJmIiwidGFnIjoiIn0%3D; ticketgol_session=eyJpdiI6ImdveklqUnNJTk5UNFdUTDVKUGpkcnc9PSIsInZhbHVlIjoibTh4WFZqcFlCSWVJN0x1ay9xM0F2eEJsOUJOMDAxeTY0aEhvazBJZDd6U3hzOGQyRE5wNWZqQkdzMDIrV01ZZzFyb3dhTWFhQmtSZGx2eGYxbnppS3pWeVA2ZUZFekxZSm5JNWt4dDNDU0FvdWxyZ2luaW9DUnVNTERVZXl5SVkiLCJtYWMiOiIxNDYwZTBkMzI2Yjc1ZDY2OGVjYWY3YTkyZWQxOGNkNDExNWRlM2E5MzhhYzJhMDg3YzA4MjNkMWU3YWJmMDQyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">en-US</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://ticketgol.test/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-locale</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6Im9wYit1YXZROEZjS1pHYm80a3VKYlE9PSIsInZhbHVlIjoiOGV6NEtEOWQwOUF6aTg2bFo3L3hSRHBMMjRvbXZZTjZLRFhaR0lpK1ZmYk5ETmNqZjJ4VUVGU2ExSjlqZFQzUzM1dlBSemFCSE1vQ1lVaHhVSkE1QWhiQ0dGVHJCTmY0ZWc1d1RTY0VzdS9HV0RyejdoL1RJMktmd3dZVEhCODYiLCJtYWMiOiI4MjkyNWFmNWQwZjY1NDQ1NzdlZTM0YWI0YjcxNWY3ZTIzM2ZlYWQyYmRlNWQ1ZmMwMTcxN2FhOGRiM2Y1YWJmIiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">ticketgol.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1155341526\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2079838828 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>selected_locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ziZR1VGcnfhLPTEZBKk72qs4ik83LV3yrOiFj8sT</span>\"\n  \"<span class=sf-dump-key>ticketgol_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Nmg37lihcVT6zZMhXS82xXUo71zj9zMJaGTKWQUz</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2079838828\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-89221063 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 02 Jul 2025 19:15:27 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6Ikt1Y1M4TzJPcm16d2R5RlZYNEh0UlE9PSIsInZhbHVlIjoiZ1ZHV2dEWTQ1bmlzNi9jQUtQOHZ0S09rWVJ3b1RXUzMzSmZ3S2Uyekl3MzlkOVF2YTIzcjIzWEJZQlVQNzVaM1RLWTlLVkhpdEczK1NYTVpSc1RoN1cydlRuajhHUW1WSGRQRlBlU1FnUlg1ekRJRVVNTm1NbExDOGUwbEsyWGYiLCJtYWMiOiJjYjFjZGIxNmI5ZGU0NmZmYWJkYzJjNThiOGEyM2M3M2FkYTgwMWNmM2I2YWIxMWNlMzRmNzdlM2RiNTU3MWEwIiwidGFnIjoiIn0%3D; expires=Wed, 02 Jul 2025 21:15:27 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"445 characters\">ticketgol_session=eyJpdiI6ImRnSVYrR3hCM0U3alcvazBmTVh6blE9PSIsInZhbHVlIjoiZ0JBRTRwOHFJRjJCQWVBR1J1V05pUzk3SXFHYUZObTBEMldibEJKSkFwM1VXY2tBdXliSHVheU95RjR0SWJXNVVIRSsyekNTOVlQU3Zkb05zbzhhek54V3lPb2p2MkZYclp5NnlxR0hyUGRJQlNRQ2ZzQjFmUGI3RlZxbWtKSVQiLCJtYWMiOiIyMmY5ZWRlZDhiOGM5NTM4YzYyMWRlMmNkOWQ0NjBlZDI0MzczNjE1ZDhiZGQwZWZjM2I3YWRlMWQxYjhjZTI5IiwidGFnIjoiIn0%3D; expires=Wed, 02 Jul 2025 21:15:27 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6Ikt1Y1M4TzJPcm16d2R5RlZYNEh0UlE9PSIsInZhbHVlIjoiZ1ZHV2dEWTQ1bmlzNi9jQUtQOHZ0S09rWVJ3b1RXUzMzSmZ3S2Uyekl3MzlkOVF2YTIzcjIzWEJZQlVQNzVaM1RLWTlLVkhpdEczK1NYTVpSc1RoN1cydlRuajhHUW1WSGRQRlBlU1FnUlg1ekRJRVVNTm1NbExDOGUwbEsyWGYiLCJtYWMiOiJjYjFjZGIxNmI5ZGU0NmZmYWJkYzJjNThiOGEyM2M3M2FkYTgwMWNmM2I2YWIxMWNlMzRmNzdlM2RiNTU3MWEwIiwidGFnIjoiIn0%3D; expires=Wed, 02-Jul-2025 21:15:27 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"417 characters\">ticketgol_session=eyJpdiI6ImRnSVYrR3hCM0U3alcvazBmTVh6blE9PSIsInZhbHVlIjoiZ0JBRTRwOHFJRjJCQWVBR1J1V05pUzk3SXFHYUZObTBEMldibEJKSkFwM1VXY2tBdXliSHVheU95RjR0SWJXNVVIRSsyekNTOVlQU3Zkb05zbzhhek54V3lPb2p2MkZYclp5NnlxR0hyUGRJQlNRQ2ZzQjFmUGI3RlZxbWtKSVQiLCJtYWMiOiIyMmY5ZWRlZDhiOGM5NTM4YzYyMWRlMmNkOWQ0NjBlZDI0MzczNjE1ZDhiZGQwZWZjM2I3YWRlMWQxYjhjZTI5IiwidGFnIjoiIn0%3D; expires=Wed, 02-Jul-2025 21:15:27 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-89221063\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-510462516 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ziZR1VGcnfhLPTEZBKk72qs4ik83LV3yrOiFj8sT</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://ticketgol.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-510462516\", {\"maxDepth\":0})</script>\n"}}