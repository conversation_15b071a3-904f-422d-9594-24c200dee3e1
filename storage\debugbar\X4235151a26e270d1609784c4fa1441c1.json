{"__meta": {"id": "X4235151a26e270d1609784c4fa1441c1", "datetime": "2025-07-02 10:39:08", "utime": **********.448299, "method": "GET", "uri": "/api/v1/events/filters", "ip": "127.0.0.1"}, "php": {"version": "8.4.6", "interface": "cgi-fcgi"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.278884, "end": **********.448316, "duration": 0.1694321632385254, "duration_str": "169ms", "measures": [{"label": "Booting", "start": **********.278884, "relative_start": 0, "end": **********.352042, "relative_end": **********.352042, "duration": 0.07315802574157715, "duration_str": "73.16ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.352054, "relative_start": 0.07317018508911133, "end": **********.448319, "relative_end": 2.86102294921875e-06, "duration": 0.09626483917236328, "duration_str": "96.26ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 7154384, "peak_usage_str": "7MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET api/v1/events/filters", "middleware": "api, set-locale", "controller": "App\\Http\\Controllers\\Api\\V1\\EventController@getFilters", "namespace": null, "prefix": "api/v1/events", "where": [], "as": "api.events.filters", "file": "<a href=\"phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FHttp%2FControllers%2FApi%2FV1%2FEventController.php&line=66\" onclick=\"\">app/Http/Controllers/Api/V1/EventController.php:66-87</a>"}, "queries": {"nb_statements": 14, "nb_visible_statements": 14, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.029109999999999997, "accumulated_duration_str": "29.11ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `sessions` where `id` = '0SPVygoyCBWzh93wW1oy7sqhnBQ7iBtsSbwRhUjx' limit 1", "type": "query", "params": [], "bindings": ["0SPVygoyCBWzh93wW1oy7sqhnBQ7iBtsSbwRhUjx"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 147}], "start": **********.356542, "duration": 0.00188, "duration_str": "1.88ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:97", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=97", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "97"}, "connection": "ticketgol", "explain": null, "start_percent": 0, "width_percent": 6.458}, {"sql": "select * from `users` where `id` = 7 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "auth.session", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\sanctum\\src\\Http\\Middleware\\AuthenticateSession.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.362357, "duration": 0.00141, "duration_str": "1.41ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ticketgol", "explain": null, "start_percent": 6.458, "width_percent": 4.844}, {"sql": "select `id` from `stadiums` where `is_published` = 1 and `stadiums`.`deleted_at` is null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/StadiumRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\StadiumRepository.php", "line": 37}, {"index": 16, "namespace": null, "name": "app/Services/StadiumService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\StadiumService.php", "line": 40}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/V1/EventController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\EventController.php", "line": 69}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 19, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.369585, "duration": 0.00211, "duration_str": "2.11ms", "memory": 0, "memory_str": null, "filename": "StadiumRepository.php:37", "source": {"index": 15, "namespace": null, "name": "app/Repositories/StadiumRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\StadiumRepository.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FStadiumRepository.php&line=37", "ajax": false, "filename": "StadiumRepository.php", "line": "37"}, "connection": "ticketgol", "explain": null, "start_percent": 11.302, "width_percent": 7.248}, {"sql": "select `stadium_id`, `name` from `stadium_translations` where `locale` = 'en' and `stadium_translations`.`stadium_id` in (3, 5, 10)", "type": "query", "params": [], "bindings": ["en"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Repositories/StadiumRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\StadiumRepository.php", "line": 37}, {"index": 21, "namespace": null, "name": "app/Services/StadiumService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\StadiumService.php", "line": 40}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Api/V1/EventController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\EventController.php", "line": 69}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 24, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.3771589, "duration": 0.00342, "duration_str": "3.42ms", "memory": 0, "memory_str": null, "filename": "StadiumRepository.php:37", "source": {"index": 20, "namespace": null, "name": "app/Repositories/StadiumRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\StadiumRepository.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FStadiumRepository.php&line=37", "ajax": false, "filename": "StadiumRepository.php", "line": "37"}, "connection": "ticketgol", "explain": null, "start_percent": 18.55, "width_percent": 11.749}, {"sql": "select `sluggable_id`, `slug`, `locale` from `slugs` where `locale` = 'en' and `slugs`.`sluggable_id` in (3, 5, 10) and `slugs`.`sluggable_type` = 'App\\\\Models\\\\Stadium'", "type": "query", "params": [], "bindings": ["en", "App\\Models\\Stadium"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Repositories/StadiumRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\StadiumRepository.php", "line": 37}, {"index": 21, "namespace": null, "name": "app/Services/StadiumService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\StadiumService.php", "line": 40}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Api/V1/EventController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\EventController.php", "line": 69}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 24, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.3841991, "duration": 0.00121, "duration_str": "1.21ms", "memory": 0, "memory_str": null, "filename": "StadiumRepository.php:37", "source": {"index": 20, "namespace": null, "name": "app/Repositories/StadiumRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\StadiumRepository.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FStadiumRepository.php&line=37", "ajax": false, "filename": "StadiumRepository.php", "line": "37"}, "connection": "ticketgol", "explain": null, "start_percent": 30.299, "width_percent": 4.157}, {"sql": "select `id` from `clubs` where `is_active` = 1 and `clubs`.`deleted_at` is null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/ClubRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\ClubRepository.php", "line": 37}, {"index": 16, "namespace": null, "name": "app/Services/ClubService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\ClubService.php", "line": 40}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/V1/EventController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\EventController.php", "line": 70}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 19, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.3889658, "duration": 0.00232, "duration_str": "2.32ms", "memory": 0, "memory_str": null, "filename": "ClubRepository.php:37", "source": {"index": 15, "namespace": null, "name": "app/Repositories/ClubRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\ClubRepository.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FClubRepository.php&line=37", "ajax": false, "filename": "ClubRepository.php", "line": "37"}, "connection": "ticketgol", "explain": null, "start_percent": 34.456, "width_percent": 7.97}, {"sql": "select `club_id`, `name` from `club_translations` where `locale` = 'en' and `club_translations`.`club_id` in (1, 5, 6, 7, 8, 9, 10)", "type": "query", "params": [], "bindings": ["en"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Repositories/ClubRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\ClubRepository.php", "line": 37}, {"index": 21, "namespace": null, "name": "app/Services/ClubService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\ClubService.php", "line": 40}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Api/V1/EventController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\EventController.php", "line": 70}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 24, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.3941321, "duration": 0.00278, "duration_str": "2.78ms", "memory": 0, "memory_str": null, "filename": "ClubRepository.php:37", "source": {"index": 20, "namespace": null, "name": "app/Repositories/ClubRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\ClubRepository.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FClubRepository.php&line=37", "ajax": false, "filename": "ClubRepository.php", "line": "37"}, "connection": "ticketgol", "explain": null, "start_percent": 42.425, "width_percent": 9.55}, {"sql": "select `sluggable_id`, `slug`, `locale` from `slugs` where `locale` = 'en' and `slugs`.`sluggable_id` in (1, 5, 6, 7, 8, 9, 10) and `slugs`.`sluggable_type` = 'App\\\\Models\\\\Club'", "type": "query", "params": [], "bindings": ["en", "App\\Models\\Club"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Repositories/ClubRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\ClubRepository.php", "line": 37}, {"index": 21, "namespace": null, "name": "app/Services/ClubService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\ClubService.php", "line": 40}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Api/V1/EventController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\EventController.php", "line": 70}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 24, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.399426, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "ClubRepository.php:37", "source": {"index": 20, "namespace": null, "name": "app/Repositories/ClubRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\ClubRepository.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FClubRepository.php&line=37", "ajax": false, "filename": "ClubRepository.php", "line": "37"}, "connection": "ticketgol", "explain": null, "start_percent": 51.975, "width_percent": 3.47}, {"sql": "select `id` from `leagues` where `is_published` = 1 and `leagues`.`deleted_at` is null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/LeagueRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\LeagueRepository.php", "line": 37}, {"index": 16, "namespace": null, "name": "app/Services/LeagueService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\LeagueService.php", "line": 40}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/V1/EventController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\EventController.php", "line": 71}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 19, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.403399, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "LeagueRepository.php:37", "source": {"index": 15, "namespace": null, "name": "app/Repositories/LeagueRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\LeagueRepository.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FLeagueRepository.php&line=37", "ajax": false, "filename": "LeagueRepository.php", "line": "37"}, "connection": "ticketgol", "explain": null, "start_percent": 55.445, "width_percent": 3.16}, {"sql": "select `league_id`, `name` from `league_translations` where `locale` = 'en' and `league_translations`.`league_id` in (1, 5, 6, 7, 10)", "type": "query", "params": [], "bindings": ["en"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Repositories/LeagueRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\LeagueRepository.php", "line": 37}, {"index": 21, "namespace": null, "name": "app/Services/LeagueService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\LeagueService.php", "line": 40}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Api/V1/EventController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\EventController.php", "line": 71}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 24, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.408357, "duration": 0.00229, "duration_str": "2.29ms", "memory": 0, "memory_str": null, "filename": "LeagueRepository.php:37", "source": {"index": 20, "namespace": null, "name": "app/Repositories/LeagueRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\LeagueRepository.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FLeagueRepository.php&line=37", "ajax": false, "filename": "LeagueRepository.php", "line": "37"}, "connection": "ticketgol", "explain": null, "start_percent": 58.605, "width_percent": 7.867}, {"sql": "select `sluggable_id`, `slug`, `locale` from `slugs` where `locale` = 'en' and `slugs`.`sluggable_id` in (1, 5, 6, 7, 10) and `slugs`.`sluggable_type` = 'App\\\\Models\\\\League'", "type": "query", "params": [], "bindings": ["en", "App\\Models\\League"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Repositories/LeagueRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\LeagueRepository.php", "line": 37}, {"index": 21, "namespace": null, "name": "app/Services/LeagueService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\LeagueService.php", "line": 40}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Api/V1/EventController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\EventController.php", "line": 71}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 24, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.413634, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "LeagueRepository.php:37", "source": {"index": 20, "namespace": null, "name": "app/Repositories/LeagueRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\LeagueRepository.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FLeagueRepository.php&line=37", "ajax": false, "filename": "LeagueRepository.php", "line": "37"}, "connection": "ticketgol", "explain": null, "start_percent": 66.472, "width_percent": 2.473}, {"sql": "select `id` from `countries` where `is_published` = 1 and `countries`.`deleted_at` is null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/CountryRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\CountryRepository.php", "line": 29}, {"index": 16, "namespace": null, "name": "app/Services/CountryService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\CountryService.php", "line": 49}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/V1/EventController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\EventController.php", "line": 72}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 19, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.416921, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "CountryRepository.php:29", "source": {"index": 15, "namespace": null, "name": "app/Repositories/CountryRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\CountryRepository.php", "line": 29}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FCountryRepository.php&line=29", "ajax": false, "filename": "CountryRepository.php", "line": "29"}, "connection": "ticketgol", "explain": null, "start_percent": 68.945, "width_percent": 3.195}, {"sql": "select `country_id`, `name` from `country_translations` where `locale` = 'en' and `country_translations`.`country_id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184, 185, 186, 187, 188, 189, 190, 191, 192, 193, 194, 195, 196, 197, 198, 199, 200, 201, 202, 203, 204, 205, 206, 207, 208, 209, 210, 211, 212, 213, 214, 215, 216, 217, 218, 219, 220, 221, 222, 223, 224, 225, 226, 227, 228, 229, 230, 231, 232, 233, 234, 235, 236, 237, 238, 239, 240, 241, 242, 243, 244, 245, 246, 247, 248, 249)", "type": "query", "params": [], "bindings": ["en"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Repositories/CountryRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\CountryRepository.php", "line": 29}, {"index": 21, "namespace": null, "name": "app/Services/CountryService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\CountryService.php", "line": 49}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Api/V1/EventController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\EventController.php", "line": 72}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 24, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.426748, "duration": 0.0025499999999999997, "duration_str": "2.55ms", "memory": 0, "memory_str": null, "filename": "CountryRepository.php:29", "source": {"index": 20, "namespace": null, "name": "app/Repositories/CountryRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\CountryRepository.php", "line": 29}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FCountryRepository.php&line=29", "ajax": false, "filename": "CountryRepository.php", "line": "29"}, "connection": "ticketgol", "explain": null, "start_percent": 72.14, "width_percent": 8.76}, {"sql": "update `sessions` set `payload` = 'YTo2OntzOjY6Il90b2tlbiI7czo0MDoiMGV4SkJyZk5vUWFtWHJTeEJpSDVLR0ZnMGo4V3BhNk5mTGgzRlBvZSI7czozOiJ1cmwiO2E6MDp7fXM6OToiX3ByZXZpb3VzIjthOjE6e3M6MzoidXJsIjtzOjI3OiJodHRwOi8vdGlja2V0Z29sLnRlc3QvbG9naW4iO31zOjY6Il9mbGFzaCI7YToyOntzOjM6Im9sZCI7YTowOnt9czozOiJuZXciO2E6MDp7fX1zOjUwOiJsb2dpbl93ZWJfNTliYTM2YWRkYzJiMmY5NDAxNTgwZjAxNGM3ZjU4ZWE0ZTMwOTg5ZCI7aTo3O3M6MTc6InBhc3N3b3JkX2hhc2hfd2ViIjtzOjYwOiIkMnkkMTIkVjF3UjY0R01Rb2xUTUFwRTN3emx5ZXdMOFA5YnNBQzNhZlpPZ0loajlmcm1DdXF6WS4uNWUiO30=', `last_activity` = **********, `user_id` = 7, `ip_address` = '127.0.0.1', `user_agent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' where `id` = '0SPVygoyCBWzh93wW1oy7sqhnBQ7iBtsSbwRhUjx'", "type": "query", "params": [], "bindings": ["YTo2OntzOjY6Il90b2tlbiI7czo0MDoiMGV4SkJyZk5vUWFtWHJTeEJpSDVLR0ZnMGo4V3BhNk5mTGgzRlBvZSI7czozOiJ1cmwiO2E6MDp7fXM6OToiX3ByZXZpb3VzIjthOjE6e3M6MzoidXJsIjtzOjI3OiJodHRwOi8vdGlja2V0Z29sLnRlc3QvbG9naW4iO31zOjY6Il9mbGFzaCI7YToyOntzOjM6Im9sZCI7YTowOnt9czozOiJuZXciO2E6MDp7fX1zOjUwOiJsb2dpbl93ZWJfNTliYTM2YWRkYzJiMmY5NDAxNTgwZjAxNGM3ZjU4ZWE0ZTMwOTg5ZCI7aTo3O3M6MTc6InBhc3N3b3JkX2hhc2hfd2ViIjtzOjYwOiIkMnkkMTIkVjF3UjY0R01Rb2xUTUFwRTN3emx5ZXdMOFA5YnNBQzNhZlpPZ0loajlmcm1DdXF6WS4uNWUiO30=", **********, 7, "127.0.0.1", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "0SPVygoyCBWzh93wW1oy7sqhnBQ7iBtsSbwRhUjx"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 173}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 140}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 175}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 245}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 130}], "start": **********.441378, "duration": 0.00556, "duration_str": "5.56ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:173", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 173}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=173", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "173"}, "connection": "ticketgol", "explain": null, "start_percent": 80.9, "width_percent": 19.1}]}, "models": {"data": {"App\\Models\\Country": {"value": 249, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FCountry.php&line=1", "ajax": false, "filename": "Country.php", "line": "?"}}, "App\\Models\\CountryTranslation": {"value": 249, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FCountryTranslation.php&line=1", "ajax": false, "filename": "CountryTranslation.php", "line": "?"}}, "App\\Models\\Slug": {"value": 15, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FSlug.php&line=1", "ajax": false, "filename": "Slug.php", "line": "?"}}, "App\\Models\\Club": {"value": 7, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FClub.php&line=1", "ajax": false, "filename": "Club.php", "line": "?"}}, "App\\Models\\ClubTranslation": {"value": 7, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FClubTranslation.php&line=1", "ajax": false, "filename": "ClubTranslation.php", "line": "?"}}, "App\\Models\\League": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FLeague.php&line=1", "ajax": false, "filename": "League.php", "line": "?"}}, "App\\Models\\LeagueTranslation": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FLeagueTranslation.php&line=1", "ajax": false, "filename": "LeagueTranslation.php", "line": "?"}}, "App\\Models\\Stadium": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FStadium.php&line=1", "ajax": false, "filename": "Stadium.php", "line": "?"}}, "App\\Models\\StadiumTranslation": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FStadiumTranslation.php&line=1", "ajax": false, "filename": "StadiumTranslation.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 544, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "0exJBrfNoQamXrSxBiH5KGFg0j8Wpa6NfLh3FPoe", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://ticketgol.test/login\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "7", "password_hash_web": "$2y$12$V1wR64GMQolTMApE3wzlyewL8P9bsAC3afZOgIhj9frmCuqzY..5e"}, "request": {"telescope": "<a href=\"http://ticketgol.test/_debugbar/telescope/9f4b2fe4-27a5-49ce-abfb-296c2d81f5ec\" target=\"_blank\">View in Telescope</a>", "path_info": "/api/v1/events/filters", "status_code": "<pre class=sf-dump id=sf-dump-1730622797 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1730622797\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-799119650 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-799119650\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1572143929 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1572143929\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:11</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"853 characters\">ajs_anonymous_id=%2205cf8d6b-8a14-40b9-9d4f-c4ebd1620a6e%22; selected_locale=en; __stripe_mid=fd60eaa0-e90c-4914-bb27-ce58ed87fc6066be49; XSRF-TOKEN=eyJpdiI6ImV3NmdtZ3FZRGRodDVNTXQwTDUvMWc9PSIsInZhbHVlIjoiU0lOeVBWc1BQRHNXN1ozdEZuY0wwMG11NCtRcTVZM2twTm9xbkhYRzk5aXE2SzBKaGhvUU1jOGVPWFkvMnFOT3ZMUUdCMnp3ZEFUK2c5Vmh0V0VSMW5nR1czQXpBQjRTNEJrSmt1WXJoNjQ4TlllVExHdDJVcWxSM0JoajdsclEiLCJtYWMiOiJhOTdhYzc1OTI1YTE4MmM5ODM3MDdkNTlmNjgyNDJmYmM5MjA1Mjk5ZTViOWJhNWMzZGNkMDQ2Y2MyNDI0N2M3IiwidGFnIjoiIn0%3D; ticketgol_session=eyJpdiI6IkVnQThWa0dtdmJYMDdqN1JDL2UzVXc9PSIsInZhbHVlIjoiQ0wvRlRHQ0xtYklIcURiZ0ZsN2wwbTdiQXIzL3F0ellJNjEvVHVsQjZJamJLbmdDbFRWbU5GbTNwK0Jnc1hvYkxMYm9kdXUvT0xhMDdRMHdNSm9OaTIwcG85dmVKTUVJZHNUcXI1S3JHaDRaZlFJcDRIS3NxYWNDS1h5Zy9zZkciLCJtYWMiOiJmMGI2NzI1ZTU1ODU1YTgzYzY2ODgyYjRlMTIzNWY3MjU4NWYxNTgyMWQ2YmEwNmRlOTk5OWE2OGFjNjRhNmI0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">en-GB,en;q=0.9,es;q=0.8,en-US;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://ticketgol.test/events</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-locale</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6ImV3NmdtZ3FZRGRodDVNTXQwTDUvMWc9PSIsInZhbHVlIjoiU0lOeVBWc1BQRHNXN1ozdEZuY0wwMG11NCtRcTVZM2twTm9xbkhYRzk5aXE2SzBKaGhvUU1jOGVPWFkvMnFOT3ZMUUdCMnp3ZEFUK2c5Vmh0V0VSMW5nR1czQXpBQjRTNEJrSmt1WXJoNjQ4TlllVExHdDJVcWxSM0JoajdsclEiLCJtYWMiOiJhOTdhYzc1OTI1YTE4MmM5ODM3MDdkNTlmNjgyNDJmYmM5MjA1Mjk5ZTViOWJhNWMzZGNkMDQ2Y2MyNDI0N2M3IiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">ticketgol.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1122864091 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>ajs_anonymous_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>selected_locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0exJBrfNoQamXrSxBiH5KGFg0j8Wpa6NfLh3FPoe</span>\"\n  \"<span class=sf-dump-key>ticketgol_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0SPVygoyCBWzh93wW1oy7sqhnBQ7iBtsSbwRhUjx</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1122864091\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-101712765 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 02 Jul 2025 10:39:08 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6InFHaXdWTEVZeStEaEdCSXJubzZnNkE9PSIsInZhbHVlIjoiRGdLYWRvWW54YWQvbWt4RDErRTUxS0tZTWZsVjAvem5WSHdma0xkUVh6NmNVLzJLeTZQeUVXMWxnc3JmdUdVcUZycFM4YXl4aVdSVklxK3BTZExxSGs3eUxkVzNDbm8rS21wczVnd0JJL1pNUlZyZkI1a0VML3htMDI4cG4yQmIiLCJtYWMiOiI0MWY0MWJmNjc0ODNkMDU3NGIxZTdhZTUxYWU0ZmEyZTI3YmMwNjljNjY2ZTIyYTgyZTNmNzdjNjI3NmM0NzE2IiwidGFnIjoiIn0%3D; expires=Wed, 02 Jul 2025 12:39:08 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"445 characters\">ticketgol_session=eyJpdiI6IlJnOXFvZ1p3enUxbzJoWFNvQi84YVE9PSIsInZhbHVlIjoiaHR5UDdIZVBBUW1jWkxwdzJsQ3pHMjF1eGpVRmx5MzRlNTRQdGFSQ1BtNWJXRHN2c0hDeGRBYTV6QmVKNVVDQXFaYnV0WUcvQ1ArSTdvOGNCdTBPM0hrcEs3WG9MYXNYMTY1cFFZNW8xL3VmUG1ncFJIWjR2c1daUE5IK2ErbTEiLCJtYWMiOiJmOGQ3NDgwN2JjYzhjZTM2ZmNiZDY1MzE2YzdhNTZlMTJiZWQ1MzI5NTllZTM3MGM0OGMxM2E1NmFlMTViZmI2IiwidGFnIjoiIn0%3D; expires=Wed, 02 Jul 2025 12:39:08 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6InFHaXdWTEVZeStEaEdCSXJubzZnNkE9PSIsInZhbHVlIjoiRGdLYWRvWW54YWQvbWt4RDErRTUxS0tZTWZsVjAvem5WSHdma0xkUVh6NmNVLzJLeTZQeUVXMWxnc3JmdUdVcUZycFM4YXl4aVdSVklxK3BTZExxSGs3eUxkVzNDbm8rS21wczVnd0JJL1pNUlZyZkI1a0VML3htMDI4cG4yQmIiLCJtYWMiOiI0MWY0MWJmNjc0ODNkMDU3NGIxZTdhZTUxYWU0ZmEyZTI3YmMwNjljNjY2ZTIyYTgyZTNmNzdjNjI3NmM0NzE2IiwidGFnIjoiIn0%3D; expires=Wed, 02-Jul-2025 12:39:08 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"417 characters\">ticketgol_session=eyJpdiI6IlJnOXFvZ1p3enUxbzJoWFNvQi84YVE9PSIsInZhbHVlIjoiaHR5UDdIZVBBUW1jWkxwdzJsQ3pHMjF1eGpVRmx5MzRlNTRQdGFSQ1BtNWJXRHN2c0hDeGRBYTV6QmVKNVVDQXFaYnV0WUcvQ1ArSTdvOGNCdTBPM0hrcEs3WG9MYXNYMTY1cFFZNW8xL3VmUG1ncFJIWjR2c1daUE5IK2ErbTEiLCJtYWMiOiJmOGQ3NDgwN2JjYzhjZTM2ZmNiZDY1MzE2YzdhNTZlMTJiZWQ1MzI5NTllZTM3MGM0OGMxM2E1NmFlMTViZmI2IiwidGFnIjoiIn0%3D; expires=Wed, 02-Jul-2025 12:39:08 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-101712765\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1062509637 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0exJBrfNoQamXrSxBiH5KGFg0j8Wpa6NfLh3FPoe</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://ticketgol.test/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$V1wR64GMQolTMApE3wzlyewL8P9bsAC3afZOgIhj9frmCuqzY..5e</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1062509637\", {\"maxDepth\":0})</script>\n"}}