{"__meta": {"id": "Xa9b58182165043d597b6510a9d68ef1a", "datetime": "2025-07-02 19:03:33", "utime": **********.808189, "method": "GET", "uri": "/api/v1/home", "ip": "127.0.0.1"}, "php": {"version": "8.4.6", "interface": "cgi-fcgi"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.638944, "end": **********.808214, "duration": 0.16927003860473633, "duration_str": "169ms", "measures": [{"label": "Booting", "start": **********.638944, "relative_start": 0, "end": **********.717885, "relative_end": **********.717885, "duration": 0.07894110679626465, "duration_str": "78.94ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.717903, "relative_start": 0.07895898818969727, "end": **********.808217, "relative_end": 3.0994415283203125e-06, "duration": 0.09031414985656738, "duration_str": "90.31ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 6773712, "peak_usage_str": "6MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET api/v1/home", "middleware": "api, set-locale", "controller": "App\\Http\\Controllers\\Api\\V1\\HomeController@index", "namespace": null, "prefix": "api/v1/home", "where": [], "as": "api.home.index", "file": "<a href=\"phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FHttp%2FControllers%2FApi%2FV1%2FHomeController.php&line=24\" onclick=\"\">app/Http/Controllers/Api/V1/HomeController.php:24-40</a>"}, "queries": {"nb_statements": 18, "nb_visible_statements": 18, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.02021, "accumulated_duration_str": "20.21ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `sessions` where `id` = 'CFoDwus13aj6e2xLfOgPLTvuRWf27pkZlMJ6n5jN' limit 1", "type": "query", "params": [], "bindings": ["CFoDwus13aj6e2xLfOgPLTvuRWf27pkZlMJ6n5jN"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 147}], "start": **********.7234058, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:97", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=97", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "97"}, "connection": "ticketgol", "explain": null, "start_percent": 0, "width_percent": 3.859}, {"sql": "select count(*) as aggregate from (select `events`.`id`, `date`, `events`.`stadium_id`, `events`.`league_id`, `et`.`name` as `event_name`, `st`.`name` as `stadium_name`, `lt`.`name` as `league_name`, (select MIN(price) from `tickets` where `event_id` = `events`.`id` and `tickets`.`deleted_at` is null) as `min_price` from `events` left join `event_translations` as `et` on `events`.`id` = `et`.`event_id` and `et`.`locale` = 'en' left join `stadium_translations` as `st` on `events`.`stadium_id` = `st`.`stadium_id` and `st`.`locale` = 'en' left join `league_translations` as `lt` on `events`.`league_id` = `lt`.`league_id` and `lt`.`locale` = 'en' left join `club_translations` as `hc` on `events`.`home_club_id` = `hc`.`club_id` and `hc`.`locale` = 'en' left join `club_translations` as `gc` on `events`.`guest_club_id` = `gc`.`club_id` and `gc`.`locale` = 'en' where `is_published` = 1 and `date` >= '2025-07-02' and `is_feature_event` = 0 and `events`.`deleted_at` is null having `min_price` is not null) as `aggregate_table`", "type": "query", "params": [], "bindings": ["en", "en", "en", "en", "en", 1, "2025-07-02", 0], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/HomeService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\HomeService.php", "line": 35}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/V1/HomeController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\HomeController.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 19, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}, {"index": 20, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingRoutingDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingRoutingDispatcher.php", "line": 35}], "start": **********.732505, "duration": 0.0031, "duration_str": "3.1ms", "memory": 0, "memory_str": null, "filename": "HomeService.php:35", "source": {"index": 16, "namespace": null, "name": "app/Services/HomeService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\HomeService.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FServices%2FHomeService.php&line=35", "ajax": false, "filename": "HomeService.php", "line": "35"}, "connection": "ticketgol", "explain": null, "start_percent": 3.859, "width_percent": 15.339}, {"sql": "select `events`.`id`, `date`, `events`.`stadium_id`, `events`.`league_id`, `et`.`name` as `event_name`, `st`.`name` as `stadium_name`, `lt`.`name` as `league_name`, (select MIN(price) from `tickets` where `event_id` = `events`.`id` and `tickets`.`deleted_at` is null) as `min_price` from `events` left join `event_translations` as `et` on `events`.`id` = `et`.`event_id` and `et`.`locale` = 'en' left join `stadium_translations` as `st` on `events`.`stadium_id` = `st`.`stadium_id` and `st`.`locale` = 'en' left join `league_translations` as `lt` on `events`.`league_id` = `lt`.`league_id` and `lt`.`locale` = 'en' left join `club_translations` as `hc` on `events`.`home_club_id` = `hc`.`club_id` and `hc`.`locale` = 'en' left join `club_translations` as `gc` on `events`.`guest_club_id` = `gc`.`club_id` and `gc`.`locale` = 'en' where `is_published` = 1 and `date` >= '2025-07-02' and `is_feature_event` = 0 and `events`.`deleted_at` is null having `min_price` is not null order by `date` asc limit 8 offset 0", "type": "query", "params": [], "bindings": ["en", "en", "en", "en", "en", 1, "2025-07-02", 0], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/HomeService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\HomeService.php", "line": 35}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/V1/HomeController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\HomeController.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 19, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}, {"index": 20, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingRoutingDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingRoutingDispatcher.php", "line": 35}], "start": **********.738316, "duration": 0.0014, "duration_str": "1.4ms", "memory": 0, "memory_str": null, "filename": "HomeService.php:35", "source": {"index": 16, "namespace": null, "name": "app/Services/HomeService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\HomeService.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FServices%2FHomeService.php&line=35", "ajax": false, "filename": "HomeService.php", "line": "35"}, "connection": "ticketgol", "explain": null, "start_percent": 19.198, "width_percent": 6.927}, {"sql": "select * from `media` where `media`.`model_id` in (4, 5, 6, 7, 9) and `media`.`model_type` = 'App\\\\Models\\\\Event'", "type": "query", "params": [], "bindings": ["App\\Models\\Event"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Services/HomeService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\HomeService.php", "line": 35}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Api/V1/HomeController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\HomeController.php", "line": 27}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 24, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}, {"index": 25, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingRoutingDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingRoutingDispatcher.php", "line": 35}], "start": **********.743232, "duration": 0.00134, "duration_str": "1.34ms", "memory": 0, "memory_str": null, "filename": "HomeService.php:35", "source": {"index": 21, "namespace": null, "name": "app/Services/HomeService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\HomeService.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FServices%2FHomeService.php&line=35", "ajax": false, "filename": "HomeService.php", "line": "35"}, "connection": "ticketgol", "explain": null, "start_percent": 26.126, "width_percent": 6.63}, {"sql": "select `id` from `stadiums` where `stadiums`.`id` in (1, 5, 6) and `stadiums`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Services/HomeService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\HomeService.php", "line": 35}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Api/V1/HomeController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\HomeController.php", "line": 27}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 24, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}, {"index": 25, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingRoutingDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingRoutingDispatcher.php", "line": 35}], "start": **********.747499, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "HomeService.php:35", "source": {"index": 21, "namespace": null, "name": "app/Services/HomeService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\HomeService.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FServices%2FHomeService.php&line=35", "ajax": false, "filename": "HomeService.php", "line": "35"}, "connection": "ticketgol", "explain": null, "start_percent": 32.756, "width_percent": 2.919}, {"sql": "select `sluggable_id`, `slug`, `locale` from `slugs` where `locale` = 'en' and `slugs`.`sluggable_id` in (1, 5, 6) and `slugs`.`sluggable_type` = 'App\\\\Models\\\\Stadium'", "type": "query", "params": [], "bindings": ["en", "App\\Models\\Stadium"], "hints": null, "show_copy": true, "backtrace": [{"index": 26, "namespace": null, "name": "app/Services/HomeService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\HomeService.php", "line": 35}, {"index": 27, "namespace": null, "name": "app/Http/Controllers/Api/V1/HomeController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\HomeController.php", "line": 27}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 29, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}, {"index": 30, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingRoutingDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingRoutingDispatcher.php", "line": 35}], "start": **********.751345, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "HomeService.php:35", "source": {"index": 26, "namespace": null, "name": "app/Services/HomeService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\HomeService.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FServices%2FHomeService.php&line=35", "ajax": false, "filename": "HomeService.php", "line": "35"}, "connection": "ticketgol", "explain": null, "start_percent": 35.675, "width_percent": 3.513}, {"sql": "select `id` from `leagues` where `leagues`.`id` in (1, 2, 3, 4, 7) and `leagues`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Services/HomeService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\HomeService.php", "line": 35}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Api/V1/HomeController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\HomeController.php", "line": 27}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 24, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}, {"index": 25, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingRoutingDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingRoutingDispatcher.php", "line": 35}], "start": **********.756935, "duration": 0.0009599999999999999, "duration_str": "960μs", "memory": 0, "memory_str": null, "filename": "HomeService.php:35", "source": {"index": 21, "namespace": null, "name": "app/Services/HomeService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\HomeService.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FServices%2FHomeService.php&line=35", "ajax": false, "filename": "HomeService.php", "line": "35"}, "connection": "ticketgol", "explain": null, "start_percent": 39.189, "width_percent": 4.75}, {"sql": "select `sluggable_id`, `slug`, `locale` from `slugs` where `locale` = 'en' and `slugs`.`sluggable_id` in (1, 2, 3, 4, 7) and `slugs`.`sluggable_type` = 'App\\\\Models\\\\League'", "type": "query", "params": [], "bindings": ["en", "App\\Models\\League"], "hints": null, "show_copy": true, "backtrace": [{"index": 26, "namespace": null, "name": "app/Services/HomeService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\HomeService.php", "line": 35}, {"index": 27, "namespace": null, "name": "app/Http/Controllers/Api/V1/HomeController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\HomeController.php", "line": 27}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 29, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}, {"index": 30, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingRoutingDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingRoutingDispatcher.php", "line": 35}], "start": **********.761691, "duration": 0.00116, "duration_str": "1.16ms", "memory": 0, "memory_str": null, "filename": "HomeService.php:35", "source": {"index": 26, "namespace": null, "name": "app/Services/HomeService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\HomeService.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FServices%2FHomeService.php&line=35", "ajax": false, "filename": "HomeService.php", "line": "35"}, "connection": "ticketgol", "explain": null, "start_percent": 43.939, "width_percent": 5.74}, {"sql": "select `sluggable_id`, `slug`, `locale` from `slugs` where `locale` = 'en' and `slugs`.`sluggable_id` in (4, 5, 6, 7, 9) and `slugs`.`sluggable_type` = 'App\\\\Models\\\\Event'", "type": "query", "params": [], "bindings": ["en", "App\\Models\\Event"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Services/HomeService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\HomeService.php", "line": 35}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Api/V1/HomeController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\HomeController.php", "line": 27}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 24, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}, {"index": 25, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingRoutingDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingRoutingDispatcher.php", "line": 35}], "start": **********.765994, "duration": 0.0010500000000000002, "duration_str": "1.05ms", "memory": 0, "memory_str": null, "filename": "HomeService.php:35", "source": {"index": 21, "namespace": null, "name": "app/Services/HomeService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\HomeService.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FServices%2FHomeService.php&line=35", "ajax": false, "filename": "HomeService.php", "line": "35"}, "connection": "ticketgol", "explain": null, "start_percent": 49.678, "width_percent": 5.195}, {"sql": "select count(*) as aggregate from (select `events`.`id`, `date`, `events`.`stadium_id`, `events`.`league_id`, `et`.`name` as `event_name`, `st`.`name` as `stadium_name`, `lt`.`name` as `league_name`, (select MIN(price) from `tickets` where `event_id` = `events`.`id` and `tickets`.`deleted_at` is null) as `min_price` from `events` left join `event_translations` as `et` on `events`.`id` = `et`.`event_id` and `et`.`locale` = 'en' left join `stadium_translations` as `st` on `events`.`stadium_id` = `st`.`stadium_id` and `st`.`locale` = 'en' left join `league_translations` as `lt` on `events`.`league_id` = `lt`.`league_id` and `lt`.`locale` = 'en' left join `club_translations` as `hc` on `events`.`home_club_id` = `hc`.`club_id` and `hc`.`locale` = 'en' left join `club_translations` as `gc` on `events`.`guest_club_id` = `gc`.`club_id` and `gc`.`locale` = 'en' where `is_published` = 1 and `date` >= '2025-07-02' and `is_feature_event` = 1 and `events`.`deleted_at` is null having `min_price` is not null) as `aggregate_table`", "type": "query", "params": [], "bindings": ["en", "en", "en", "en", "en", 1, "2025-07-02", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/HomeService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\HomeService.php", "line": 48}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/V1/HomeController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\HomeController.php", "line": 28}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 19, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}, {"index": 20, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingRoutingDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingRoutingDispatcher.php", "line": 35}], "start": **********.771073, "duration": 0.0015400000000000001, "duration_str": "1.54ms", "memory": 0, "memory_str": null, "filename": "HomeService.php:48", "source": {"index": 16, "namespace": null, "name": "app/Services/HomeService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\HomeService.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FServices%2FHomeService.php&line=48", "ajax": false, "filename": "HomeService.php", "line": "48"}, "connection": "ticketgol", "explain": null, "start_percent": 54.874, "width_percent": 7.62}, {"sql": "select `events`.`id`, `date`, `events`.`stadium_id`, `events`.`league_id`, `et`.`name` as `event_name`, `st`.`name` as `stadium_name`, `lt`.`name` as `league_name`, (select MIN(price) from `tickets` where `event_id` = `events`.`id` and `tickets`.`deleted_at` is null) as `min_price` from `events` left join `event_translations` as `et` on `events`.`id` = `et`.`event_id` and `et`.`locale` = 'en' left join `stadium_translations` as `st` on `events`.`stadium_id` = `st`.`stadium_id` and `st`.`locale` = 'en' left join `league_translations` as `lt` on `events`.`league_id` = `lt`.`league_id` and `lt`.`locale` = 'en' left join `club_translations` as `hc` on `events`.`home_club_id` = `hc`.`club_id` and `hc`.`locale` = 'en' left join `club_translations` as `gc` on `events`.`guest_club_id` = `gc`.`club_id` and `gc`.`locale` = 'en' where `is_published` = 1 and `date` >= '2025-07-02' and `is_feature_event` = 1 and `events`.`deleted_at` is null having `min_price` is not null limit 8 offset 0", "type": "query", "params": [], "bindings": ["en", "en", "en", "en", "en", 1, "2025-07-02", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/HomeService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\HomeService.php", "line": 48}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/V1/HomeController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\HomeController.php", "line": 28}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 19, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}, {"index": 20, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingRoutingDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingRoutingDispatcher.php", "line": 35}], "start": **********.7745419, "duration": 0.0014399999999999999, "duration_str": "1.44ms", "memory": 0, "memory_str": null, "filename": "HomeService.php:48", "source": {"index": 16, "namespace": null, "name": "app/Services/HomeService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\HomeService.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FServices%2FHomeService.php&line=48", "ajax": false, "filename": "HomeService.php", "line": "48"}, "connection": "ticketgol", "explain": null, "start_percent": 62.494, "width_percent": 7.125}, {"sql": "select * from `media` where `media`.`model_id` in (1, 2, 3) and `media`.`model_type` = 'App\\\\Models\\\\Event'", "type": "query", "params": [], "bindings": ["App\\Models\\Event"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Services/HomeService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\HomeService.php", "line": 48}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Api/V1/HomeController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\HomeController.php", "line": 28}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 24, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}, {"index": 25, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingRoutingDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingRoutingDispatcher.php", "line": 35}], "start": **********.778511, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "HomeService.php:48", "source": {"index": 21, "namespace": null, "name": "app/Services/HomeService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\HomeService.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FServices%2FHomeService.php&line=48", "ajax": false, "filename": "HomeService.php", "line": "48"}, "connection": "ticketgol", "explain": null, "start_percent": 69.619, "width_percent": 4.057}, {"sql": "select `id` from `stadiums` where `stadiums`.`id` in (6, 7, 9) and `stadiums`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Services/HomeService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\HomeService.php", "line": 48}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Api/V1/HomeController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\HomeController.php", "line": 28}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 24, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}, {"index": 25, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingRoutingDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingRoutingDispatcher.php", "line": 35}], "start": **********.781665, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "HomeService.php:48", "source": {"index": 21, "namespace": null, "name": "app/Services/HomeService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\HomeService.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FServices%2FHomeService.php&line=48", "ajax": false, "filename": "HomeService.php", "line": "48"}, "connection": "ticketgol", "explain": null, "start_percent": 73.676, "width_percent": 4.008}, {"sql": "select `sluggable_id`, `slug`, `locale` from `slugs` where `locale` = 'en' and `slugs`.`sluggable_id` in (6, 7, 9) and `slugs`.`sluggable_type` = 'App\\\\Models\\\\Stadium'", "type": "query", "params": [], "bindings": ["en", "App\\Models\\Stadium"], "hints": null, "show_copy": true, "backtrace": [{"index": 26, "namespace": null, "name": "app/Services/HomeService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\HomeService.php", "line": 48}, {"index": 27, "namespace": null, "name": "app/Http/Controllers/Api/V1/HomeController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\HomeController.php", "line": 28}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 29, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}, {"index": 30, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingRoutingDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingRoutingDispatcher.php", "line": 35}], "start": **********.7855232, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "HomeService.php:48", "source": {"index": 26, "namespace": null, "name": "app/Services/HomeService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\HomeService.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FServices%2FHomeService.php&line=48", "ajax": false, "filename": "HomeService.php", "line": "48"}, "connection": "ticketgol", "explain": null, "start_percent": 77.684, "width_percent": 4.8}, {"sql": "select `id` from `leagues` where `leagues`.`id` in (3, 7) and `leagues`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Services/HomeService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\HomeService.php", "line": 48}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Api/V1/HomeController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\HomeController.php", "line": 28}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 24, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}, {"index": 25, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingRoutingDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingRoutingDispatcher.php", "line": 35}], "start": **********.789356, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "HomeService.php:48", "source": {"index": 21, "namespace": null, "name": "app/Services/HomeService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\HomeService.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FServices%2FHomeService.php&line=48", "ajax": false, "filename": "HomeService.php", "line": "48"}, "connection": "ticketgol", "explain": null, "start_percent": 82.484, "width_percent": 3.216}, {"sql": "select `sluggable_id`, `slug`, `locale` from `slugs` where `locale` = 'en' and `slugs`.`sluggable_id` in (3, 7) and `slugs`.`sluggable_type` = 'App\\\\Models\\\\League'", "type": "query", "params": [], "bindings": ["en", "App\\Models\\League"], "hints": null, "show_copy": true, "backtrace": [{"index": 26, "namespace": null, "name": "app/Services/HomeService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\HomeService.php", "line": 48}, {"index": 27, "namespace": null, "name": "app/Http/Controllers/Api/V1/HomeController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\HomeController.php", "line": 28}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 29, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}, {"index": 30, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingRoutingDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingRoutingDispatcher.php", "line": 35}], "start": **********.793286, "duration": 0.00125, "duration_str": "1.25ms", "memory": 0, "memory_str": null, "filename": "HomeService.php:48", "source": {"index": 26, "namespace": null, "name": "app/Services/HomeService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\HomeService.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FServices%2FHomeService.php&line=48", "ajax": false, "filename": "HomeService.php", "line": "48"}, "connection": "ticketgol", "explain": null, "start_percent": 85.7, "width_percent": 6.185}, {"sql": "select `sluggable_id`, `slug`, `locale` from `slugs` where `locale` = 'en' and `slugs`.`sluggable_id` in (1, 2, 3) and `slugs`.`sluggable_type` = 'App\\\\Models\\\\Event'", "type": "query", "params": [], "bindings": ["en", "App\\Models\\Event"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Services/HomeService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\HomeService.php", "line": 48}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Api/V1/HomeController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\HomeController.php", "line": 28}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 24, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}, {"index": 25, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingRoutingDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingRoutingDispatcher.php", "line": 35}], "start": **********.797195, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "HomeService.php:48", "source": {"index": 21, "namespace": null, "name": "app/Services/HomeService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\HomeService.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FServices%2FHomeService.php&line=48", "ajax": false, "filename": "HomeService.php", "line": "48"}, "connection": "ticketgol", "explain": null, "start_percent": 91.885, "width_percent": 4.552}, {"sql": "update `sessions` set `payload` = 'YTozOntzOjY6Il90b2tlbiI7czo0MDoidFVWWWE4NzJCUm9BUzlpWGRnczM5alhDSGpwMURXU2l5Ykx5cWFyciI7czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6MjE6Imh0dHA6Ly90aWNrZXRnb2wudGVzdCI7fX0=', `last_activity` = **********, `user_id` = null, `ip_address` = '127.0.0.1', `user_agent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' where `id` = 'CFoDwus13aj6e2xLfOgPLTvuRWf27pkZlMJ6n5jN'", "type": "query", "params": [], "bindings": ["YTozOntzOjY6Il90b2tlbiI7czo0MDoidFVWWWE4NzJCUm9BUzlpWGRnczM5alhDSGpwMURXU2l5Ykx5cWFyciI7czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6MjE6Imh0dHA6Ly90aWNrZXRnb2wudGVzdCI7fX0=", **********, null, "127.0.0.1", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "CFoDwus13aj6e2xLfOgPLTvuRWf27pkZlMJ6n5jN"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 173}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 140}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 175}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 245}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 130}], "start": **********.805726, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:173", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 173}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=173", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "173"}, "connection": "ticketgol", "explain": null, "start_percent": 96.437, "width_percent": 3.563}]}, "models": {"data": {"App\\Models\\Slug": {"value": 21, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FSlug.php&line=1", "ajax": false, "filename": "Slug.php", "line": "?"}}, "App\\Models\\Event": {"value": 8, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FEvent.php&line=1", "ajax": false, "filename": "Event.php", "line": "?"}}, "App\\Models\\League": {"value": 7, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FLeague.php&line=1", "ajax": false, "filename": "League.php", "line": "?"}}, "App\\Models\\Stadium": {"value": 6, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FStadium.php&line=1", "ajax": false, "filename": "Stadium.php", "line": "?"}}, "Spatie\\MediaLibrary\\MediaCollections\\Models\\Media": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Fspatie%2Flaravel-medialibrary%2Fsrc%2FMediaCollections%2FModels%2FMedia.php&line=1", "ajax": false, "filename": "Media.php", "line": "?"}}}, "count": 44, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "tUVYa872BRoAS9iXdgs39jXCHjp1DWSiybLyqarr", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://ticketgol.test\"\n]"}, "request": {"telescope": "<a href=\"http://ticketgol.test/_debugbar/telescope/9f4be449-6eb7-4f3d-a3aa-797e1471d3bf\" target=\"_blank\">View in Telescope</a>", "path_info": "/api/v1/home", "status_code": "<pre class=sf-dump id=sf-dump-594422928 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-594422928\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-2029886181 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2029886181\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-121648129 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-121648129\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1952237332 data-indent-pad=\"  \"><span class=sf-dump-note>array:11</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"853 characters\">ajs_anonymous_id=%2205cf8d6b-8a14-40b9-9d4f-c4ebd1620a6e%22; selected_locale=en; __stripe_mid=fd60eaa0-e90c-4914-bb27-ce58ed87fc6066be49; XSRF-TOKEN=eyJpdiI6IjJaemxtcGNXMW1sSHBWTWlWZHFmUGc9PSIsInZhbHVlIjoiQi9NeXhNVzVXZ2ZKVlZIeHovOFdaZllST3Z5bWZFSENvUkZKMkNCeVdZVkxweWg0ZVRWUkErQVo4NnBDeDgrcjRjYUY0RUEvQlV1SVpySE5vcXRza1pqWi9KM3NXcVlzWUNLRWhnOTdJTjltYm1JYUxSeE16eVJYNGdjeUVyYjciLCJtYWMiOiJmNTc5NDZiM2FkM2UxNjcwMzNkNjljNDUyNjNlNzczMjQyNzQzZmQzNGE1MjlkZTNlOTc2OGZkMWE0OWRkYWI2IiwidGFnIjoiIn0%3D; ticketgol_session=eyJpdiI6ImwzeGRBTWZZVFdDNU1Cbzd5OUxlZnc9PSIsInZhbHVlIjoiK1lWdzJxbEdzcUwwTHdoQ1JFYUk5bHFtODZjVnlaQjRjWnlUZ3ltVnQwY2Q2ZjdnL1pwTitjcU5DaWdjdjFtWFRCczBxSFhGbHFldWc0RmswOVZUL2I1MkVFcW5RRnUrTldLenJMWnJZNGtTVjkrbFpkQjh0S0dkRFhGQXpkNnkiLCJtYWMiOiI5MTZlMTRkMGVlM2RhMDlhMTM4ZTFhODQ4NzRkOWQ0ZTkwZTdlZGFmMjFmYTA0MWFjNTdmZTVkYjk2MzA0NGY1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">en-GB,en;q=0.9,es;q=0.8,en-US;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://ticketgol.test/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-locale</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IjJaemxtcGNXMW1sSHBWTWlWZHFmUGc9PSIsInZhbHVlIjoiQi9NeXhNVzVXZ2ZKVlZIeHovOFdaZllST3Z5bWZFSENvUkZKMkNCeVdZVkxweWg0ZVRWUkErQVo4NnBDeDgrcjRjYUY0RUEvQlV1SVpySE5vcXRza1pqWi9KM3NXcVlzWUNLRWhnOTdJTjltYm1JYUxSeE16eVJYNGdjeUVyYjciLCJtYWMiOiJmNTc5NDZiM2FkM2UxNjcwMzNkNjljNDUyNjNlNzczMjQyNzQzZmQzNGE1MjlkZTNlOTc2OGZkMWE0OWRkYWI2IiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">ticketgol.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1952237332\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-418514271 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>ajs_anonymous_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>selected_locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">tUVYa872BRoAS9iXdgs39jXCHjp1DWSiybLyqarr</span>\"\n  \"<span class=sf-dump-key>ticketgol_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">CFoDwus13aj6e2xLfOgPLTvuRWf27pkZlMJ6n5jN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-418514271\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1626429400 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 02 Jul 2025 19:03:33 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IkxobGk2ZDU0MWtPOG5IWHlGMWhxeEE9PSIsInZhbHVlIjoiQmlmNG5pTm1TTm5rL1ViQjZQWVJoSGJhN3UvazY3RnhNTVRDR25SenVCVGR6NVpuNi9xMU50L1FSMGlHU0kxS0FQaG9kZEJVYjRwTU83RXM3MVZhVktKV01lQ0lhZjNRakJveXhYWFlESEg1Rm1OOXNDcTEzSFVmcmM4WDBKRU4iLCJtYWMiOiJkMjE0NzkzMDVmODg0YjdhYmQzOWY3YTc4ZWE2OTllZTc4OGY5ODY3NGIxZDkzZmJjYWEwNzdmNDlkOTJiYjg1IiwidGFnIjoiIn0%3D; expires=Wed, 02 Jul 2025 21:03:33 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"445 characters\">ticketgol_session=eyJpdiI6IlV2Wk9wY3VhN0dqdXRCL3hSMFVZZEE9PSIsInZhbHVlIjoiRkJhR2dlMjl4b0VJN3RGbm55MW9Zbmh0YVdaaWdnaUFTTFFZQlFrNEJDdFQ2SDRVYUMwMExEQmhQNnI1MlV3cHpOY0RJNk90NFgyd0tYU09RdkJOc2duZitmeElDbjB3dC9BbjdPcXN5VnY5aFRjYmVOaHZIWFNQYVgrZjVFdngiLCJtYWMiOiI3ZDlmNDljM2M1ZTI5ODM3ZWZiYzI5MDQzZDMxNzA5YjhiMGUzNmQwYTFkYzRlOWU4Y2Q5ODdkODc2NzM2N2NjIiwidGFnIjoiIn0%3D; expires=Wed, 02 Jul 2025 21:03:33 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IkxobGk2ZDU0MWtPOG5IWHlGMWhxeEE9PSIsInZhbHVlIjoiQmlmNG5pTm1TTm5rL1ViQjZQWVJoSGJhN3UvazY3RnhNTVRDR25SenVCVGR6NVpuNi9xMU50L1FSMGlHU0kxS0FQaG9kZEJVYjRwTU83RXM3MVZhVktKV01lQ0lhZjNRakJveXhYWFlESEg1Rm1OOXNDcTEzSFVmcmM4WDBKRU4iLCJtYWMiOiJkMjE0NzkzMDVmODg0YjdhYmQzOWY3YTc4ZWE2OTllZTc4OGY5ODY3NGIxZDkzZmJjYWEwNzdmNDlkOTJiYjg1IiwidGFnIjoiIn0%3D; expires=Wed, 02-Jul-2025 21:03:33 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"417 characters\">ticketgol_session=eyJpdiI6IlV2Wk9wY3VhN0dqdXRCL3hSMFVZZEE9PSIsInZhbHVlIjoiRkJhR2dlMjl4b0VJN3RGbm55MW9Zbmh0YVdaaWdnaUFTTFFZQlFrNEJDdFQ2SDRVYUMwMExEQmhQNnI1MlV3cHpOY0RJNk90NFgyd0tYU09RdkJOc2duZitmeElDbjB3dC9BbjdPcXN5VnY5aFRjYmVOaHZIWFNQYVgrZjVFdngiLCJtYWMiOiI3ZDlmNDljM2M1ZTI5ODM3ZWZiYzI5MDQzZDMxNzA5YjhiMGUzNmQwYTFkYzRlOWU4Y2Q5ODdkODc2NzM2N2NjIiwidGFnIjoiIn0%3D; expires=Wed, 02-Jul-2025 21:03:33 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1626429400\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1713249061 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">tUVYa872BRoAS9iXdgs39jXCHjp1DWSiybLyqarr</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://ticketgol.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1713249061\", {\"maxDepth\":0})</script>\n"}}