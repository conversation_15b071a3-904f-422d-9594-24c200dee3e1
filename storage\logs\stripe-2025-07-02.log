[2025-07-02 17:40:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 17:40:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 17:40:01] local.INFO: <PERSON>ron job executed {"time":"2025-07-02 17:40:01"} 
[2025-07-02 17:40:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 17:40:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 17:40:02] local.INFO: Cron job executed {"time":"2025-07-02 17:40:02"} 
[2025-07-02 17:41:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 17:41:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 17:41:01] local.INFO: Cron job executed {"time":"2025-07-02 17:41:01"} 
[2025-07-02 17:41:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 17:41:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 17:41:03] local.INFO: Cron job executed {"time":"2025-07-02 17:41:03"} 
[2025-07-02 17:42:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 17:42:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 17:42:01] local.INFO: Cron job executed {"time":"2025-07-02 17:42:01"} 
[2025-07-02 17:42:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 17:42:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 17:42:02] local.INFO: Cron job executed {"time":"2025-07-02 17:42:02"} 
[2025-07-02 17:43:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 17:43:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 17:43:01] local.INFO: Cron job executed {"time":"2025-07-02 17:43:01"} 
[2025-07-02 17:43:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 17:43:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 17:43:02] local.INFO: Cron job executed {"time":"2025-07-02 17:43:02"} 
[2025-07-02 17:44:02] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 17:44:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 17:44:02] local.INFO: Cron job executed {"time":"2025-07-02 17:44:02"} 
[2025-07-02 17:44:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 17:44:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 17:44:03] local.INFO: Cron job executed {"time":"2025-07-02 17:44:03"} 
[2025-07-02 17:45:02] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 17:45:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 17:45:02] local.INFO: Cron job executed {"time":"2025-07-02 17:45:02"} 
[2025-07-02 17:45:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 17:45:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 17:45:03] local.INFO: Cron job executed {"time":"2025-07-02 17:45:03"} 
[2025-07-02 17:46:02] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 17:46:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 17:46:02] local.INFO: Cron job executed {"time":"2025-07-02 17:46:02"} 
[2025-07-02 17:46:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 17:46:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 17:46:03] local.INFO: Cron job executed {"time":"2025-07-02 17:46:03"} 
[2025-07-02 17:47:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 17:47:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 17:47:01] local.INFO: Cron job executed {"time":"2025-07-02 17:47:01"} 
[2025-07-02 17:47:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 17:47:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 17:47:02] local.INFO: Cron job executed {"time":"2025-07-02 17:47:02"} 
[2025-07-02 17:48:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 17:48:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 17:48:01] local.INFO: Cron job executed {"time":"2025-07-02 17:48:01"} 
[2025-07-02 17:48:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 17:48:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 17:48:02] local.INFO: Cron job executed {"time":"2025-07-02 17:48:02"} 
[2025-07-02 17:49:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 17:49:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 17:49:01] local.INFO: Cron job executed {"time":"2025-07-02 17:49:01"} 
[2025-07-02 17:49:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 17:49:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 17:49:03] local.INFO: Cron job executed {"time":"2025-07-02 17:49:03"} 
[2025-07-02 17:50:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 17:50:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 17:50:01] local.INFO: Cron job executed {"time":"2025-07-02 17:50:01"} 
[2025-07-02 17:50:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 17:50:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 17:50:02] local.INFO: Cron job executed {"time":"2025-07-02 17:50:02"} 
[2025-07-02 17:51:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 17:51:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 17:51:01] local.INFO: Cron job executed {"time":"2025-07-02 17:51:01"} 
[2025-07-02 17:51:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 17:51:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 17:51:02] local.INFO: Cron job executed {"time":"2025-07-02 17:51:02"} 
[2025-07-02 17:52:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 17:52:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 17:52:01] local.INFO: Cron job executed {"time":"2025-07-02 17:52:01"} 
[2025-07-02 17:52:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 17:52:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 17:52:02] local.INFO: Cron job executed {"time":"2025-07-02 17:52:02"} 
[2025-07-02 17:53:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 17:53:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 17:53:01] local.INFO: Cron job executed {"time":"2025-07-02 17:53:01"} 
[2025-07-02 17:53:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 17:53:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 17:53:02] local.INFO: Cron job executed {"time":"2025-07-02 17:53:02"} 
[2025-07-02 17:54:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 17:54:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 17:54:01] local.INFO: Cron job executed {"time":"2025-07-02 17:54:01"} 
[2025-07-02 17:54:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 17:54:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 17:54:02] local.INFO: Cron job executed {"time":"2025-07-02 17:54:02"} 
[2025-07-02 17:55:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 17:55:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 17:55:01] local.INFO: Cron job executed {"time":"2025-07-02 17:55:01"} 
[2025-07-02 17:55:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 17:55:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 17:55:02] local.INFO: Cron job executed {"time":"2025-07-02 17:55:02"} 
[2025-07-02 17:56:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 17:56:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 17:56:01] local.INFO: Cron job executed {"time":"2025-07-02 17:56:01"} 
[2025-07-02 17:56:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 17:56:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 17:56:02] local.INFO: Cron job executed {"time":"2025-07-02 17:56:02"} 
[2025-07-02 17:57:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 17:57:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 17:57:01] local.INFO: Cron job executed {"time":"2025-07-02 17:57:01"} 
[2025-07-02 17:57:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 17:57:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 17:57:02] local.INFO: Cron job executed {"time":"2025-07-02 17:57:02"} 
[2025-07-02 17:58:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 17:58:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 17:58:01] local.INFO: Cron job executed {"time":"2025-07-02 17:58:01"} 
[2025-07-02 17:58:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 17:58:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 17:58:02] local.INFO: Cron job executed {"time":"2025-07-02 17:58:02"} 
[2025-07-02 17:59:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 17:59:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 17:59:01] local.INFO: Cron job executed {"time":"2025-07-02 17:59:01"} 
[2025-07-02 17:59:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 17:59:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 17:59:02] local.INFO: Cron job executed {"time":"2025-07-02 17:59:02"} 
[2025-07-02 18:00:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:00:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:00:01] local.INFO: Cron job executed {"time":"2025-07-02 18:00:01"} 
[2025-07-02 18:00:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:00:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:00:02] local.INFO: Cron job executed {"time":"2025-07-02 18:00:02"} 
[2025-07-02 18:01:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:01:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:01:01] local.INFO: Cron job executed {"time":"2025-07-02 18:01:01"} 
[2025-07-02 18:01:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:01:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:01:02] local.INFO: Cron job executed {"time":"2025-07-02 18:01:02"} 
[2025-07-02 18:02:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:02:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:02:01] local.INFO: Cron job executed {"time":"2025-07-02 18:02:01"} 
[2025-07-02 18:02:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:02:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:02:02] local.INFO: Cron job executed {"time":"2025-07-02 18:02:02"} 
[2025-07-02 18:03:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:03:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:03:01] local.INFO: Cron job executed {"time":"2025-07-02 18:03:01"} 
[2025-07-02 18:03:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:03:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:03:02] local.INFO: Cron job executed {"time":"2025-07-02 18:03:02"} 
[2025-07-02 18:04:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:04:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:04:01] local.INFO: Cron job executed {"time":"2025-07-02 18:04:01"} 
[2025-07-02 18:04:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:04:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:04:02] local.INFO: Cron job executed {"time":"2025-07-02 18:04:02"} 
[2025-07-02 18:05:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:05:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:05:01] local.INFO: Cron job executed {"time":"2025-07-02 18:05:01"} 
[2025-07-02 18:05:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:05:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:05:02] local.INFO: Cron job executed {"time":"2025-07-02 18:05:02"} 
[2025-07-02 18:06:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:06:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:06:01] local.INFO: Cron job executed {"time":"2025-07-02 18:06:01"} 
[2025-07-02 18:06:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:06:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:06:02] local.INFO: Cron job executed {"time":"2025-07-02 18:06:02"} 
[2025-07-02 18:07:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:07:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:07:01] local.INFO: Cron job executed {"time":"2025-07-02 18:07:01"} 
[2025-07-02 18:07:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:07:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:07:02] local.INFO: Cron job executed {"time":"2025-07-02 18:07:02"} 
[2025-07-02 18:08:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:08:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:08:01] local.INFO: Cron job executed {"time":"2025-07-02 18:08:01"} 
[2025-07-02 18:08:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:08:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:08:02] local.INFO: Cron job executed {"time":"2025-07-02 18:08:02"} 
[2025-07-02 18:09:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:09:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:09:01] local.INFO: Cron job executed {"time":"2025-07-02 18:09:01"} 
[2025-07-02 18:09:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:09:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:09:02] local.INFO: Cron job executed {"time":"2025-07-02 18:09:02"} 
[2025-07-02 18:10:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:10:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:10:01] local.INFO: Cron job executed {"time":"2025-07-02 18:10:01"} 
[2025-07-02 18:10:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:10:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:10:02] local.INFO: Cron job executed {"time":"2025-07-02 18:10:02"} 
[2025-07-02 18:11:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:11:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:11:01] local.INFO: Cron job executed {"time":"2025-07-02 18:11:01"} 
[2025-07-02 18:11:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:11:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:11:02] local.INFO: Cron job executed {"time":"2025-07-02 18:11:02"} 
[2025-07-02 18:12:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:12:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:12:01] local.INFO: Cron job executed {"time":"2025-07-02 18:12:01"} 
[2025-07-02 18:12:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:12:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:12:02] local.INFO: Cron job executed {"time":"2025-07-02 18:12:02"} 
[2025-07-02 18:13:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:13:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:13:01] local.INFO: Cron job executed {"time":"2025-07-02 18:13:01"} 
[2025-07-02 18:13:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:13:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:13:02] local.INFO: Cron job executed {"time":"2025-07-02 18:13:02"} 
[2025-07-02 18:14:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:14:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:14:01] local.INFO: Cron job executed {"time":"2025-07-02 18:14:01"} 
[2025-07-02 18:14:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:14:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:14:03] local.INFO: Cron job executed {"time":"2025-07-02 18:14:03"} 
[2025-07-02 18:15:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:15:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:15:01] local.INFO: Cron job executed {"time":"2025-07-02 18:15:01"} 
[2025-07-02 18:15:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:15:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:15:03] local.INFO: Cron job executed {"time":"2025-07-02 18:15:03"} 
[2025-07-02 18:16:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:16:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:16:01] local.INFO: Cron job executed {"time":"2025-07-02 18:16:01"} 
[2025-07-02 18:16:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:16:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:16:03] local.INFO: Cron job executed {"time":"2025-07-02 18:16:03"} 
[2025-07-02 18:17:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:17:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:17:01] local.INFO: Cron job executed {"time":"2025-07-02 18:17:01"} 
[2025-07-02 18:17:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:17:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:17:02] local.INFO: Cron job executed {"time":"2025-07-02 18:17:02"} 
[2025-07-02 18:18:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:18:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:18:01] local.INFO: Cron job executed {"time":"2025-07-02 18:18:01"} 
[2025-07-02 18:18:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:18:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:18:02] local.INFO: Cron job executed {"time":"2025-07-02 18:18:02"} 
[2025-07-02 18:19:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:19:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:19:01] local.INFO: Cron job executed {"time":"2025-07-02 18:19:01"} 
[2025-07-02 18:19:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:19:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:19:02] local.INFO: Cron job executed {"time":"2025-07-02 18:19:02"} 
[2025-07-02 18:20:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:20:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:20:01] local.INFO: Cron job executed {"time":"2025-07-02 18:20:01"} 
[2025-07-02 18:20:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:20:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:20:02] local.INFO: Cron job executed {"time":"2025-07-02 18:20:02"} 
[2025-07-02 18:21:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:21:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:21:01] local.INFO: Cron job executed {"time":"2025-07-02 18:21:01"} 
[2025-07-02 18:21:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:21:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:21:02] local.INFO: Cron job executed {"time":"2025-07-02 18:21:02"} 
[2025-07-02 18:22:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:22:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:22:01] local.INFO: Cron job executed {"time":"2025-07-02 18:22:01"} 
[2025-07-02 18:22:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:22:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:22:02] local.INFO: Cron job executed {"time":"2025-07-02 18:22:02"} 
[2025-07-02 18:23:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:23:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:23:01] local.INFO: Cron job executed {"time":"2025-07-02 18:23:01"} 
[2025-07-02 18:23:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:23:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:23:02] local.INFO: Cron job executed {"time":"2025-07-02 18:23:02"} 
[2025-07-02 18:24:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:24:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:24:01] local.INFO: Cron job executed {"time":"2025-07-02 18:24:01"} 
[2025-07-02 18:24:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:24:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:24:02] local.INFO: Cron job executed {"time":"2025-07-02 18:24:02"} 
[2025-07-02 18:25:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:25:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:25:01] local.INFO: Cron job executed {"time":"2025-07-02 18:25:01"} 
[2025-07-02 18:25:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:25:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:25:02] local.INFO: Cron job executed {"time":"2025-07-02 18:25:02"} 
[2025-07-02 18:26:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:26:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:26:01] local.INFO: Cron job executed {"time":"2025-07-02 18:26:01"} 
[2025-07-02 18:26:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:26:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:26:02] local.INFO: Cron job executed {"time":"2025-07-02 18:26:02"} 
[2025-07-02 18:27:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:27:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:27:01] local.INFO: Cron job executed {"time":"2025-07-02 18:27:01"} 
[2025-07-02 18:27:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:27:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:27:02] local.INFO: Cron job executed {"time":"2025-07-02 18:27:02"} 
[2025-07-02 18:28:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:28:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:28:01] local.INFO: Cron job executed {"time":"2025-07-02 18:28:01"} 
[2025-07-02 18:28:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:28:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:28:03] local.INFO: Cron job executed {"time":"2025-07-02 18:28:03"} 
[2025-07-02 18:29:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:29:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:29:01] local.INFO: Cron job executed {"time":"2025-07-02 18:29:01"} 
[2025-07-02 18:29:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:29:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:29:03] local.INFO: Cron job executed {"time":"2025-07-02 18:29:03"} 
[2025-07-02 18:30:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:30:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:30:01] local.INFO: Cron job executed {"time":"2025-07-02 18:30:01"} 
[2025-07-02 18:30:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:30:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:30:03] local.INFO: Cron job executed {"time":"2025-07-02 18:30:03"} 
[2025-07-02 18:31:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:31:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:31:01] local.INFO: Cron job executed {"time":"2025-07-02 18:31:01"} 
[2025-07-02 18:31:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:31:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:31:03] local.INFO: Cron job executed {"time":"2025-07-02 18:31:03"} 
[2025-07-02 18:32:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:32:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:32:01] local.INFO: Cron job executed {"time":"2025-07-02 18:32:01"} 
[2025-07-02 18:32:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:32:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:32:02] local.INFO: Cron job executed {"time":"2025-07-02 18:32:02"} 
[2025-07-02 18:33:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:33:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:33:01] local.INFO: Cron job executed {"time":"2025-07-02 18:33:01"} 
[2025-07-02 18:33:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:33:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:33:02] local.INFO: Cron job executed {"time":"2025-07-02 18:33:02"} 
[2025-07-02 18:34:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:34:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:34:01] local.INFO: Cron job executed {"time":"2025-07-02 18:34:01"} 
[2025-07-02 18:34:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:34:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:34:02] local.INFO: Cron job executed {"time":"2025-07-02 18:34:02"} 
[2025-07-02 18:35:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:35:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:35:01] local.INFO: Cron job executed {"time":"2025-07-02 18:35:01"} 
[2025-07-02 18:35:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:35:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:35:02] local.INFO: Cron job executed {"time":"2025-07-02 18:35:02"} 
[2025-07-02 18:36:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:36:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:36:01] local.INFO: Cron job executed {"time":"2025-07-02 18:36:01"} 
[2025-07-02 18:36:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:36:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:36:02] local.INFO: Cron job executed {"time":"2025-07-02 18:36:02"} 
[2025-07-02 18:37:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:37:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:37:01] local.INFO: Cron job executed {"time":"2025-07-02 18:37:01"} 
[2025-07-02 18:37:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:37:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:37:02] local.INFO: Cron job executed {"time":"2025-07-02 18:37:02"} 
[2025-07-02 18:38:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:38:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:38:01] local.INFO: Cron job executed {"time":"2025-07-02 18:38:01"} 
[2025-07-02 18:38:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:38:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:38:02] local.INFO: Cron job executed {"time":"2025-07-02 18:38:02"} 
[2025-07-02 18:39:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:39:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:39:01] local.INFO: Cron job executed {"time":"2025-07-02 18:39:01"} 
[2025-07-02 18:39:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:39:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:39:02] local.INFO: Cron job executed {"time":"2025-07-02 18:39:02"} 
[2025-07-02 18:40:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:40:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:40:01] local.INFO: Cron job executed {"time":"2025-07-02 18:40:01"} 
[2025-07-02 18:40:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:40:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:40:02] local.INFO: Cron job executed {"time":"2025-07-02 18:40:02"} 
[2025-07-02 18:41:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:41:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:41:01] local.INFO: Cron job executed {"time":"2025-07-02 18:41:01"} 
[2025-07-02 18:41:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:41:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:41:02] local.INFO: Cron job executed {"time":"2025-07-02 18:41:02"} 
[2025-07-02 18:42:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:42:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:42:01] local.INFO: Cron job executed {"time":"2025-07-02 18:42:01"} 
[2025-07-02 18:42:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:42:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:42:03] local.INFO: Cron job executed {"time":"2025-07-02 18:42:03"} 
[2025-07-02 18:43:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:43:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:43:01] local.INFO: Cron job executed {"time":"2025-07-02 18:43:01"} 
[2025-07-02 18:43:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:43:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:43:03] local.INFO: Cron job executed {"time":"2025-07-02 18:43:03"} 
[2025-07-02 18:44:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:44:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:44:01] local.INFO: Cron job executed {"time":"2025-07-02 18:44:01"} 
[2025-07-02 18:44:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:44:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:44:02] local.INFO: Cron job executed {"time":"2025-07-02 18:44:02"} 
[2025-07-02 18:45:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:45:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:45:01] local.INFO: Cron job executed {"time":"2025-07-02 18:45:01"} 
[2025-07-02 18:45:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:45:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:45:02] local.INFO: Cron job executed {"time":"2025-07-02 18:45:02"} 
[2025-07-02 18:46:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:46:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:46:01] local.INFO: Cron job executed {"time":"2025-07-02 18:46:01"} 
[2025-07-02 18:46:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:46:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:46:02] local.INFO: Cron job executed {"time":"2025-07-02 18:46:02"} 
[2025-07-02 18:47:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:47:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:47:01] local.INFO: Cron job executed {"time":"2025-07-02 18:47:01"} 
[2025-07-02 18:47:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:47:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:47:02] local.INFO: Cron job executed {"time":"2025-07-02 18:47:02"} 
[2025-07-02 18:48:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:48:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:48:01] local.INFO: Cron job executed {"time":"2025-07-02 18:48:01"} 
[2025-07-02 18:48:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:48:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:48:03] local.INFO: Cron job executed {"time":"2025-07-02 18:48:03"} 
[2025-07-02 18:49:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:49:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:49:01] local.INFO: Cron job executed {"time":"2025-07-02 18:49:01"} 
[2025-07-02 18:49:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:49:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:49:02] local.INFO: Cron job executed {"time":"2025-07-02 18:49:02"} 
[2025-07-02 18:50:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:50:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:50:01] local.INFO: Cron job executed {"time":"2025-07-02 18:50:01"} 
[2025-07-02 18:50:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:50:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:50:03] local.INFO: Cron job executed {"time":"2025-07-02 18:50:03"} 
[2025-07-02 18:51:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:51:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:51:01] local.INFO: Cron job executed {"time":"2025-07-02 18:51:01"} 
[2025-07-02 18:51:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:51:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:51:02] local.INFO: Cron job executed {"time":"2025-07-02 18:51:02"} 
[2025-07-02 18:52:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:52:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:52:01] local.INFO: Cron job executed {"time":"2025-07-02 18:52:01"} 
[2025-07-02 18:52:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:52:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:52:02] local.INFO: Cron job executed {"time":"2025-07-02 18:52:02"} 
[2025-07-02 18:53:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:53:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:53:01] local.INFO: Cron job executed {"time":"2025-07-02 18:53:01"} 
[2025-07-02 18:53:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:53:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:53:02] local.INFO: Cron job executed {"time":"2025-07-02 18:53:02"} 
[2025-07-02 18:54:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:54:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:54:01] local.INFO: Cron job executed {"time":"2025-07-02 18:54:01"} 
[2025-07-02 18:54:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:54:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:54:02] local.INFO: Cron job executed {"time":"2025-07-02 18:54:02"} 
[2025-07-02 18:55:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:55:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:55:01] local.INFO: Cron job executed {"time":"2025-07-02 18:55:01"} 
[2025-07-02 18:55:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:55:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:55:02] local.INFO: Cron job executed {"time":"2025-07-02 18:55:02"} 
[2025-07-02 18:56:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:56:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:56:01] local.INFO: Cron job executed {"time":"2025-07-02 18:56:01"} 
[2025-07-02 18:56:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:56:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:56:02] local.INFO: Cron job executed {"time":"2025-07-02 18:56:02"} 
[2025-07-02 18:57:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:57:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:57:01] local.INFO: Cron job executed {"time":"2025-07-02 18:57:01"} 
[2025-07-02 18:57:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:57:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:57:02] local.INFO: Cron job executed {"time":"2025-07-02 18:57:02"} 
[2025-07-02 18:58:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:58:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:58:01] local.INFO: Cron job executed {"time":"2025-07-02 18:58:01"} 
[2025-07-02 18:58:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:58:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:58:03] local.INFO: Cron job executed {"time":"2025-07-02 18:58:03"} 
[2025-07-02 18:59:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:59:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:59:01] local.INFO: Cron job executed {"time":"2025-07-02 18:59:01"} 
[2025-07-02 18:59:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 18:59:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 18:59:03] local.INFO: Cron job executed {"time":"2025-07-02 18:59:03"} 
[2025-07-02 19:00:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 19:00:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 19:00:01] local.INFO: Cron job executed {"time":"2025-07-02 19:00:01"} 
[2025-07-02 19:00:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 19:00:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 19:00:02] local.INFO: Cron job executed {"time":"2025-07-02 19:00:02"} 
[2025-07-02 19:01:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 19:01:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 19:01:01] local.INFO: Cron job executed {"time":"2025-07-02 19:01:01"} 
[2025-07-02 19:01:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 19:01:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 19:01:02] local.INFO: Cron job executed {"time":"2025-07-02 19:01:02"} 
[2025-07-02 19:02:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 19:02:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 19:02:01] local.INFO: Cron job executed {"time":"2025-07-02 19:02:01"} 
[2025-07-02 19:02:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 19:02:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 19:02:03] local.INFO: Cron job executed {"time":"2025-07-02 19:02:03"} 
[2025-07-02 19:03:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 19:03:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 19:03:01] local.INFO: Cron job executed {"time":"2025-07-02 19:03:01"} 
[2025-07-02 19:03:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 19:03:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 19:03:02] local.INFO: Cron job executed {"time":"2025-07-02 19:03:02"} 
[2025-07-02 19:04:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 19:04:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 19:04:01] local.INFO: Cron job executed {"time":"2025-07-02 19:04:01"} 
[2025-07-02 19:04:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 19:04:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 19:04:03] local.INFO: Cron job executed {"time":"2025-07-02 19:04:03"} 
[2025-07-02 19:05:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 19:05:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 19:05:01] local.INFO: Cron job executed {"time":"2025-07-02 19:05:01"} 
[2025-07-02 19:05:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 19:05:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 19:05:02] local.INFO: Cron job executed {"time":"2025-07-02 19:05:02"} 
[2025-07-02 19:06:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 19:06:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 19:06:01] local.INFO: Cron job executed {"time":"2025-07-02 19:06:01"} 
[2025-07-02 19:06:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 19:06:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 19:06:03] local.INFO: Cron job executed {"time":"2025-07-02 19:06:03"} 
[2025-07-02 19:07:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 19:07:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 19:07:01] local.INFO: Cron job executed {"time":"2025-07-02 19:07:01"} 
[2025-07-02 19:07:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 19:07:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 19:07:02] local.INFO: Cron job executed {"time":"2025-07-02 19:07:02"} 
[2025-07-02 19:08:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 19:08:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 19:08:01] local.INFO: Cron job executed {"time":"2025-07-02 19:08:01"} 
[2025-07-02 19:08:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 19:08:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 19:08:02] local.INFO: Cron job executed {"time":"2025-07-02 19:08:02"} 
[2025-07-02 19:09:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 19:09:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 19:09:01] local.INFO: Cron job executed {"time":"2025-07-02 19:09:01"} 
[2025-07-02 19:09:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 19:09:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 19:09:02] local.INFO: Cron job executed {"time":"2025-07-02 19:09:02"} 
[2025-07-02 19:10:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 19:10:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 19:10:01] local.INFO: Cron job executed {"time":"2025-07-02 19:10:01"} 
[2025-07-02 19:10:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 19:10:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 19:10:02] local.INFO: Cron job executed {"time":"2025-07-02 19:10:02"} 
[2025-07-02 19:11:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 19:11:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 19:11:01] local.INFO: Cron job executed {"time":"2025-07-02 19:11:01"} 
[2025-07-02 19:11:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 19:11:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 19:11:02] local.INFO: Cron job executed {"time":"2025-07-02 19:11:02"} 
[2025-07-02 19:12:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 19:12:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 19:12:01] local.INFO: Cron job executed {"time":"2025-07-02 19:12:01"} 
[2025-07-02 19:12:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 19:12:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 19:12:02] local.INFO: Cron job executed {"time":"2025-07-02 19:12:02"} 
[2025-07-02 19:13:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 19:13:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 19:13:01] local.INFO: Cron job executed {"time":"2025-07-02 19:13:01"} 
[2025-07-02 19:13:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 19:13:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 19:13:02] local.INFO: Cron job executed {"time":"2025-07-02 19:13:02"} 
[2025-07-02 19:14:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 19:14:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 19:14:01] local.INFO: Cron job executed {"time":"2025-07-02 19:14:01"} 
[2025-07-02 19:14:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 19:14:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 19:14:03] local.INFO: Cron job executed {"time":"2025-07-02 19:14:03"} 
[2025-07-02 19:15:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 19:15:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 19:15:01] local.INFO: Cron job executed {"time":"2025-07-02 19:15:01"} 
[2025-07-02 19:15:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 19:15:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 19:15:03] local.INFO: Cron job executed {"time":"2025-07-02 19:15:03"} 
[2025-07-02 19:16:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 19:16:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 19:16:01] local.INFO: Cron job executed {"time":"2025-07-02 19:16:01"} 
[2025-07-02 19:16:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 19:16:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 19:16:02] local.INFO: Cron job executed {"time":"2025-07-02 19:16:02"} 
[2025-07-02 19:17:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 19:17:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 19:17:01] local.INFO: Cron job executed {"time":"2025-07-02 19:17:01"} 
[2025-07-02 19:17:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 19:17:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 19:17:03] local.INFO: Cron job executed {"time":"2025-07-02 19:17:03"} 
[2025-07-02 19:18:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 19:18:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 19:18:01] local.INFO: Cron job executed {"time":"2025-07-02 19:18:01"} 
[2025-07-02 19:18:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 19:18:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 19:18:02] local.INFO: Cron job executed {"time":"2025-07-02 19:18:02"} 
[2025-07-02 19:19:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 19:19:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 19:19:01] local.INFO: Cron job executed {"time":"2025-07-02 19:19:01"} 
[2025-07-02 19:19:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 19:19:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 19:19:02] local.INFO: Cron job executed {"time":"2025-07-02 19:19:02"} 
[2025-07-02 19:20:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 19:20:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 19:20:01] local.INFO: Cron job executed {"time":"2025-07-02 19:20:01"} 
[2025-07-02 19:20:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 19:20:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 19:20:02] local.INFO: Cron job executed {"time":"2025-07-02 19:20:02"} 
[2025-07-02 19:21:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 19:21:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 19:21:01] local.INFO: Cron job executed {"time":"2025-07-02 19:21:01"} 
[2025-07-02 19:21:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 19:21:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 19:21:02] local.INFO: Cron job executed {"time":"2025-07-02 19:21:02"} 
[2025-07-02 19:22:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 19:22:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 19:22:01] local.INFO: Cron job executed {"time":"2025-07-02 19:22:01"} 
[2025-07-02 19:22:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 19:22:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 19:22:03] local.INFO: Cron job executed {"time":"2025-07-02 19:22:03"} 
[2025-07-02 19:23:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 19:23:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 19:23:01] local.INFO: Cron job executed {"time":"2025-07-02 19:23:01"} 
[2025-07-02 19:23:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 19:23:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 19:23:02] local.INFO: Cron job executed {"time":"2025-07-02 19:23:02"} 
[2025-07-02 19:24:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 19:24:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 19:24:01] local.INFO: Cron job executed {"time":"2025-07-02 19:24:01"} 
[2025-07-02 19:24:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 19:24:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 19:24:02] local.INFO: Cron job executed {"time":"2025-07-02 19:24:02"} 
[2025-07-02 19:25:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 19:25:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 19:25:01] local.INFO: Cron job executed {"time":"2025-07-02 19:25:01"} 
[2025-07-02 19:25:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 19:25:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 19:25:03] local.INFO: Cron job executed {"time":"2025-07-02 19:25:03"} 
[2025-07-02 19:26:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 19:26:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 19:26:01] local.INFO: Cron job executed {"time":"2025-07-02 19:26:01"} 
[2025-07-02 19:26:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 19:26:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 19:26:02] local.INFO: Cron job executed {"time":"2025-07-02 19:26:02"} 
[2025-07-02 19:27:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 19:27:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 19:27:01] local.INFO: Cron job executed {"time":"2025-07-02 19:27:01"} 
[2025-07-02 19:27:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 19:27:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 19:27:02] local.INFO: Cron job executed {"time":"2025-07-02 19:27:02"} 
[2025-07-02 19:28:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 19:28:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 19:28:01] local.INFO: Cron job executed {"time":"2025-07-02 19:28:01"} 
[2025-07-02 19:28:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 19:28:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 19:28:02] local.INFO: Cron job executed {"time":"2025-07-02 19:28:02"} 
[2025-07-02 19:29:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 19:29:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 19:29:01] local.INFO: Cron job executed {"time":"2025-07-02 19:29:01"} 
[2025-07-02 19:29:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 19:29:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 19:29:03] local.INFO: Cron job executed {"time":"2025-07-02 19:29:03"} 
[2025-07-02 19:30:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 19:30:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 19:30:01] local.INFO: Cron job executed {"time":"2025-07-02 19:30:01"} 
[2025-07-02 19:30:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 19:30:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 19:30:02] local.INFO: Cron job executed {"time":"2025-07-02 19:30:02"} 
[2025-07-02 19:31:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 19:31:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 19:31:01] local.INFO: Cron job executed {"time":"2025-07-02 19:31:01"} 
[2025-07-02 19:31:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 19:31:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 19:31:02] local.INFO: Cron job executed {"time":"2025-07-02 19:31:02"} 
[2025-07-02 19:32:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 19:32:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 19:32:01] local.INFO: Cron job executed {"time":"2025-07-02 19:32:01"} 
[2025-07-02 19:32:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 19:32:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 19:32:02] local.INFO: Cron job executed {"time":"2025-07-02 19:32:02"} 
[2025-07-02 19:33:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 19:33:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 19:33:01] local.INFO: Cron job executed {"time":"2025-07-02 19:33:01"} 
[2025-07-02 19:33:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 19:33:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 19:33:02] local.INFO: Cron job executed {"time":"2025-07-02 19:33:02"} 
[2025-07-02 19:34:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 19:34:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 19:34:01] local.INFO: Cron job executed {"time":"2025-07-02 19:34:01"} 
[2025-07-02 19:34:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 19:34:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 19:34:03] local.INFO: Cron job executed {"time":"2025-07-02 19:34:03"} 
[2025-07-02 19:35:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 19:35:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 19:35:01] local.INFO: Cron job executed {"time":"2025-07-02 19:35:01"} 
[2025-07-02 19:35:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 19:35:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 19:35:02] local.INFO: Cron job executed {"time":"2025-07-02 19:35:02"} 
[2025-07-02 19:36:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 19:36:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 19:36:01] local.INFO: Cron job executed {"time":"2025-07-02 19:36:01"} 
[2025-07-02 19:36:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 19:36:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 19:36:02] local.INFO: Cron job executed {"time":"2025-07-02 19:36:02"} 
[2025-07-02 19:37:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 19:37:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 19:37:01] local.INFO: Cron job executed {"time":"2025-07-02 19:37:01"} 
[2025-07-02 19:37:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 19:37:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 19:37:02] local.INFO: Cron job executed {"time":"2025-07-02 19:37:02"} 
[2025-07-02 19:38:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 19:38:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 19:38:01] local.INFO: Cron job executed {"time":"2025-07-02 19:38:01"} 
[2025-07-02 19:38:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 19:38:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 19:38:02] local.INFO: Cron job executed {"time":"2025-07-02 19:38:02"} 
[2025-07-02 19:39:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 19:39:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 19:39:01] local.INFO: Cron job executed {"time":"2025-07-02 19:39:01"} 
[2025-07-02 19:39:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 19:39:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 19:39:03] local.INFO: Cron job executed {"time":"2025-07-02 19:39:03"} 
[2025-07-02 19:40:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 19:40:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 19:40:01] local.INFO: Cron job executed {"time":"2025-07-02 19:40:01"} 
[2025-07-02 19:40:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 19:40:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 19:40:02] local.INFO: Cron job executed {"time":"2025-07-02 19:40:02"} 
[2025-07-02 19:41:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 19:41:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 19:41:01] local.INFO: Cron job executed {"time":"2025-07-02 19:41:01"} 
[2025-07-02 19:41:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 19:41:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 19:41:02] local.INFO: Cron job executed {"time":"2025-07-02 19:41:02"} 
[2025-07-02 19:42:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 19:42:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 19:42:01] local.INFO: Cron job executed {"time":"2025-07-02 19:42:01"} 
[2025-07-02 19:42:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 19:42:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 19:42:02] local.INFO: Cron job executed {"time":"2025-07-02 19:42:02"} 
[2025-07-02 19:43:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 19:43:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 19:43:01] local.INFO: Cron job executed {"time":"2025-07-02 19:43:01"} 
[2025-07-02 19:43:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 19:43:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 19:43:02] local.INFO: Cron job executed {"time":"2025-07-02 19:43:02"} 
[2025-07-02 19:44:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 19:44:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 19:44:01] local.INFO: Cron job executed {"time":"2025-07-02 19:44:01"} 
[2025-07-02 19:44:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 19:44:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 19:44:02] local.INFO: Cron job executed {"time":"2025-07-02 19:44:02"} 
[2025-07-02 19:45:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 19:45:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 19:45:01] local.INFO: Cron job executed {"time":"2025-07-02 19:45:01"} 
[2025-07-02 19:45:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 19:45:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 19:45:02] local.INFO: Cron job executed {"time":"2025-07-02 19:45:02"} 
[2025-07-02 19:46:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 19:46:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 19:46:01] local.INFO: Cron job executed {"time":"2025-07-02 19:46:01"} 
[2025-07-02 19:46:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 19:46:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 19:46:02] local.INFO: Cron job executed {"time":"2025-07-02 19:46:02"} 
[2025-07-02 19:47:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 19:47:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 19:47:01] local.INFO: Cron job executed {"time":"2025-07-02 19:47:01"} 
[2025-07-02 19:47:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 19:47:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 19:47:03] local.INFO: Cron job executed {"time":"2025-07-02 19:47:03"} 
[2025-07-02 19:48:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 19:48:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 19:48:01] local.INFO: Cron job executed {"time":"2025-07-02 19:48:01"} 
[2025-07-02 19:48:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 19:48:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 19:48:02] local.INFO: Cron job executed {"time":"2025-07-02 19:48:02"} 
[2025-07-02 19:49:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 19:49:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 19:49:01] local.INFO: Cron job executed {"time":"2025-07-02 19:49:01"} 
[2025-07-02 19:49:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 19:49:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 19:49:02] local.INFO: Cron job executed {"time":"2025-07-02 19:49:02"} 
[2025-07-02 19:50:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 19:50:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 19:50:01] local.INFO: Cron job executed {"time":"2025-07-02 19:50:01"} 
[2025-07-02 19:50:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 19:50:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 19:50:02] local.INFO: Cron job executed {"time":"2025-07-02 19:50:02"} 
[2025-07-02 19:51:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 19:51:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 19:51:01] local.INFO: Cron job executed {"time":"2025-07-02 19:51:01"} 
[2025-07-02 19:51:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 19:51:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 19:51:02] local.INFO: Cron job executed {"time":"2025-07-02 19:51:02"} 
[2025-07-02 19:52:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 19:52:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 19:52:01] local.INFO: Cron job executed {"time":"2025-07-02 19:52:01"} 
[2025-07-02 19:52:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 19:52:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 19:52:02] local.INFO: Cron job executed {"time":"2025-07-02 19:52:02"} 
[2025-07-02 19:53:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 19:53:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 19:53:01] local.INFO: Cron job executed {"time":"2025-07-02 19:53:01"} 
[2025-07-02 19:53:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 19:53:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 19:53:02] local.INFO: Cron job executed {"time":"2025-07-02 19:53:02"} 
[2025-07-02 19:54:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 19:54:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 19:54:01] local.INFO: Cron job executed {"time":"2025-07-02 19:54:01"} 
[2025-07-02 19:54:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 19:54:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 19:54:02] local.INFO: Cron job executed {"time":"2025-07-02 19:54:02"} 
[2025-07-02 19:55:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 19:55:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 19:55:01] local.INFO: Cron job executed {"time":"2025-07-02 19:55:01"} 
[2025-07-02 19:55:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 19:55:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 19:55:02] local.INFO: Cron job executed {"time":"2025-07-02 19:55:02"} 
[2025-07-02 19:56:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 19:56:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 19:56:01] local.INFO: Cron job executed {"time":"2025-07-02 19:56:01"} 
[2025-07-02 19:56:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 19:56:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 19:56:02] local.INFO: Cron job executed {"time":"2025-07-02 19:56:02"} 
[2025-07-02 19:57:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 19:57:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 19:57:01] local.INFO: Cron job executed {"time":"2025-07-02 19:57:01"} 
[2025-07-02 19:57:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 19:57:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 19:57:02] local.INFO: Cron job executed {"time":"2025-07-02 19:57:02"} 
[2025-07-02 19:58:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 19:58:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 19:58:01] local.INFO: Cron job executed {"time":"2025-07-02 19:58:01"} 
[2025-07-02 19:58:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 19:58:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 19:58:02] local.INFO: Cron job executed {"time":"2025-07-02 19:58:02"} 
[2025-07-02 19:59:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 19:59:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 19:59:01] local.INFO: Cron job executed {"time":"2025-07-02 19:59:01"} 
[2025-07-02 19:59:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 19:59:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 19:59:02] local.INFO: Cron job executed {"time":"2025-07-02 19:59:02"} 
[2025-07-02 20:00:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 20:00:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 20:00:01] local.INFO: Cron job executed {"time":"2025-07-02 20:00:01"} 
[2025-07-02 20:00:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 20:00:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 20:00:02] local.INFO: Cron job executed {"time":"2025-07-02 20:00:02"} 
[2025-07-02 20:01:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 20:01:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 20:01:01] local.INFO: Cron job executed {"time":"2025-07-02 20:01:01"} 
[2025-07-02 20:01:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 20:01:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 20:01:02] local.INFO: Cron job executed {"time":"2025-07-02 20:01:02"} 
[2025-07-02 20:02:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 20:02:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 20:02:01] local.INFO: Cron job executed {"time":"2025-07-02 20:02:01"} 
[2025-07-02 20:02:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 20:02:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 20:02:02] local.INFO: Cron job executed {"time":"2025-07-02 20:02:02"} 
[2025-07-02 20:03:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 20:03:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 20:03:01] local.INFO: Cron job executed {"time":"2025-07-02 20:03:01"} 
[2025-07-02 20:03:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 20:03:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 20:03:02] local.INFO: Cron job executed {"time":"2025-07-02 20:03:02"} 
[2025-07-02 20:04:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 20:04:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 20:04:01] local.INFO: Cron job executed {"time":"2025-07-02 20:04:01"} 
[2025-07-02 20:04:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 20:04:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 20:04:03] local.INFO: Cron job executed {"time":"2025-07-02 20:04:03"} 
[2025-07-02 20:05:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 20:05:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 20:05:01] local.INFO: Cron job executed {"time":"2025-07-02 20:05:01"} 
[2025-07-02 20:05:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 20:05:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 20:05:02] local.INFO: Cron job executed {"time":"2025-07-02 20:05:02"} 
[2025-07-02 20:06:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 20:06:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 20:06:01] local.INFO: Cron job executed {"time":"2025-07-02 20:06:01"} 
[2025-07-02 20:06:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 20:06:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 20:06:03] local.INFO: Cron job executed {"time":"2025-07-02 20:06:03"} 
[2025-07-02 20:07:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 20:07:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 20:07:01] local.INFO: Cron job executed {"time":"2025-07-02 20:07:01"} 
[2025-07-02 20:07:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 20:07:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 20:07:02] local.INFO: Cron job executed {"time":"2025-07-02 20:07:02"} 
[2025-07-02 20:08:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 20:08:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 20:08:01] local.INFO: Cron job executed {"time":"2025-07-02 20:08:01"} 
[2025-07-02 20:08:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 20:08:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 20:08:02] local.INFO: Cron job executed {"time":"2025-07-02 20:08:02"} 
[2025-07-02 20:09:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 20:09:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 20:09:01] local.INFO: Cron job executed {"time":"2025-07-02 20:09:01"} 
[2025-07-02 20:09:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 20:09:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 20:09:02] local.INFO: Cron job executed {"time":"2025-07-02 20:09:02"} 
[2025-07-02 20:10:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 20:10:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 20:10:01] local.INFO: Cron job executed {"time":"2025-07-02 20:10:01"} 
[2025-07-02 20:10:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 20:10:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 20:10:02] local.INFO: Cron job executed {"time":"2025-07-02 20:10:02"} 
[2025-07-02 20:11:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 20:11:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 20:11:01] local.INFO: Cron job executed {"time":"2025-07-02 20:11:01"} 
[2025-07-02 20:11:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 20:11:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 20:11:02] local.INFO: Cron job executed {"time":"2025-07-02 20:11:02"} 
[2025-07-02 20:12:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 20:12:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 20:12:01] local.INFO: Cron job executed {"time":"2025-07-02 20:12:01"} 
[2025-07-02 20:12:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 20:12:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 20:12:03] local.INFO: Cron job executed {"time":"2025-07-02 20:12:03"} 
[2025-07-02 20:13:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 20:13:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 20:13:01] local.INFO: Cron job executed {"time":"2025-07-02 20:13:01"} 
[2025-07-02 20:13:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 20:13:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 20:13:03] local.INFO: Cron job executed {"time":"2025-07-02 20:13:03"} 
[2025-07-02 20:14:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 20:14:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 20:14:01] local.INFO: Cron job executed {"time":"2025-07-02 20:14:01"} 
[2025-07-02 20:14:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 20:14:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 20:14:02] local.INFO: Cron job executed {"time":"2025-07-02 20:14:02"} 
[2025-07-02 20:15:16] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 20:15:16] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 20:15:16] local.INFO: Cron job executed {"time":"2025-07-02 20:15:16"} 
[2025-07-02 20:15:19] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 20:15:19] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 20:15:20] local.INFO: Cron job executed {"time":"2025-07-02 20:15:20"} 
[2025-07-02 20:16:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 20:16:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 20:16:01] local.INFO: Cron job executed {"time":"2025-07-02 20:16:01"} 
[2025-07-02 20:16:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 20:16:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 20:16:03] local.INFO: Cron job executed {"time":"2025-07-02 20:16:03"} 
[2025-07-02 20:17:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 20:17:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 20:17:01] local.INFO: Cron job executed {"time":"2025-07-02 20:17:01"} 
[2025-07-02 20:17:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 20:17:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 20:17:02] local.INFO: Cron job executed {"time":"2025-07-02 20:17:02"} 
[2025-07-02 20:18:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 20:18:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 20:18:01] local.INFO: Cron job executed {"time":"2025-07-02 20:18:01"} 
[2025-07-02 20:18:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 20:18:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 20:18:02] local.INFO: Cron job executed {"time":"2025-07-02 20:18:02"} 
[2025-07-02 20:19:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 20:19:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 20:19:01] local.INFO: Cron job executed {"time":"2025-07-02 20:19:01"} 
[2025-07-02 20:19:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 20:19:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 20:19:02] local.INFO: Cron job executed {"time":"2025-07-02 20:19:02"} 
[2025-07-02 20:20:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 20:20:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 20:20:01] local.INFO: Cron job executed {"time":"2025-07-02 20:20:01"} 
[2025-07-02 20:20:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 20:20:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 20:20:02] local.INFO: Cron job executed {"time":"2025-07-02 20:20:02"} 
[2025-07-02 20:21:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 20:21:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 20:21:01] local.INFO: Cron job executed {"time":"2025-07-02 20:21:01"} 
[2025-07-02 20:21:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 20:21:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 20:21:02] local.INFO: Cron job executed {"time":"2025-07-02 20:21:02"} 
[2025-07-02 20:22:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 20:22:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 20:22:01] local.INFO: Cron job executed {"time":"2025-07-02 20:22:01"} 
[2025-07-02 20:22:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 20:22:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 20:22:03] local.INFO: Cron job executed {"time":"2025-07-02 20:22:03"} 
[2025-07-02 20:23:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 20:23:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 20:23:01] local.INFO: Cron job executed {"time":"2025-07-02 20:23:01"} 
[2025-07-02 20:23:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 20:23:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 20:23:02] local.INFO: Cron job executed {"time":"2025-07-02 20:23:02"} 
[2025-07-02 20:24:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 20:24:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 20:24:01] local.INFO: Cron job executed {"time":"2025-07-02 20:24:01"} 
[2025-07-02 20:24:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 20:24:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 20:24:02] local.INFO: Cron job executed {"time":"2025-07-02 20:24:02"} 
[2025-07-02 20:25:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 20:25:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 20:25:01] local.INFO: Cron job executed {"time":"2025-07-02 20:25:01"} 
[2025-07-02 20:25:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 20:25:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 20:25:02] local.INFO: Cron job executed {"time":"2025-07-02 20:25:02"} 
[2025-07-02 20:26:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 20:26:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 20:26:01] local.INFO: Cron job executed {"time":"2025-07-02 20:26:01"} 
[2025-07-02 20:26:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 20:26:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 20:26:02] local.INFO: Cron job executed {"time":"2025-07-02 20:26:02"} 
[2025-07-02 20:27:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 20:27:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 20:27:01] local.INFO: Cron job executed {"time":"2025-07-02 20:27:01"} 
[2025-07-02 20:27:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 20:27:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 20:27:02] local.INFO: Cron job executed {"time":"2025-07-02 20:27:02"} 
[2025-07-02 20:28:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 20:28:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 20:28:01] local.INFO: Cron job executed {"time":"2025-07-02 20:28:01"} 
[2025-07-02 20:28:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 20:28:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 20:28:03] local.INFO: Cron job executed {"time":"2025-07-02 20:28:03"} 
[2025-07-02 20:29:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 20:29:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 20:29:01] local.INFO: Cron job executed {"time":"2025-07-02 20:29:01"} 
[2025-07-02 20:29:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 20:29:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 20:29:02] local.INFO: Cron job executed {"time":"2025-07-02 20:29:02"} 
[2025-07-02 20:30:02] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 20:30:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 20:30:02] local.INFO: Cron job executed {"time":"2025-07-02 20:30:02"} 
[2025-07-02 20:30:04] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 20:30:04] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 20:30:04] local.INFO: Cron job executed {"time":"2025-07-02 20:30:04"} 
[2025-07-02 20:31:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 20:31:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 20:31:01] local.INFO: Cron job executed {"time":"2025-07-02 20:31:01"} 
[2025-07-02 20:31:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 20:31:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 20:31:02] local.INFO: Cron job executed {"time":"2025-07-02 20:31:02"} 
[2025-07-02 20:32:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 20:32:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 20:32:01] local.INFO: Cron job executed {"time":"2025-07-02 20:32:01"} 
[2025-07-02 20:32:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 20:32:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 20:32:02] local.INFO: Cron job executed {"time":"2025-07-02 20:32:02"} 
[2025-07-02 20:33:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 20:33:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 20:33:01] local.INFO: Cron job executed {"time":"2025-07-02 20:33:01"} 
[2025-07-02 20:33:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 20:33:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 20:33:02] local.INFO: Cron job executed {"time":"2025-07-02 20:33:02"} 
[2025-07-02 20:34:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 20:34:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 20:34:01] local.INFO: Cron job executed {"time":"2025-07-02 20:34:01"} 
[2025-07-02 20:34:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 20:34:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 20:34:02] local.INFO: Cron job executed {"time":"2025-07-02 20:34:02"} 
[2025-07-02 20:35:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 20:35:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 20:35:01] local.INFO: Cron job executed {"time":"2025-07-02 20:35:01"} 
[2025-07-02 20:35:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 20:35:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 20:35:02] local.INFO: Cron job executed {"time":"2025-07-02 20:35:02"} 
[2025-07-02 20:36:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 20:36:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 20:36:01] local.INFO: Cron job executed {"time":"2025-07-02 20:36:01"} 
[2025-07-02 20:36:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 20:36:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 20:36:03] local.INFO: Cron job executed {"time":"2025-07-02 20:36:03"} 
[2025-07-02 20:37:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 20:37:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 20:37:01] local.INFO: Cron job executed {"time":"2025-07-02 20:37:01"} 
[2025-07-02 20:37:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 20:37:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 20:37:02] local.INFO: Cron job executed {"time":"2025-07-02 20:37:02"} 
[2025-07-02 20:38:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 20:38:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 20:38:01] local.INFO: Cron job executed {"time":"2025-07-02 20:38:01"} 
[2025-07-02 20:38:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 20:38:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 20:38:02] local.INFO: Cron job executed {"time":"2025-07-02 20:38:02"} 
[2025-07-02 20:39:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 20:39:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 20:39:01] local.INFO: Cron job executed {"time":"2025-07-02 20:39:01"} 
[2025-07-02 20:39:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 20:39:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 20:39:02] local.INFO: Cron job executed {"time":"2025-07-02 20:39:02"} 
[2025-07-02 20:40:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 20:40:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 20:40:01] local.INFO: Cron job executed {"time":"2025-07-02 20:40:01"} 
[2025-07-02 20:40:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 20:40:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 20:40:02] local.INFO: Cron job executed {"time":"2025-07-02 20:40:02"} 
[2025-07-02 20:41:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 20:41:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 20:41:01] local.INFO: Cron job executed {"time":"2025-07-02 20:41:01"} 
[2025-07-02 20:41:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 20:41:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 20:41:02] local.INFO: Cron job executed {"time":"2025-07-02 20:41:02"} 
[2025-07-02 20:42:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 20:42:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 20:42:01] local.INFO: Cron job executed {"time":"2025-07-02 20:42:01"} 
[2025-07-02 20:42:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 20:42:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 20:42:02] local.INFO: Cron job executed {"time":"2025-07-02 20:42:02"} 
[2025-07-02 20:43:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 20:43:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 20:43:01] local.INFO: Cron job executed {"time":"2025-07-02 20:43:01"} 
[2025-07-02 20:43:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 20:43:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 20:43:02] local.INFO: Cron job executed {"time":"2025-07-02 20:43:02"} 
[2025-07-02 20:44:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 20:44:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 20:44:01] local.INFO: Cron job executed {"time":"2025-07-02 20:44:01"} 
[2025-07-02 20:44:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 20:44:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 20:44:02] local.INFO: Cron job executed {"time":"2025-07-02 20:44:02"} 
[2025-07-02 20:45:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 20:45:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 20:45:01] local.INFO: Cron job executed {"time":"2025-07-02 20:45:01"} 
[2025-07-02 20:45:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 20:45:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 20:45:02] local.INFO: Cron job executed {"time":"2025-07-02 20:45:02"} 
[2025-07-02 20:46:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 20:46:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 20:46:01] local.INFO: Cron job executed {"time":"2025-07-02 20:46:01"} 
[2025-07-02 20:46:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 20:46:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 20:46:02] local.INFO: Cron job executed {"time":"2025-07-02 20:46:02"} 
[2025-07-02 20:47:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 20:47:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 20:47:01] local.INFO: Cron job executed {"time":"2025-07-02 20:47:01"} 
[2025-07-02 20:47:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 20:47:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 20:47:02] local.INFO: Cron job executed {"time":"2025-07-02 20:47:02"} 
[2025-07-02 20:48:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 20:48:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 20:48:01] local.INFO: Cron job executed {"time":"2025-07-02 20:48:01"} 
[2025-07-02 20:48:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 20:48:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 20:48:02] local.INFO: Cron job executed {"time":"2025-07-02 20:48:02"} 
[2025-07-02 20:49:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 20:49:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 20:49:01] local.INFO: Cron job executed {"time":"2025-07-02 20:49:01"} 
[2025-07-02 20:49:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 20:49:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 20:49:03] local.INFO: Cron job executed {"time":"2025-07-02 20:49:03"} 
[2025-07-02 20:50:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 20:50:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 20:50:01] local.INFO: Cron job executed {"time":"2025-07-02 20:50:01"} 
[2025-07-02 20:50:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 20:50:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 20:50:02] local.INFO: Cron job executed {"time":"2025-07-02 20:50:02"} 
[2025-07-02 20:51:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 20:51:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 20:51:01] local.INFO: Cron job executed {"time":"2025-07-02 20:51:01"} 
[2025-07-02 20:51:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 20:51:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 20:51:02] local.INFO: Cron job executed {"time":"2025-07-02 20:51:02"} 
[2025-07-02 20:52:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 20:52:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 20:52:01] local.INFO: Cron job executed {"time":"2025-07-02 20:52:01"} 
[2025-07-02 20:52:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 20:52:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 20:52:02] local.INFO: Cron job executed {"time":"2025-07-02 20:52:02"} 
[2025-07-02 20:53:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 20:53:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 20:53:01] local.INFO: Cron job executed {"time":"2025-07-02 20:53:01"} 
[2025-07-02 20:53:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 20:53:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 20:53:03] local.INFO: Cron job executed {"time":"2025-07-02 20:53:03"} 
[2025-07-02 20:54:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 20:54:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 20:54:01] local.INFO: Cron job executed {"time":"2025-07-02 20:54:01"} 
[2025-07-02 20:54:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 20:54:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 20:54:02] local.INFO: Cron job executed {"time":"2025-07-02 20:54:02"} 
[2025-07-02 20:55:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 20:55:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 20:55:01] local.INFO: Cron job executed {"time":"2025-07-02 20:55:01"} 
[2025-07-02 20:55:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 20:55:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 20:55:02] local.INFO: Cron job executed {"time":"2025-07-02 20:55:02"} 
[2025-07-02 20:56:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 20:56:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 20:56:01] local.INFO: Cron job executed {"time":"2025-07-02 20:56:01"} 
[2025-07-02 20:56:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 20:56:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 20:56:02] local.INFO: Cron job executed {"time":"2025-07-02 20:56:02"} 
[2025-07-02 20:57:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 20:57:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 20:57:01] local.INFO: Cron job executed {"time":"2025-07-02 20:57:01"} 
[2025-07-02 20:57:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 20:57:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 20:57:02] local.INFO: Cron job executed {"time":"2025-07-02 20:57:02"} 
[2025-07-02 20:58:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 20:58:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 20:58:01] local.INFO: Cron job executed {"time":"2025-07-02 20:58:01"} 
[2025-07-02 20:58:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 20:58:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 20:58:03] local.INFO: Cron job executed {"time":"2025-07-02 20:58:03"} 
[2025-07-02 20:59:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 20:59:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 20:59:01] local.INFO: Cron job executed {"time":"2025-07-02 20:59:01"} 
[2025-07-02 20:59:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 20:59:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 20:59:03] local.INFO: Cron job executed {"time":"2025-07-02 20:59:03"} 
[2025-07-02 21:00:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 21:00:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 21:00:01] local.INFO: Cron job executed {"time":"2025-07-02 21:00:01"} 
[2025-07-02 21:00:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 21:00:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 21:00:02] local.INFO: Cron job executed {"time":"2025-07-02 21:00:02"} 
[2025-07-02 21:01:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 21:01:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 21:01:01] local.INFO: Cron job executed {"time":"2025-07-02 21:01:01"} 
[2025-07-02 21:01:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 21:01:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 21:01:02] local.INFO: Cron job executed {"time":"2025-07-02 21:01:02"} 
[2025-07-02 21:02:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 21:02:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 21:02:01] local.INFO: Cron job executed {"time":"2025-07-02 21:02:01"} 
[2025-07-02 21:02:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 21:02:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 21:02:02] local.INFO: Cron job executed {"time":"2025-07-02 21:02:02"} 
[2025-07-02 21:03:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 21:03:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 21:03:01] local.INFO: Cron job executed {"time":"2025-07-02 21:03:01"} 
[2025-07-02 21:03:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 21:03:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 21:03:02] local.INFO: Cron job executed {"time":"2025-07-02 21:03:02"} 
[2025-07-02 21:04:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 21:04:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 21:04:01] local.INFO: Cron job executed {"time":"2025-07-02 21:04:01"} 
[2025-07-02 21:04:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 21:04:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 21:04:02] local.INFO: Cron job executed {"time":"2025-07-02 21:04:02"} 
[2025-07-02 21:05:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 21:05:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 21:05:01] local.INFO: Cron job executed {"time":"2025-07-02 21:05:01"} 
[2025-07-02 21:05:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 21:05:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 21:05:02] local.INFO: Cron job executed {"time":"2025-07-02 21:05:02"} 
[2025-07-02 21:06:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 21:06:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 21:06:01] local.INFO: Cron job executed {"time":"2025-07-02 21:06:01"} 
[2025-07-02 21:06:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 21:06:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 21:06:02] local.INFO: Cron job executed {"time":"2025-07-02 21:06:02"} 
[2025-07-02 21:07:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 21:07:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 21:07:01] local.INFO: Cron job executed {"time":"2025-07-02 21:07:01"} 
[2025-07-02 21:07:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 21:07:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 21:07:02] local.INFO: Cron job executed {"time":"2025-07-02 21:07:02"} 
[2025-07-02 21:08:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 21:08:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 21:08:01] local.INFO: Cron job executed {"time":"2025-07-02 21:08:01"} 
[2025-07-02 21:08:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 21:08:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 21:08:02] local.INFO: Cron job executed {"time":"2025-07-02 21:08:02"} 
[2025-07-02 21:09:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 21:09:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 21:09:01] local.INFO: Cron job executed {"time":"2025-07-02 21:09:01"} 
[2025-07-02 21:09:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 21:09:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 21:09:02] local.INFO: Cron job executed {"time":"2025-07-02 21:09:02"} 
[2025-07-02 21:10:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 21:10:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 21:10:01] local.INFO: Cron job executed {"time":"2025-07-02 21:10:01"} 
[2025-07-02 21:10:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 21:10:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 21:10:02] local.INFO: Cron job executed {"time":"2025-07-02 21:10:02"} 
[2025-07-02 21:11:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 21:11:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 21:11:01] local.INFO: Cron job executed {"time":"2025-07-02 21:11:01"} 
[2025-07-02 21:11:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 21:11:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 21:11:02] local.INFO: Cron job executed {"time":"2025-07-02 21:11:02"} 
[2025-07-02 21:12:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 21:12:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 21:12:01] local.INFO: Cron job executed {"time":"2025-07-02 21:12:01"} 
[2025-07-02 21:12:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 21:12:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 21:12:02] local.INFO: Cron job executed {"time":"2025-07-02 21:12:02"} 
[2025-07-02 21:13:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 21:13:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 21:13:01] local.INFO: Cron job executed {"time":"2025-07-02 21:13:01"} 
[2025-07-02 21:13:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 21:13:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 21:13:02] local.INFO: Cron job executed {"time":"2025-07-02 21:13:02"} 
[2025-07-02 21:14:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 21:14:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 21:14:01] local.INFO: Cron job executed {"time":"2025-07-02 21:14:01"} 
[2025-07-02 21:14:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 21:14:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 21:14:02] local.INFO: Cron job executed {"time":"2025-07-02 21:14:02"} 
[2025-07-02 21:15:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 21:15:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 21:15:01] local.INFO: Cron job executed {"time":"2025-07-02 21:15:01"} 
[2025-07-02 21:15:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 21:15:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 21:15:03] local.INFO: Cron job executed {"time":"2025-07-02 21:15:03"} 
[2025-07-02 21:16:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 21:16:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 21:16:01] local.INFO: Cron job executed {"time":"2025-07-02 21:16:01"} 
[2025-07-02 21:16:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 21:16:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 21:16:02] local.INFO: Cron job executed {"time":"2025-07-02 21:16:02"} 
[2025-07-02 21:17:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 21:17:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 21:17:01] local.INFO: Cron job executed {"time":"2025-07-02 21:17:01"} 
[2025-07-02 21:17:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 21:17:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 21:17:02] local.INFO: Cron job executed {"time":"2025-07-02 21:17:02"} 
[2025-07-02 21:18:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 21:18:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 21:18:01] local.INFO: Cron job executed {"time":"2025-07-02 21:18:01"} 
[2025-07-02 21:18:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 21:18:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 21:18:02] local.INFO: Cron job executed {"time":"2025-07-02 21:18:02"} 
[2025-07-02 21:19:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 21:19:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 21:19:01] local.INFO: Cron job executed {"time":"2025-07-02 21:19:01"} 
[2025-07-02 21:19:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 21:19:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 21:19:02] local.INFO: Cron job executed {"time":"2025-07-02 21:19:02"} 
[2025-07-02 21:20:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 21:20:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 21:20:01] local.INFO: Cron job executed {"time":"2025-07-02 21:20:01"} 
[2025-07-02 21:20:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 21:20:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 21:20:02] local.INFO: Cron job executed {"time":"2025-07-02 21:20:02"} 
[2025-07-02 21:21:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 21:21:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 21:21:01] local.INFO: Cron job executed {"time":"2025-07-02 21:21:01"} 
[2025-07-02 21:21:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 21:21:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 21:21:02] local.INFO: Cron job executed {"time":"2025-07-02 21:21:02"} 
[2025-07-02 21:22:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 21:22:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 21:22:01] local.INFO: Cron job executed {"time":"2025-07-02 21:22:01"} 
[2025-07-02 21:22:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 21:22:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 21:22:02] local.INFO: Cron job executed {"time":"2025-07-02 21:22:02"} 
[2025-07-02 21:23:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 21:23:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 21:23:01] local.INFO: Cron job executed {"time":"2025-07-02 21:23:01"} 
[2025-07-02 21:23:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 21:23:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 21:23:02] local.INFO: Cron job executed {"time":"2025-07-02 21:23:02"} 
[2025-07-02 21:24:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 21:24:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 21:24:01] local.INFO: Cron job executed {"time":"2025-07-02 21:24:01"} 
[2025-07-02 21:24:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 21:24:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 21:24:02] local.INFO: Cron job executed {"time":"2025-07-02 21:24:02"} 
[2025-07-02 21:25:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 21:25:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 21:25:01] local.INFO: Cron job executed {"time":"2025-07-02 21:25:01"} 
[2025-07-02 21:25:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 21:25:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 21:25:02] local.INFO: Cron job executed {"time":"2025-07-02 21:25:02"} 
[2025-07-02 21:26:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 21:26:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 21:26:01] local.INFO: Cron job executed {"time":"2025-07-02 21:26:01"} 
[2025-07-02 21:26:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 21:26:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 21:26:02] local.INFO: Cron job executed {"time":"2025-07-02 21:26:02"} 
[2025-07-02 21:27:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 21:27:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 21:27:01] local.INFO: Cron job executed {"time":"2025-07-02 21:27:01"} 
[2025-07-02 21:27:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 21:27:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 21:27:02] local.INFO: Cron job executed {"time":"2025-07-02 21:27:02"} 
[2025-07-02 21:28:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 21:28:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 21:28:01] local.INFO: Cron job executed {"time":"2025-07-02 21:28:01"} 
[2025-07-02 21:28:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 21:28:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 21:28:02] local.INFO: Cron job executed {"time":"2025-07-02 21:28:02"} 
[2025-07-02 21:29:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 21:29:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 21:29:01] local.INFO: Cron job executed {"time":"2025-07-02 21:29:01"} 
[2025-07-02 21:29:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 21:29:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 21:29:02] local.INFO: Cron job executed {"time":"2025-07-02 21:29:02"} 
[2025-07-02 21:30:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 21:30:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 21:30:01] local.INFO: Cron job executed {"time":"2025-07-02 21:30:01"} 
[2025-07-02 21:30:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 21:30:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 21:30:02] local.INFO: Cron job executed {"time":"2025-07-02 21:30:02"} 
[2025-07-02 21:31:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 21:31:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 21:31:01] local.INFO: Cron job executed {"time":"2025-07-02 21:31:01"} 
[2025-07-02 21:31:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 21:31:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 21:31:02] local.INFO: Cron job executed {"time":"2025-07-02 21:31:02"} 
[2025-07-02 21:32:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 21:32:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 21:32:01] local.INFO: Cron job executed {"time":"2025-07-02 21:32:01"} 
[2025-07-02 21:32:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 21:32:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 21:32:03] local.INFO: Cron job executed {"time":"2025-07-02 21:32:03"} 
[2025-07-02 21:33:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 21:33:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 21:33:01] local.INFO: Cron job executed {"time":"2025-07-02 21:33:01"} 
[2025-07-02 21:33:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 21:33:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 21:33:02] local.INFO: Cron job executed {"time":"2025-07-02 21:33:02"} 
[2025-07-02 21:34:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 21:34:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 21:34:01] local.INFO: Cron job executed {"time":"2025-07-02 21:34:01"} 
[2025-07-02 21:34:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 21:34:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 21:34:02] local.INFO: Cron job executed {"time":"2025-07-02 21:34:02"} 
[2025-07-02 21:35:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 21:35:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 21:35:01] local.INFO: Cron job executed {"time":"2025-07-02 21:35:01"} 
[2025-07-02 21:35:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 21:35:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 21:35:02] local.INFO: Cron job executed {"time":"2025-07-02 21:35:02"} 
[2025-07-02 21:36:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 21:36:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 21:36:01] local.INFO: Cron job executed {"time":"2025-07-02 21:36:01"} 
[2025-07-02 21:36:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 21:36:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 21:36:02] local.INFO: Cron job executed {"time":"2025-07-02 21:36:02"} 
[2025-07-02 21:37:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 21:37:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 21:37:01] local.INFO: Cron job executed {"time":"2025-07-02 21:37:01"} 
[2025-07-02 21:37:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 21:37:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 21:37:02] local.INFO: Cron job executed {"time":"2025-07-02 21:37:02"} 
[2025-07-02 21:38:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 21:38:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 21:38:01] local.INFO: Cron job executed {"time":"2025-07-02 21:38:01"} 
[2025-07-02 21:38:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 21:38:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 21:38:02] local.INFO: Cron job executed {"time":"2025-07-02 21:38:02"} 
[2025-07-02 21:39:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 21:39:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 21:39:01] local.INFO: Cron job executed {"time":"2025-07-02 21:39:01"} 
[2025-07-02 21:39:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 21:39:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 21:39:02] local.INFO: Cron job executed {"time":"2025-07-02 21:39:02"} 
[2025-07-02 21:40:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 21:40:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 21:40:01] local.INFO: Cron job executed {"time":"2025-07-02 21:40:01"} 
[2025-07-02 21:40:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 21:40:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 21:40:02] local.INFO: Cron job executed {"time":"2025-07-02 21:40:02"} 
[2025-07-02 21:41:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 21:41:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 21:41:01] local.INFO: Cron job executed {"time":"2025-07-02 21:41:01"} 
[2025-07-02 21:41:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 21:41:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 21:41:02] local.INFO: Cron job executed {"time":"2025-07-02 21:41:02"} 
[2025-07-02 21:42:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 21:42:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 21:42:01] local.INFO: Cron job executed {"time":"2025-07-02 21:42:01"} 
[2025-07-02 21:42:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 21:42:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 21:42:02] local.INFO: Cron job executed {"time":"2025-07-02 21:42:02"} 
[2025-07-02 21:43:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 21:43:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 21:43:01] local.INFO: Cron job executed {"time":"2025-07-02 21:43:01"} 
[2025-07-02 21:43:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 21:43:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 21:43:02] local.INFO: Cron job executed {"time":"2025-07-02 21:43:02"} 
[2025-07-02 21:44:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 21:44:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 21:44:01] local.INFO: Cron job executed {"time":"2025-07-02 21:44:01"} 
[2025-07-02 21:44:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 21:44:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 21:44:02] local.INFO: Cron job executed {"time":"2025-07-02 21:44:02"} 
[2025-07-02 21:45:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 21:45:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 21:45:01] local.INFO: Cron job executed {"time":"2025-07-02 21:45:01"} 
[2025-07-02 21:45:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 21:45:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 21:45:02] local.INFO: Cron job executed {"time":"2025-07-02 21:45:02"} 
[2025-07-02 21:46:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 21:46:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 21:46:01] local.INFO: Cron job executed {"time":"2025-07-02 21:46:01"} 
[2025-07-02 21:46:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 21:46:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 21:46:02] local.INFO: Cron job executed {"time":"2025-07-02 21:46:02"} 
[2025-07-02 21:47:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 21:47:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 21:47:01] local.INFO: Cron job executed {"time":"2025-07-02 21:47:01"} 
[2025-07-02 21:47:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 21:47:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 21:47:03] local.INFO: Cron job executed {"time":"2025-07-02 21:47:03"} 
[2025-07-02 21:48:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 21:48:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 21:48:01] local.INFO: Cron job executed {"time":"2025-07-02 21:48:01"} 
[2025-07-02 21:48:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 21:48:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 21:48:02] local.INFO: Cron job executed {"time":"2025-07-02 21:48:02"} 
[2025-07-02 21:49:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 21:49:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 21:49:01] local.INFO: Cron job executed {"time":"2025-07-02 21:49:01"} 
[2025-07-02 21:49:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 21:49:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 21:49:02] local.INFO: Cron job executed {"time":"2025-07-02 21:49:02"} 
[2025-07-02 21:50:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 21:50:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 21:50:01] local.INFO: Cron job executed {"time":"2025-07-02 21:50:01"} 
[2025-07-02 21:50:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 21:50:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 21:50:02] local.INFO: Cron job executed {"time":"2025-07-02 21:50:02"} 
[2025-07-02 21:51:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 21:51:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 21:51:01] local.INFO: Cron job executed {"time":"2025-07-02 21:51:01"} 
[2025-07-02 21:51:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 21:51:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 21:51:02] local.INFO: Cron job executed {"time":"2025-07-02 21:51:02"} 
[2025-07-02 21:52:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 21:52:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 21:52:01] local.INFO: Cron job executed {"time":"2025-07-02 21:52:01"} 
[2025-07-02 21:52:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 21:52:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 21:52:02] local.INFO: Cron job executed {"time":"2025-07-02 21:52:02"} 
[2025-07-02 21:53:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 21:53:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 21:53:01] local.INFO: Cron job executed {"time":"2025-07-02 21:53:01"} 
[2025-07-02 21:53:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 21:53:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 21:53:03] local.INFO: Cron job executed {"time":"2025-07-02 21:53:03"} 
[2025-07-02 21:54:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 21:54:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 21:54:01] local.INFO: Cron job executed {"time":"2025-07-02 21:54:01"} 
[2025-07-02 21:54:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 21:54:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 21:54:02] local.INFO: Cron job executed {"time":"2025-07-02 21:54:02"} 
[2025-07-02 21:55:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 21:55:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 21:55:01] local.INFO: Cron job executed {"time":"2025-07-02 21:55:01"} 
[2025-07-02 21:55:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 21:55:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 21:55:02] local.INFO: Cron job executed {"time":"2025-07-02 21:55:02"} 
[2025-07-02 21:56:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 21:56:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 21:56:01] local.INFO: Cron job executed {"time":"2025-07-02 21:56:01"} 
[2025-07-02 21:56:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 21:56:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 21:56:03] local.INFO: Cron job executed {"time":"2025-07-02 21:56:03"} 
[2025-07-02 21:57:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 21:57:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 21:57:01] local.INFO: Cron job executed {"time":"2025-07-02 21:57:01"} 
[2025-07-02 21:57:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 21:57:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 21:57:02] local.INFO: Cron job executed {"time":"2025-07-02 21:57:02"} 
[2025-07-02 21:58:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 21:58:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 21:58:01] local.INFO: Cron job executed {"time":"2025-07-02 21:58:01"} 
[2025-07-02 21:58:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 21:58:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 21:58:03] local.INFO: Cron job executed {"time":"2025-07-02 21:58:03"} 
[2025-07-02 21:59:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 21:59:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 21:59:01] local.INFO: Cron job executed {"time":"2025-07-02 21:59:01"} 
[2025-07-02 21:59:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 21:59:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 21:59:02] local.INFO: Cron job executed {"time":"2025-07-02 21:59:02"} 
[2025-07-02 22:00:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 22:00:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 22:00:01] local.INFO: Cron job executed {"time":"2025-07-02 22:00:01"} 
[2025-07-02 22:00:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 22:00:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 22:00:02] local.INFO: Cron job executed {"time":"2025-07-02 22:00:02"} 
[2025-07-02 22:01:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 22:01:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 22:01:01] local.INFO: Cron job executed {"time":"2025-07-02 22:01:01"} 
[2025-07-02 22:01:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 22:01:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 22:01:03] local.INFO: Cron job executed {"time":"2025-07-02 22:01:03"} 
[2025-07-02 22:02:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 22:02:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 22:02:01] local.INFO: Cron job executed {"time":"2025-07-02 22:02:01"} 
[2025-07-02 22:02:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 22:02:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 22:02:02] local.INFO: Cron job executed {"time":"2025-07-02 22:02:02"} 
[2025-07-02 22:03:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 22:03:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 22:03:01] local.INFO: Cron job executed {"time":"2025-07-02 22:03:01"} 
[2025-07-02 22:03:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 22:03:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 22:03:03] local.INFO: Cron job executed {"time":"2025-07-02 22:03:03"} 
[2025-07-02 22:04:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 22:04:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 22:04:01] local.INFO: Cron job executed {"time":"2025-07-02 22:04:01"} 
[2025-07-02 22:04:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 22:04:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 22:04:02] local.INFO: Cron job executed {"time":"2025-07-02 22:04:02"} 
[2025-07-02 22:05:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 22:05:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 22:05:01] local.INFO: Cron job executed {"time":"2025-07-02 22:05:01"} 
[2025-07-02 22:05:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 22:05:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 22:05:03] local.INFO: Cron job executed {"time":"2025-07-02 22:05:03"} 
[2025-07-02 22:06:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 22:06:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 22:06:01] local.INFO: Cron job executed {"time":"2025-07-02 22:06:01"} 
[2025-07-02 22:06:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 22:06:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 22:06:02] local.INFO: Cron job executed {"time":"2025-07-02 22:06:02"} 
[2025-07-02 22:07:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 22:07:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 22:07:01] local.INFO: Cron job executed {"time":"2025-07-02 22:07:01"} 
[2025-07-02 22:07:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 22:07:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 22:07:02] local.INFO: Cron job executed {"time":"2025-07-02 22:07:02"} 
[2025-07-02 22:08:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 22:08:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 22:08:01] local.INFO: Cron job executed {"time":"2025-07-02 22:08:01"} 
[2025-07-02 22:08:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 22:08:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 22:08:02] local.INFO: Cron job executed {"time":"2025-07-02 22:08:02"} 
[2025-07-02 22:09:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 22:09:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 22:09:01] local.INFO: Cron job executed {"time":"2025-07-02 22:09:01"} 
[2025-07-02 22:09:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 22:09:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 22:09:02] local.INFO: Cron job executed {"time":"2025-07-02 22:09:02"} 
[2025-07-02 22:10:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 22:10:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 22:10:01] local.INFO: Cron job executed {"time":"2025-07-02 22:10:01"} 
[2025-07-02 22:10:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 22:10:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 22:10:02] local.INFO: Cron job executed {"time":"2025-07-02 22:10:02"} 
[2025-07-02 22:11:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 22:11:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 22:11:01] local.INFO: Cron job executed {"time":"2025-07-02 22:11:01"} 
[2025-07-02 22:11:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 22:11:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 22:11:02] local.INFO: Cron job executed {"time":"2025-07-02 22:11:02"} 
[2025-07-02 22:12:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 22:12:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 22:12:01] local.INFO: Cron job executed {"time":"2025-07-02 22:12:01"} 
[2025-07-02 22:12:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 22:12:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 22:12:02] local.INFO: Cron job executed {"time":"2025-07-02 22:12:02"} 
[2025-07-02 22:13:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 22:13:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 22:13:01] local.INFO: Cron job executed {"time":"2025-07-02 22:13:01"} 
[2025-07-02 22:13:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 22:13:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 22:13:02] local.INFO: Cron job executed {"time":"2025-07-02 22:13:02"} 
[2025-07-02 22:14:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 22:14:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 22:14:01] local.INFO: Cron job executed {"time":"2025-07-02 22:14:01"} 
[2025-07-02 22:14:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 22:14:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 22:14:02] local.INFO: Cron job executed {"time":"2025-07-02 22:14:02"} 
[2025-07-02 22:15:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 22:15:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 22:15:01] local.INFO: Cron job executed {"time":"2025-07-02 22:15:01"} 
[2025-07-02 22:15:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 22:15:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 22:15:02] local.INFO: Cron job executed {"time":"2025-07-02 22:15:02"} 
[2025-07-02 22:16:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 22:16:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 22:16:01] local.INFO: Cron job executed {"time":"2025-07-02 22:16:01"} 
[2025-07-02 22:16:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 22:16:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 22:16:02] local.INFO: Cron job executed {"time":"2025-07-02 22:16:02"} 
[2025-07-02 22:17:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 22:17:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 22:17:01] local.INFO: Cron job executed {"time":"2025-07-02 22:17:01"} 
[2025-07-02 22:17:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 22:17:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 22:17:02] local.INFO: Cron job executed {"time":"2025-07-02 22:17:02"} 
[2025-07-02 22:18:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 22:18:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 22:18:01] local.INFO: Cron job executed {"time":"2025-07-02 22:18:01"} 
[2025-07-02 22:18:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 22:18:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 22:18:03] local.INFO: Cron job executed {"time":"2025-07-02 22:18:03"} 
[2025-07-02 22:19:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 22:19:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 22:19:01] local.INFO: Cron job executed {"time":"2025-07-02 22:19:01"} 
[2025-07-02 22:19:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 22:19:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 22:19:02] local.INFO: Cron job executed {"time":"2025-07-02 22:19:02"} 
[2025-07-02 22:20:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 22:20:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 22:20:01] local.INFO: Cron job executed {"time":"2025-07-02 22:20:01"} 
[2025-07-02 22:20:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 22:20:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 22:20:03] local.INFO: Cron job executed {"time":"2025-07-02 22:20:03"} 
[2025-07-02 22:21:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 22:21:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 22:21:01] local.INFO: Cron job executed {"time":"2025-07-02 22:21:01"} 
[2025-07-02 22:21:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 22:21:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 22:21:03] local.INFO: Cron job executed {"time":"2025-07-02 22:21:03"} 
[2025-07-02 22:22:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 22:22:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 22:22:01] local.INFO: Cron job executed {"time":"2025-07-02 22:22:01"} 
[2025-07-02 22:22:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 22:22:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 22:22:03] local.INFO: Cron job executed {"time":"2025-07-02 22:22:03"} 
[2025-07-02 22:23:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 22:23:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 22:23:01] local.INFO: Cron job executed {"time":"2025-07-02 22:23:01"} 
[2025-07-02 22:23:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 22:23:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 22:23:02] local.INFO: Cron job executed {"time":"2025-07-02 22:23:02"} 
[2025-07-02 22:24:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 22:24:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 22:24:01] local.INFO: Cron job executed {"time":"2025-07-02 22:24:01"} 
[2025-07-02 22:24:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 22:24:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 22:24:02] local.INFO: Cron job executed {"time":"2025-07-02 22:24:02"} 
[2025-07-02 22:25:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 22:25:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 22:25:01] local.INFO: Cron job executed {"time":"2025-07-02 22:25:01"} 
[2025-07-02 22:25:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 22:25:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 22:25:02] local.INFO: Cron job executed {"time":"2025-07-02 22:25:02"} 
[2025-07-02 22:26:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 22:26:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 22:26:01] local.INFO: Cron job executed {"time":"2025-07-02 22:26:01"} 
[2025-07-02 22:26:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 22:26:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 22:26:02] local.INFO: Cron job executed {"time":"2025-07-02 22:26:02"} 
[2025-07-02 22:27:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 22:27:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 22:27:01] local.INFO: Cron job executed {"time":"2025-07-02 22:27:01"} 
[2025-07-02 22:27:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 22:27:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 22:27:02] local.INFO: Cron job executed {"time":"2025-07-02 22:27:02"} 
[2025-07-02 22:28:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 22:28:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 22:28:01] local.INFO: Cron job executed {"time":"2025-07-02 22:28:01"} 
[2025-07-02 22:28:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 22:28:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 22:28:02] local.INFO: Cron job executed {"time":"2025-07-02 22:28:02"} 
[2025-07-02 22:29:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 22:29:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 22:29:01] local.INFO: Cron job executed {"time":"2025-07-02 22:29:01"} 
[2025-07-02 22:29:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 22:29:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 22:29:02] local.INFO: Cron job executed {"time":"2025-07-02 22:29:02"} 
[2025-07-02 22:30:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 22:30:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 22:30:01] local.INFO: Cron job executed {"time":"2025-07-02 22:30:01"} 
[2025-07-02 22:30:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 22:30:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 22:30:03] local.INFO: Cron job executed {"time":"2025-07-02 22:30:03"} 
[2025-07-02 22:31:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 22:31:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 22:31:01] local.INFO: Cron job executed {"time":"2025-07-02 22:31:01"} 
[2025-07-02 22:31:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 22:31:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 22:31:02] local.INFO: Cron job executed {"time":"2025-07-02 22:31:02"} 
[2025-07-02 22:32:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 22:32:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 22:32:01] local.INFO: Cron job executed {"time":"2025-07-02 22:32:01"} 
[2025-07-02 22:32:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 22:32:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 22:32:02] local.INFO: Cron job executed {"time":"2025-07-02 22:32:02"} 
[2025-07-02 22:33:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 22:33:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 22:33:01] local.INFO: Cron job executed {"time":"2025-07-02 22:33:01"} 
[2025-07-02 22:33:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 22:33:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 22:33:02] local.INFO: Cron job executed {"time":"2025-07-02 22:33:02"} 
[2025-07-02 22:34:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 22:34:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 22:34:01] local.INFO: Cron job executed {"time":"2025-07-02 22:34:01"} 
[2025-07-02 22:34:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 22:34:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 22:34:02] local.INFO: Cron job executed {"time":"2025-07-02 22:34:02"} 
[2025-07-02 22:35:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 22:35:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 22:35:01] local.INFO: Cron job executed {"time":"2025-07-02 22:35:01"} 
[2025-07-02 22:35:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 22:35:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 22:35:02] local.INFO: Cron job executed {"time":"2025-07-02 22:35:02"} 
[2025-07-02 22:36:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 22:36:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 22:36:01] local.INFO: Cron job executed {"time":"2025-07-02 22:36:01"} 
[2025-07-02 22:36:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 22:36:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 22:36:02] local.INFO: Cron job executed {"time":"2025-07-02 22:36:02"} 
[2025-07-02 22:37:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 22:37:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 22:37:01] local.INFO: Cron job executed {"time":"2025-07-02 22:37:01"} 
[2025-07-02 22:37:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 22:37:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 22:37:02] local.INFO: Cron job executed {"time":"2025-07-02 22:37:02"} 
[2025-07-02 22:38:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 22:38:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 22:38:01] local.INFO: Cron job executed {"time":"2025-07-02 22:38:01"} 
[2025-07-02 22:38:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 22:38:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 22:38:02] local.INFO: Cron job executed {"time":"2025-07-02 22:38:02"} 
[2025-07-02 22:39:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 22:39:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 22:39:01] local.INFO: Cron job executed {"time":"2025-07-02 22:39:01"} 
[2025-07-02 22:39:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 22:39:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 22:39:02] local.INFO: Cron job executed {"time":"2025-07-02 22:39:02"} 
[2025-07-02 22:40:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 22:40:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 22:40:01] local.INFO: Cron job executed {"time":"2025-07-02 22:40:01"} 
[2025-07-02 22:40:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 22:40:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 22:40:03] local.INFO: Cron job executed {"time":"2025-07-02 22:40:03"} 
[2025-07-02 22:41:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 22:41:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 22:41:01] local.INFO: Cron job executed {"time":"2025-07-02 22:41:01"} 
[2025-07-02 22:41:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 22:41:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 22:41:03] local.INFO: Cron job executed {"time":"2025-07-02 22:41:03"} 
[2025-07-02 22:42:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 22:42:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 22:42:01] local.INFO: Cron job executed {"time":"2025-07-02 22:42:01"} 
[2025-07-02 22:42:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 22:42:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 22:42:02] local.INFO: Cron job executed {"time":"2025-07-02 22:42:02"} 
[2025-07-02 22:43:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 22:43:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 22:43:01] local.INFO: Cron job executed {"time":"2025-07-02 22:43:01"} 
[2025-07-02 22:43:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 22:43:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 22:43:02] local.INFO: Cron job executed {"time":"2025-07-02 22:43:02"} 
[2025-07-02 22:44:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 22:44:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 22:44:01] local.INFO: Cron job executed {"time":"2025-07-02 22:44:01"} 
[2025-07-02 22:44:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 22:44:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 22:44:02] local.INFO: Cron job executed {"time":"2025-07-02 22:44:02"} 
[2025-07-02 22:45:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 22:45:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 22:45:01] local.INFO: Cron job executed {"time":"2025-07-02 22:45:01"} 
[2025-07-02 22:45:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 22:45:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 22:45:02] local.INFO: Cron job executed {"time":"2025-07-02 22:45:02"} 
[2025-07-02 22:46:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 22:46:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 22:46:01] local.INFO: Cron job executed {"time":"2025-07-02 22:46:01"} 
[2025-07-02 22:46:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 22:46:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 22:46:02] local.INFO: Cron job executed {"time":"2025-07-02 22:46:02"} 
[2025-07-02 22:47:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 22:47:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 22:47:01] local.INFO: Cron job executed {"time":"2025-07-02 22:47:01"} 
[2025-07-02 22:47:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 22:47:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 22:47:02] local.INFO: Cron job executed {"time":"2025-07-02 22:47:02"} 
[2025-07-02 22:48:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 22:48:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 22:48:01] local.INFO: Cron job executed {"time":"2025-07-02 22:48:01"} 
[2025-07-02 22:48:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 22:48:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 22:48:02] local.INFO: Cron job executed {"time":"2025-07-02 22:48:02"} 
[2025-07-02 22:49:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 22:49:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 22:49:01] local.INFO: Cron job executed {"time":"2025-07-02 22:49:01"} 
[2025-07-02 22:49:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 22:49:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 22:49:02] local.INFO: Cron job executed {"time":"2025-07-02 22:49:02"} 
[2025-07-02 22:50:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 22:50:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 22:50:01] local.INFO: Cron job executed {"time":"2025-07-02 22:50:01"} 
[2025-07-02 22:50:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 22:50:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 22:50:02] local.INFO: Cron job executed {"time":"2025-07-02 22:50:02"} 
[2025-07-02 22:51:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 22:51:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 22:51:01] local.INFO: Cron job executed {"time":"2025-07-02 22:51:01"} 
[2025-07-02 22:51:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 22:51:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 22:51:02] local.INFO: Cron job executed {"time":"2025-07-02 22:51:02"} 
[2025-07-02 22:52:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 22:52:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 22:52:01] local.INFO: Cron job executed {"time":"2025-07-02 22:52:01"} 
[2025-07-02 22:52:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 22:52:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 22:52:02] local.INFO: Cron job executed {"time":"2025-07-02 22:52:02"} 
[2025-07-02 22:53:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 22:53:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 22:53:01] local.INFO: Cron job executed {"time":"2025-07-02 22:53:01"} 
[2025-07-02 22:53:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 22:53:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 22:53:02] local.INFO: Cron job executed {"time":"2025-07-02 22:53:02"} 
[2025-07-02 22:54:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 22:54:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 22:54:01] local.INFO: Cron job executed {"time":"2025-07-02 22:54:01"} 
[2025-07-02 22:54:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 22:54:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 22:54:02] local.INFO: Cron job executed {"time":"2025-07-02 22:54:02"} 
[2025-07-02 22:55:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 22:55:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 22:55:01] local.INFO: Cron job executed {"time":"2025-07-02 22:55:01"} 
[2025-07-02 22:55:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 22:55:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 22:55:02] local.INFO: Cron job executed {"time":"2025-07-02 22:55:02"} 
[2025-07-02 22:56:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 22:56:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 22:56:01] local.INFO: Cron job executed {"time":"2025-07-02 22:56:01"} 
[2025-07-02 22:56:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 22:56:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 22:56:03] local.INFO: Cron job executed {"time":"2025-07-02 22:56:03"} 
[2025-07-02 22:57:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 22:57:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 22:57:01] local.INFO: Cron job executed {"time":"2025-07-02 22:57:01"} 
[2025-07-02 22:57:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 22:57:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 22:57:02] local.INFO: Cron job executed {"time":"2025-07-02 22:57:02"} 
[2025-07-02 22:58:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 22:58:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 22:58:01] local.INFO: Cron job executed {"time":"2025-07-02 22:58:01"} 
[2025-07-02 22:58:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 22:58:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 22:58:02] local.INFO: Cron job executed {"time":"2025-07-02 22:58:02"} 
[2025-07-02 22:59:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 22:59:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 22:59:01] local.INFO: Cron job executed {"time":"2025-07-02 22:59:01"} 
[2025-07-02 22:59:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 22:59:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 22:59:02] local.INFO: Cron job executed {"time":"2025-07-02 22:59:02"} 
[2025-07-02 23:00:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 23:00:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 23:00:01] local.INFO: Cron job executed {"time":"2025-07-02 23:00:01"} 
[2025-07-02 23:00:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 23:00:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 23:00:02] local.INFO: Cron job executed {"time":"2025-07-02 23:00:02"} 
[2025-07-02 23:01:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 23:01:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 23:01:01] local.INFO: Cron job executed {"time":"2025-07-02 23:01:01"} 
[2025-07-02 23:01:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 23:01:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 23:01:02] local.INFO: Cron job executed {"time":"2025-07-02 23:01:02"} 
[2025-07-02 23:02:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 23:02:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 23:02:01] local.INFO: Cron job executed {"time":"2025-07-02 23:02:01"} 
[2025-07-02 23:02:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 23:02:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 23:02:02] local.INFO: Cron job executed {"time":"2025-07-02 23:02:02"} 
[2025-07-02 23:03:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 23:03:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 23:03:01] local.INFO: Cron job executed {"time":"2025-07-02 23:03:01"} 
[2025-07-02 23:03:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 23:03:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 23:03:02] local.INFO: Cron job executed {"time":"2025-07-02 23:03:02"} 
[2025-07-02 23:04:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 23:04:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 23:04:01] local.INFO: Cron job executed {"time":"2025-07-02 23:04:01"} 
[2025-07-02 23:04:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 23:04:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 23:04:02] local.INFO: Cron job executed {"time":"2025-07-02 23:04:02"} 
[2025-07-02 23:05:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 23:05:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 23:05:01] local.INFO: Cron job executed {"time":"2025-07-02 23:05:01"} 
[2025-07-02 23:05:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 23:05:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 23:05:02] local.INFO: Cron job executed {"time":"2025-07-02 23:05:02"} 
[2025-07-02 23:06:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 23:06:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 23:06:01] local.INFO: Cron job executed {"time":"2025-07-02 23:06:01"} 
[2025-07-02 23:06:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 23:06:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 23:06:02] local.INFO: Cron job executed {"time":"2025-07-02 23:06:02"} 
[2025-07-02 23:07:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 23:07:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 23:07:01] local.INFO: Cron job executed {"time":"2025-07-02 23:07:01"} 
[2025-07-02 23:07:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 23:07:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 23:07:02] local.INFO: Cron job executed {"time":"2025-07-02 23:07:02"} 
[2025-07-02 23:08:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 23:08:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 23:08:01] local.INFO: Cron job executed {"time":"2025-07-02 23:08:01"} 
[2025-07-02 23:08:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 23:08:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 23:08:02] local.INFO: Cron job executed {"time":"2025-07-02 23:08:02"} 
[2025-07-02 23:09:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 23:09:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 23:09:01] local.INFO: Cron job executed {"time":"2025-07-02 23:09:01"} 
[2025-07-02 23:09:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 23:09:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 23:09:02] local.INFO: Cron job executed {"time":"2025-07-02 23:09:02"} 
[2025-07-02 23:10:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 23:10:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 23:10:01] local.INFO: Cron job executed {"time":"2025-07-02 23:10:01"} 
[2025-07-02 23:10:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 23:10:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 23:10:02] local.INFO: Cron job executed {"time":"2025-07-02 23:10:02"} 
[2025-07-02 23:11:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 23:11:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 23:11:01] local.INFO: Cron job executed {"time":"2025-07-02 23:11:01"} 
[2025-07-02 23:11:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 23:11:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 23:11:03] local.INFO: Cron job executed {"time":"2025-07-02 23:11:03"} 
[2025-07-02 23:12:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 23:12:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 23:12:01] local.INFO: Cron job executed {"time":"2025-07-02 23:12:01"} 
[2025-07-02 23:12:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 23:12:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 23:12:02] local.INFO: Cron job executed {"time":"2025-07-02 23:12:02"} 
[2025-07-02 23:13:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 23:13:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 23:13:01] local.INFO: Cron job executed {"time":"2025-07-02 23:13:01"} 
[2025-07-02 23:13:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 23:13:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 23:13:02] local.INFO: Cron job executed {"time":"2025-07-02 23:13:02"} 
[2025-07-02 23:14:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 23:14:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 23:14:01] local.INFO: Cron job executed {"time":"2025-07-02 23:14:01"} 
[2025-07-02 23:14:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 23:14:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 23:14:03] local.INFO: Cron job executed {"time":"2025-07-02 23:14:03"} 
[2025-07-02 23:15:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 23:15:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 23:15:01] local.INFO: Cron job executed {"time":"2025-07-02 23:15:01"} 
[2025-07-02 23:15:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 23:15:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 23:15:02] local.INFO: Cron job executed {"time":"2025-07-02 23:15:02"} 
[2025-07-02 23:16:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 23:16:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 23:16:01] local.INFO: Cron job executed {"time":"2025-07-02 23:16:01"} 
[2025-07-02 23:16:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 23:16:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 23:16:02] local.INFO: Cron job executed {"time":"2025-07-02 23:16:02"} 
[2025-07-02 23:17:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 23:17:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 23:17:01] local.INFO: Cron job executed {"time":"2025-07-02 23:17:01"} 
[2025-07-02 23:17:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 23:17:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 23:17:02] local.INFO: Cron job executed {"time":"2025-07-02 23:17:02"} 
[2025-07-02 23:18:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 23:18:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 23:18:01] local.INFO: Cron job executed {"time":"2025-07-02 23:18:01"} 
[2025-07-02 23:18:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 23:18:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 23:18:02] local.INFO: Cron job executed {"time":"2025-07-02 23:18:02"} 
[2025-07-02 23:19:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 23:19:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 23:19:01] local.INFO: Cron job executed {"time":"2025-07-02 23:19:01"} 
[2025-07-02 23:19:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 23:19:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 23:19:02] local.INFO: Cron job executed {"time":"2025-07-02 23:19:02"} 
[2025-07-02 23:20:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 23:20:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 23:20:01] local.INFO: Cron job executed {"time":"2025-07-02 23:20:01"} 
[2025-07-02 23:20:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 23:20:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 23:20:02] local.INFO: Cron job executed {"time":"2025-07-02 23:20:02"} 
[2025-07-02 23:21:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 23:21:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 23:21:01] local.INFO: Cron job executed {"time":"2025-07-02 23:21:01"} 
[2025-07-02 23:21:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 23:21:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 23:21:03] local.INFO: Cron job executed {"time":"2025-07-02 23:21:03"} 
[2025-07-02 23:22:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 23:22:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 23:22:01] local.INFO: Cron job executed {"time":"2025-07-02 23:22:01"} 
[2025-07-02 23:22:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 23:22:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 23:22:02] local.INFO: Cron job executed {"time":"2025-07-02 23:22:02"} 
[2025-07-02 23:23:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 23:23:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 23:23:01] local.INFO: Cron job executed {"time":"2025-07-02 23:23:01"} 
[2025-07-02 23:23:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 23:23:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 23:23:02] local.INFO: Cron job executed {"time":"2025-07-02 23:23:02"} 
[2025-07-02 23:24:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 23:24:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 23:24:01] local.INFO: Cron job executed {"time":"2025-07-02 23:24:01"} 
[2025-07-02 23:24:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 23:24:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 23:24:02] local.INFO: Cron job executed {"time":"2025-07-02 23:24:02"} 
[2025-07-02 23:25:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 23:25:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 23:25:01] local.INFO: Cron job executed {"time":"2025-07-02 23:25:01"} 
[2025-07-02 23:25:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 23:25:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 23:25:03] local.INFO: Cron job executed {"time":"2025-07-02 23:25:03"} 
[2025-07-02 23:26:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 23:26:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 23:26:01] local.INFO: Cron job executed {"time":"2025-07-02 23:26:01"} 
[2025-07-02 23:26:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 23:26:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 23:26:03] local.INFO: Cron job executed {"time":"2025-07-02 23:26:03"} 
[2025-07-02 23:27:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 23:27:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 23:27:01] local.INFO: Cron job executed {"time":"2025-07-02 23:27:01"} 
[2025-07-02 23:27:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 23:27:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 23:27:02] local.INFO: Cron job executed {"time":"2025-07-02 23:27:02"} 
[2025-07-02 23:28:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 23:28:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 23:28:01] local.INFO: Cron job executed {"time":"2025-07-02 23:28:01"} 
[2025-07-02 23:28:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 23:28:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 23:28:02] local.INFO: Cron job executed {"time":"2025-07-02 23:28:02"} 
[2025-07-02 23:29:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 23:29:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 23:29:01] local.INFO: Cron job executed {"time":"2025-07-02 23:29:01"} 
[2025-07-02 23:29:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 23:29:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 23:29:02] local.INFO: Cron job executed {"time":"2025-07-02 23:29:02"} 
[2025-07-02 23:30:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 23:30:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 23:30:01] local.INFO: Cron job executed {"time":"2025-07-02 23:30:01"} 
[2025-07-02 23:30:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 23:30:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 23:30:02] local.INFO: Cron job executed {"time":"2025-07-02 23:30:02"} 
[2025-07-02 23:31:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 23:31:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 23:31:01] local.INFO: Cron job executed {"time":"2025-07-02 23:31:01"} 
[2025-07-02 23:31:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 23:31:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 23:31:02] local.INFO: Cron job executed {"time":"2025-07-02 23:31:02"} 
[2025-07-02 23:32:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 23:32:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 23:32:01] local.INFO: Cron job executed {"time":"2025-07-02 23:32:01"} 
[2025-07-02 23:32:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 23:32:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 23:32:02] local.INFO: Cron job executed {"time":"2025-07-02 23:32:02"} 
[2025-07-02 23:33:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 23:33:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 23:33:01] local.INFO: Cron job executed {"time":"2025-07-02 23:33:01"} 
[2025-07-02 23:33:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 23:33:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 23:33:03] local.INFO: Cron job executed {"time":"2025-07-02 23:33:03"} 
[2025-07-02 23:34:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 23:34:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 23:34:01] local.INFO: Cron job executed {"time":"2025-07-02 23:34:01"} 
[2025-07-02 23:34:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 23:34:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 23:34:02] local.INFO: Cron job executed {"time":"2025-07-02 23:34:02"} 
[2025-07-02 23:35:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 23:35:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 23:35:01] local.INFO: Cron job executed {"time":"2025-07-02 23:35:01"} 
[2025-07-02 23:35:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 23:35:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 23:35:02] local.INFO: Cron job executed {"time":"2025-07-02 23:35:02"} 
[2025-07-02 23:36:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 23:36:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 23:36:01] local.INFO: Cron job executed {"time":"2025-07-02 23:36:01"} 
[2025-07-02 23:36:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 23:36:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 23:36:03] local.INFO: Cron job executed {"time":"2025-07-02 23:36:03"} 
[2025-07-02 23:37:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 23:37:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 23:37:01] local.INFO: Cron job executed {"time":"2025-07-02 23:37:01"} 
[2025-07-02 23:37:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 23:37:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 23:37:02] local.INFO: Cron job executed {"time":"2025-07-02 23:37:02"} 
[2025-07-02 23:38:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 23:38:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 23:38:01] local.INFO: Cron job executed {"time":"2025-07-02 23:38:01"} 
[2025-07-02 23:38:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 23:38:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 23:38:02] local.INFO: Cron job executed {"time":"2025-07-02 23:38:02"} 
[2025-07-02 23:39:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 23:39:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 23:39:01] local.INFO: Cron job executed {"time":"2025-07-02 23:39:01"} 
[2025-07-02 23:39:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 23:39:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 23:39:02] local.INFO: Cron job executed {"time":"2025-07-02 23:39:02"} 
[2025-07-02 23:40:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 23:40:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 23:40:01] local.INFO: Cron job executed {"time":"2025-07-02 23:40:01"} 
[2025-07-02 23:40:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 23:40:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 23:40:02] local.INFO: Cron job executed {"time":"2025-07-02 23:40:02"} 
[2025-07-02 23:41:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 23:41:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 23:41:01] local.INFO: Cron job executed {"time":"2025-07-02 23:41:01"} 
[2025-07-02 23:41:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 23:41:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 23:41:03] local.INFO: Cron job executed {"time":"2025-07-02 23:41:03"} 
[2025-07-02 23:42:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 23:42:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 23:42:01] local.INFO: Cron job executed {"time":"2025-07-02 23:42:01"} 
[2025-07-02 23:42:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 23:42:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 23:42:02] local.INFO: Cron job executed {"time":"2025-07-02 23:42:02"} 
[2025-07-02 23:43:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 23:43:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 23:43:01] local.INFO: Cron job executed {"time":"2025-07-02 23:43:01"} 
[2025-07-02 23:43:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 23:43:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 23:43:03] local.INFO: Cron job executed {"time":"2025-07-02 23:43:03"} 
[2025-07-02 23:44:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 23:44:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 23:44:01] local.INFO: Cron job executed {"time":"2025-07-02 23:44:01"} 
[2025-07-02 23:44:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 23:44:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 23:44:03] local.INFO: Cron job executed {"time":"2025-07-02 23:44:03"} 
[2025-07-02 23:45:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 23:45:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 23:45:01] local.INFO: Cron job executed {"time":"2025-07-02 23:45:01"} 
[2025-07-02 23:45:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 23:45:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 23:45:02] local.INFO: Cron job executed {"time":"2025-07-02 23:45:02"} 
[2025-07-02 23:46:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 23:46:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 23:46:01] local.INFO: Cron job executed {"time":"2025-07-02 23:46:01"} 
[2025-07-02 23:46:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 23:46:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 23:46:02] local.INFO: Cron job executed {"time":"2025-07-02 23:46:02"} 
[2025-07-02 23:47:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 23:47:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 23:47:01] local.INFO: Cron job executed {"time":"2025-07-02 23:47:01"} 
[2025-07-02 23:47:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 23:47:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 23:47:02] local.INFO: Cron job executed {"time":"2025-07-02 23:47:02"} 
[2025-07-02 23:48:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 23:48:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 23:48:01] local.INFO: Cron job executed {"time":"2025-07-02 23:48:01"} 
[2025-07-02 23:48:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 23:48:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 23:48:02] local.INFO: Cron job executed {"time":"2025-07-02 23:48:02"} 
[2025-07-02 23:49:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 23:49:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 23:49:01] local.INFO: Cron job executed {"time":"2025-07-02 23:49:01"} 
[2025-07-02 23:49:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 23:49:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 23:49:02] local.INFO: Cron job executed {"time":"2025-07-02 23:49:02"} 
[2025-07-02 23:50:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 23:50:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 23:50:01] local.INFO: Cron job executed {"time":"2025-07-02 23:50:01"} 
[2025-07-02 23:50:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 23:50:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 23:50:02] local.INFO: Cron job executed {"time":"2025-07-02 23:50:02"} 
[2025-07-02 23:51:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 23:51:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 23:51:01] local.INFO: Cron job executed {"time":"2025-07-02 23:51:01"} 
[2025-07-02 23:51:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 23:51:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 23:51:02] local.INFO: Cron job executed {"time":"2025-07-02 23:51:02"} 
[2025-07-02 23:52:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 23:52:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 23:52:01] local.INFO: Cron job executed {"time":"2025-07-02 23:52:01"} 
[2025-07-02 23:52:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 23:52:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 23:52:02] local.INFO: Cron job executed {"time":"2025-07-02 23:52:02"} 
[2025-07-02 23:53:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 23:53:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 23:53:01] local.INFO: Cron job executed {"time":"2025-07-02 23:53:01"} 
[2025-07-02 23:53:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 23:53:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 23:53:02] local.INFO: Cron job executed {"time":"2025-07-02 23:53:02"} 
[2025-07-02 23:54:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 23:54:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 23:54:01] local.INFO: Cron job executed {"time":"2025-07-02 23:54:01"} 
[2025-07-02 23:54:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 23:54:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 23:54:02] local.INFO: Cron job executed {"time":"2025-07-02 23:54:02"} 
[2025-07-02 23:55:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 23:55:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 23:55:01] local.INFO: Cron job executed {"time":"2025-07-02 23:55:01"} 
[2025-07-02 23:55:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 23:55:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 23:55:02] local.INFO: Cron job executed {"time":"2025-07-02 23:55:02"} 
[2025-07-02 23:56:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 23:56:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 23:56:01] local.INFO: Cron job executed {"time":"2025-07-02 23:56:01"} 
[2025-07-02 23:56:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 23:56:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 23:56:02] local.INFO: Cron job executed {"time":"2025-07-02 23:56:02"} 
[2025-07-02 23:57:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 23:57:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 23:57:01] local.INFO: Cron job executed {"time":"2025-07-02 23:57:01"} 
[2025-07-02 23:57:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 23:57:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 23:57:02] local.INFO: Cron job executed {"time":"2025-07-02 23:57:02"} 
[2025-07-02 23:58:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 23:58:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 23:58:01] local.INFO: Cron job executed {"time":"2025-07-02 23:58:01"} 
[2025-07-02 23:58:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 23:58:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 23:58:02] local.INFO: Cron job executed {"time":"2025-07-02 23:58:02"} 
[2025-07-02 23:59:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 23:59:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 23:59:01] local.INFO: Cron job executed {"time":"2025-07-02 23:59:01"} 
[2025-07-02 23:59:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 23:59:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 23:59:03] local.INFO: Cron job executed {"time":"2025-07-02 23:59:03"} 
