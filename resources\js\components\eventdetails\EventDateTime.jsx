import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import timezone from "dayjs/plugin/timezone";
import advancedFormat from "dayjs/plugin/advancedFormat";
import useTranslations from "@/hooks/useTranslations";

dayjs.extend(utc);
dayjs.extend(timezone);
dayjs.extend(advancedFormat);

export default function EventDateTime({ event }) {
    const { translate } = useTranslations();

    const getAbbreviation = (date) => {
        const fullTzName = date.format("zzz");
        return fullTzName
            .split(" ")
            .map((word) => word[0])
            .join("");
    };

    const userTimezone = dayjs.tz.guess();

    // Convert time to both zones
    const timeInBaseTimezone = dayjs.tz(event.time, event.timezone);
    const timeInUserTimezone = timeInBaseTimezone.clone().tz(userTimezone);

    const baseAbbr = getAbbreviation(timeInBaseTimezone);
    const userAbbr = getAbbreviation(timeInUserTimezone);

    const isSameTz = baseAbbr === userAbbr;

    return (
        <span>
            <b>
                {dayjs(event.date).format("DD/MM/YYYY")} |{" "}
                {timeInUserTimezone.format("h:mm A")} {userAbbr}
            </b>
            {!isSameTz && (
                <i>
                    {" "}
                    ({timeInBaseTimezone.format("h:mm A")} {baseAbbr}{" "}
                    {translate("common.local", "Local")})
                </i>
            )}
        </span>
    );
}
