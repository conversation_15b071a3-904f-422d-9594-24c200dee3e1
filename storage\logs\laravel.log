[2025-07-02 18:08:03] local.ERROR: file:///E:/Ticketgol/Code/Ticketgol/bootstrap/ssr/ssr.js:10630
window.axios = axios$1;
^

ReferenceError: window is not defined
    at file:///E:/Ticketgol/Code/Ticketgol/bootstrap/ssr/ssr.js:10630:1
    at ModuleJob.run (node:internal/modules/esm/module_job:271:25)
    at async onImport.tracePromise.__proto__ (node:internal/modules/esm/loader:547:26)
    at async asyncRunEntryPointWithESMLoader (node:internal/modules/run_main:116:5)

Node.js v22.12.0
 {"exception":"[object] (Inertia\\Ssr\\SsrException(code: 0): file:///E:/Ticketgol/Code/Ticketgol/bootstrap/ssr/ssr.js:10630

window.axios = axios$1;

^



ReferenceError: window is not defined

    at file:///E:/Ticketgol/Code/Ticketgol/bootstrap/ssr/ssr.js:10630:1

    at ModuleJob.run (node:internal/modules/esm/module_job:271:25)

    at async onImport.tracePromise.__proto__ (node:internal/modules/esm/loader:547:26)

    at async asyncRunEntryPointWithESMLoader (node:internal/modules/run_main:116:5)



Node.js v22.12.0

 at E:\\Ticketgol\\Code\\Ticketgol\\vendor\\inertiajs\\inertia-laravel\\src\\Commands\\StartSsr.php:84)
[stacktrace]
#0 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Inertia\\Commands\\StartSsr->handle()
#1 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#2 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#3 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#4 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(694): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#5 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(213): Illuminate\\Container\\Container->call(Array)
#6 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#7 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(182): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#8 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#9 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Inertia\\Commands\\StartSsr), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#10 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(198): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#12 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1205): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#13 E:\\Ticketgol\\Code\\Ticketgol\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#14 {main}
"} 
[2025-07-02 18:10:38] local.ERROR: file:///E:/Ticketgol/Code/Ticketgol/bootstrap/ssr/ssr.js:10631
const htmlLang = document.documentElement.lang;
                 ^

ReferenceError: document is not defined
    at file:///E:/Ticketgol/Code/Ticketgol/bootstrap/ssr/ssr.js:10631:18
    at ModuleJob.run (node:internal/modules/esm/module_job:271:25)
    at async onImport.tracePromise.__proto__ (node:internal/modules/esm/loader:547:26)
    at async asyncRunEntryPointWithESMLoader (node:internal/modules/run_main:116:5)

Node.js v22.12.0
 {"exception":"[object] (Inertia\\Ssr\\SsrException(code: 0): file:///E:/Ticketgol/Code/Ticketgol/bootstrap/ssr/ssr.js:10631

const htmlLang = document.documentElement.lang;

                 ^



ReferenceError: document is not defined

    at file:///E:/Ticketgol/Code/Ticketgol/bootstrap/ssr/ssr.js:10631:18

    at ModuleJob.run (node:internal/modules/esm/module_job:271:25)

    at async onImport.tracePromise.__proto__ (node:internal/modules/esm/loader:547:26)

    at async asyncRunEntryPointWithESMLoader (node:internal/modules/run_main:116:5)



Node.js v22.12.0

 at E:\\Ticketgol\\Code\\Ticketgol\\vendor\\inertiajs\\inertia-laravel\\src\\Commands\\StartSsr.php:84)
[stacktrace]
#0 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Inertia\\Commands\\StartSsr->handle()
#1 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#2 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#3 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#4 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(694): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#5 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(213): Illuminate\\Container\\Container->call(Array)
#6 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#7 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(182): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#8 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#9 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Inertia\\Commands\\StartSsr), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#10 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(198): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#12 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1205): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#13 E:\\Ticketgol\\Code\\Ticketgol\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#14 {main}
"} 
[2025-07-02 18:48:27] local.ERROR: file:///E:/Ticketgol/Code/Ticketgol/bootstrap/ssr/ssr.js:10631
const htmlLang = document.documentElement.lang;
                 ^

ReferenceError: document is not defined
    at file:///E:/Ticketgol/Code/Ticketgol/bootstrap/ssr/ssr.js:10631:18
    at ModuleJob.run (node:internal/modules/esm/module_job:271:25)
    at async onImport.tracePromise.__proto__ (node:internal/modules/esm/loader:547:26)
    at async asyncRunEntryPointWithESMLoader (node:internal/modules/run_main:116:5)

Node.js v22.12.0
 {"exception":"[object] (Inertia\\Ssr\\SsrException(code: 0): file:///E:/Ticketgol/Code/Ticketgol/bootstrap/ssr/ssr.js:10631

const htmlLang = document.documentElement.lang;

                 ^



ReferenceError: document is not defined

    at file:///E:/Ticketgol/Code/Ticketgol/bootstrap/ssr/ssr.js:10631:18

    at ModuleJob.run (node:internal/modules/esm/module_job:271:25)

    at async onImport.tracePromise.__proto__ (node:internal/modules/esm/loader:547:26)

    at async asyncRunEntryPointWithESMLoader (node:internal/modules/run_main:116:5)



Node.js v22.12.0

 at E:\\Ticketgol\\Code\\Ticketgol\\vendor\\inertiajs\\inertia-laravel\\src\\Commands\\StartSsr.php:84)
[stacktrace]
#0 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Inertia\\Commands\\StartSsr->handle()
#1 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#2 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#3 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#4 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(694): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#5 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(213): Illuminate\\Container\\Container->call(Array)
#6 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#7 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(182): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#8 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#9 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Inertia\\Commands\\StartSsr), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#10 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(198): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#12 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1205): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#13 E:\\Ticketgol\\Code\\Ticketgol\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#14 {main}
"} 
[2025-07-02 18:53:03] local.ERROR: Error: createRoot(...): Target container is not a DOM element.
    at createRoot (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom.development.js:29384:11)
    at Object.createRoot$1 [as createRoot] (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom.development.js:29855:10)
    at exports.createRoot (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\client.js:12:16)
    at setup (file:///E:/Ticketgol/Code/Ticketgol/bootstrap/ssr/ssr.js:10685:20)
    at file:///E:/Ticketgol/Code/Ticketgol/node_modules/@inertiajs/react/dist/index.esm.js:1:1656
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async X (file:///E:/Ticketgol/Code/Ticketgol/node_modules/@inertiajs/react/dist/index.esm.js:1:1574)
    at async Server.<anonymous> (file:///E:/Ticketgol/Code/Ticketgol/node_modules/@inertiajs/core/dist/server.esm.js:1:527)
 {"exception":"[object] (Inertia\\Ssr\\SsrException(code: 0): Error: createRoot(...): Target container is not a DOM element.
    at createRoot (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom.development.js:29384:11)
    at Object.createRoot$1 [as createRoot] (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom.development.js:29855:10)
    at exports.createRoot (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\client.js:12:16)
    at setup (file:///E:/Ticketgol/Code/Ticketgol/bootstrap/ssr/ssr.js:10685:20)
    at file:///E:/Ticketgol/Code/Ticketgol/node_modules/@inertiajs/react/dist/index.esm.js:1:1656
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async X (file:///E:/Ticketgol/Code/Ticketgol/node_modules/@inertiajs/react/dist/index.esm.js:1:1574)
    at async Server.<anonymous> (file:///E:/Ticketgol/Code/Ticketgol/node_modules/@inertiajs/core/dist/server.esm.js:1:527)
 at E:\\Ticketgol\\Code\\Ticketgol\\vendor\\inertiajs\\inertia-laravel\\src\\Commands\\StartSsr.php:84)
[stacktrace]
#0 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Inertia\\Commands\\StartSsr->handle()
#1 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#2 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#3 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#4 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(694): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#5 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(213): Illuminate\\Container\\Container->call(Array)
#6 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#7 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(182): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#8 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#9 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Inertia\\Commands\\StartSsr), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#10 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(198): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#12 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1205): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#13 E:\\Ticketgol\\Code\\Ticketgol\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#14 {main}
"} 
[2025-07-02 18:58:02] local.ERROR: Error: hydrateRoot(...): Target container is not a DOM element.
    at hydrateRoot (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom.development.js:29442:11)
    at Object.hydrateRoot$1 [as hydrateRoot] (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom.development.js:29865:10)
    at exports.hydrateRoot (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\client.js:20:16)
    at setup (file:///E:/Ticketgol/Code/Ticketgol/bootstrap/ssr/ssr.js:10685:20)
    at file:///E:/Ticketgol/Code/Ticketgol/node_modules/@inertiajs/react/dist/index.esm.js:1:1656
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async X (file:///E:/Ticketgol/Code/Ticketgol/node_modules/@inertiajs/react/dist/index.esm.js:1:1574)
    at async Server.<anonymous> (file:///E:/Ticketgol/Code/Ticketgol/node_modules/@inertiajs/core/dist/server.esm.js:1:527)
 {"exception":"[object] (Inertia\\Ssr\\SsrException(code: 0): Error: hydrateRoot(...): Target container is not a DOM element.
    at hydrateRoot (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom.development.js:29442:11)
    at Object.hydrateRoot$1 [as hydrateRoot] (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom.development.js:29865:10)
    at exports.hydrateRoot (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\client.js:20:16)
    at setup (file:///E:/Ticketgol/Code/Ticketgol/bootstrap/ssr/ssr.js:10685:20)
    at file:///E:/Ticketgol/Code/Ticketgol/node_modules/@inertiajs/react/dist/index.esm.js:1:1656
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async X (file:///E:/Ticketgol/Code/Ticketgol/node_modules/@inertiajs/react/dist/index.esm.js:1:1574)
    at async Server.<anonymous> (file:///E:/Ticketgol/Code/Ticketgol/node_modules/@inertiajs/core/dist/server.esm.js:1:527)
 at E:\\Ticketgol\\Code\\Ticketgol\\vendor\\inertiajs\\inertia-laravel\\src\\Commands\\StartSsr.php:84)
[stacktrace]
#0 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Inertia\\Commands\\StartSsr->handle()
#1 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#2 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#3 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#4 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(694): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#5 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(213): Illuminate\\Container\\Container->call(Array)
#6 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#7 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(182): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#8 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#9 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Inertia\\Commands\\StartSsr), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#10 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(198): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#12 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1205): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#13 E:\\Ticketgol\\Code\\Ticketgol\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#14 {main}
"} 
[2025-07-02 19:03:33] local.ERROR: ReferenceError: document is not defined
    at useTranslations (file:///E:/Ticketgol/Code/Ticketgol/bootstrap/ssr/ssr.js:108:18)
    at Navbar (file:///E:/Ticketgol/Code/Ticketgol/bootstrap/ssr/ssr.js:255:25)
    at renderWithHooks (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:5662:16)
    at renderIndeterminateComponent (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:5736:15)
    at renderElement (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:5961:7)
    at renderNodeDestructiveImpl (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6119:11)
    at renderNodeDestructive (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6091:14)
    at renderNode (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6274:12)
    at renderChildrenArray (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6226:7)
    at renderNodeDestructiveImpl (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6156:7)
    at renderNodeDestructive (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6091:14)
    at renderNode (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6274:12)
    at renderHostElement (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:5646:3)
    at renderElement (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:5967:5)
    at renderNodeDestructiveImpl (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6119:11)
    at renderNodeDestructive (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6091:14)
    at renderIndeterminateComponent (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:5790:7)
    at renderElement (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:5961:7)
    at renderNodeDestructiveImpl (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6119:11)
    at renderNodeDestructive (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6091:14)
    at renderContextProvider (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:5935:3)
    at renderElement (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6032:11)
    at renderNodeDestructiveImpl (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6119:11)
    at renderNodeDestructive (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6091:14)
    at renderContextProvider (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:5935:3)
    at renderElement (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6032:11)
    at renderNodeDestructiveImpl (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6119:11)
    at renderNodeDestructive (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6091:14)
    at renderIndeterminateComponent (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:5790:7)
    at renderElement (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:5961:7)
    at renderNodeDestructiveImpl (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6119:11)
    at renderNodeDestructive (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6091:14)
    at renderContextProvider (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:5935:3)
    at renderElement (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6032:11)
    at renderNodeDestructiveImpl (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6119:11)
    at renderNodeDestructive (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6091:14)
    at renderIndeterminateComponent (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:5790:7)
    at renderElement (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:5961:7)
    at renderNodeDestructiveImpl (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6119:11)
    at renderNodeDestructive (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6091:14)
    at renderNode (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6274:12)
    at renderHostElement (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:5646:3)
    at renderElement (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:5967:5)
    at renderNodeDestructiveImpl (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6119:11)
    at renderNodeDestructive (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6091:14)
    at retryTask (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6543:5)
    at performWork (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6591:7)
    at E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6915:12
    at scheduleWork (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:78:3)
    at startWork (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6914:3)
 {"exception":"[object] (Inertia\\Ssr\\SsrException(code: 0): ReferenceError: document is not defined
    at useTranslations (file:///E:/Ticketgol/Code/Ticketgol/bootstrap/ssr/ssr.js:108:18)
    at Navbar (file:///E:/Ticketgol/Code/Ticketgol/bootstrap/ssr/ssr.js:255:25)
    at renderWithHooks (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:5662:16)
    at renderIndeterminateComponent (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:5736:15)
    at renderElement (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:5961:7)
    at renderNodeDestructiveImpl (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6119:11)
    at renderNodeDestructive (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6091:14)
    at renderNode (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6274:12)
    at renderChildrenArray (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6226:7)
    at renderNodeDestructiveImpl (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6156:7)
    at renderNodeDestructive (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6091:14)
    at renderNode (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6274:12)
    at renderHostElement (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:5646:3)
    at renderElement (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:5967:5)
    at renderNodeDestructiveImpl (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6119:11)
    at renderNodeDestructive (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6091:14)
    at renderIndeterminateComponent (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:5790:7)
    at renderElement (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:5961:7)
    at renderNodeDestructiveImpl (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6119:11)
    at renderNodeDestructive (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6091:14)
    at renderContextProvider (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:5935:3)
    at renderElement (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6032:11)
    at renderNodeDestructiveImpl (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6119:11)
    at renderNodeDestructive (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6091:14)
    at renderContextProvider (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:5935:3)
    at renderElement (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6032:11)
    at renderNodeDestructiveImpl (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6119:11)
    at renderNodeDestructive (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6091:14)
    at renderIndeterminateComponent (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:5790:7)
    at renderElement (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:5961:7)
    at renderNodeDestructiveImpl (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6119:11)
    at renderNodeDestructive (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6091:14)
    at renderContextProvider (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:5935:3)
    at renderElement (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6032:11)
    at renderNodeDestructiveImpl (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6119:11)
    at renderNodeDestructive (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6091:14)
    at renderIndeterminateComponent (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:5790:7)
    at renderElement (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:5961:7)
    at renderNodeDestructiveImpl (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6119:11)
    at renderNodeDestructive (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6091:14)
    at renderNode (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6274:12)
    at renderHostElement (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:5646:3)
    at renderElement (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:5967:5)
    at renderNodeDestructiveImpl (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6119:11)
    at renderNodeDestructive (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6091:14)
    at retryTask (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6543:5)
    at performWork (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6591:7)
    at E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6915:12
    at scheduleWork (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:78:3)
    at startWork (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6914:3)
 at E:\\Ticketgol\\Code\\Ticketgol\\vendor\\inertiajs\\inertia-laravel\\src\\Commands\\StartSsr.php:84)
[stacktrace]
#0 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Inertia\\Commands\\StartSsr->handle()
#1 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#2 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#3 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#4 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(694): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#5 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(213): Illuminate\\Container\\Container->call(Array)
#6 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#7 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(182): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#8 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#9 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Inertia\\Commands\\StartSsr), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#10 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(198): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#12 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1205): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#13 E:\\Ticketgol\\Code\\Ticketgol\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#14 {main}
"} 
[2025-07-02 19:07:30] local.ERROR: ReferenceError: document is not defined
    at useTranslations (file:///E:/Ticketgol/Code/Ticketgol/bootstrap/ssr/ssr.js:108:18)
    at Navbar (file:///E:/Ticketgol/Code/Ticketgol/bootstrap/ssr/ssr.js:255:25)
    at renderWithHooks (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:5662:16)
    at renderIndeterminateComponent (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:5736:15)
    at renderElement (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:5961:7)
    at renderNodeDestructiveImpl (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6119:11)
    at renderNodeDestructive (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6091:14)
    at renderNode (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6274:12)
    at renderChildrenArray (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6226:7)
    at renderNodeDestructiveImpl (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6156:7)
    at renderNodeDestructive (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6091:14)
    at renderNode (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6274:12)
    at renderHostElement (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:5646:3)
    at renderElement (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:5967:5)
    at renderNodeDestructiveImpl (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6119:11)
    at renderNodeDestructive (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6091:14)
    at renderIndeterminateComponent (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:5790:7)
    at renderElement (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:5961:7)
    at renderNodeDestructiveImpl (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6119:11)
    at renderNodeDestructive (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6091:14)
    at renderContextProvider (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:5935:3)
    at renderElement (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6032:11)
    at renderNodeDestructiveImpl (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6119:11)
    at renderNodeDestructive (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6091:14)
    at renderContextProvider (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:5935:3)
    at renderElement (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6032:11)
    at renderNodeDestructiveImpl (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6119:11)
    at renderNodeDestructive (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6091:14)
    at renderIndeterminateComponent (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:5790:7)
    at renderElement (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:5961:7)
    at renderNodeDestructiveImpl (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6119:11)
    at renderNodeDestructive (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6091:14)
    at renderContextProvider (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:5935:3)
    at renderElement (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6032:11)
    at renderNodeDestructiveImpl (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6119:11)
    at renderNodeDestructive (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6091:14)
    at renderIndeterminateComponent (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:5790:7)
    at renderElement (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:5961:7)
    at renderNodeDestructiveImpl (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6119:11)
    at renderNodeDestructive (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6091:14)
    at renderNode (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6274:12)
    at renderHostElement (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:5646:3)
    at renderElement (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:5967:5)
    at renderNodeDestructiveImpl (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6119:11)
    at renderNodeDestructive (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6091:14)
    at retryTask (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6543:5)
    at performWork (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6591:7)
    at E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6915:12
    at scheduleWork (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:78:3)
    at startWork (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6914:3)
 {"exception":"[object] (Inertia\\Ssr\\SsrException(code: 0): ReferenceError: document is not defined
    at useTranslations (file:///E:/Ticketgol/Code/Ticketgol/bootstrap/ssr/ssr.js:108:18)
    at Navbar (file:///E:/Ticketgol/Code/Ticketgol/bootstrap/ssr/ssr.js:255:25)
    at renderWithHooks (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:5662:16)
    at renderIndeterminateComponent (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:5736:15)
    at renderElement (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:5961:7)
    at renderNodeDestructiveImpl (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6119:11)
    at renderNodeDestructive (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6091:14)
    at renderNode (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6274:12)
    at renderChildrenArray (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6226:7)
    at renderNodeDestructiveImpl (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6156:7)
    at renderNodeDestructive (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6091:14)
    at renderNode (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6274:12)
    at renderHostElement (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:5646:3)
    at renderElement (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:5967:5)
    at renderNodeDestructiveImpl (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6119:11)
    at renderNodeDestructive (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6091:14)
    at renderIndeterminateComponent (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:5790:7)
    at renderElement (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:5961:7)
    at renderNodeDestructiveImpl (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6119:11)
    at renderNodeDestructive (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6091:14)
    at renderContextProvider (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:5935:3)
    at renderElement (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6032:11)
    at renderNodeDestructiveImpl (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6119:11)
    at renderNodeDestructive (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6091:14)
    at renderContextProvider (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:5935:3)
    at renderElement (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6032:11)
    at renderNodeDestructiveImpl (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6119:11)
    at renderNodeDestructive (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6091:14)
    at renderIndeterminateComponent (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:5790:7)
    at renderElement (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:5961:7)
    at renderNodeDestructiveImpl (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6119:11)
    at renderNodeDestructive (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6091:14)
    at renderContextProvider (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:5935:3)
    at renderElement (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6032:11)
    at renderNodeDestructiveImpl (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6119:11)
    at renderNodeDestructive (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6091:14)
    at renderIndeterminateComponent (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:5790:7)
    at renderElement (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:5961:7)
    at renderNodeDestructiveImpl (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6119:11)
    at renderNodeDestructive (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6091:14)
    at renderNode (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6274:12)
    at renderHostElement (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:5646:3)
    at renderElement (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:5967:5)
    at renderNodeDestructiveImpl (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6119:11)
    at renderNodeDestructive (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6091:14)
    at retryTask (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6543:5)
    at performWork (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6591:7)
    at E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6915:12
    at scheduleWork (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:78:3)
    at startWork (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6914:3)
 at E:\\Ticketgol\\Code\\Ticketgol\\vendor\\inertiajs\\inertia-laravel\\src\\Commands\\StartSsr.php:84)
[stacktrace]
#0 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Inertia\\Commands\\StartSsr->handle()
#1 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#2 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#3 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#4 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(694): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#5 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(213): Illuminate\\Container\\Container->call(Array)
#6 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#7 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(182): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#8 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#9 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Inertia\\Commands\\StartSsr), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#10 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(198): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#12 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1205): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#13 E:\\Ticketgol\\Code\\Ticketgol\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#14 {main}
"} 
[2025-07-02 19:09:43] local.ERROR: ReferenceError: document is not defined
    at useTranslations (file:///E:/Ticketgol/Code/Ticketgol/bootstrap/ssr/ssr.js:108:18)
    at Navbar (file:///E:/Ticketgol/Code/Ticketgol/bootstrap/ssr/ssr.js:255:25)
    at renderWithHooks (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:5662:16)
    at renderIndeterminateComponent (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:5736:15)
    at renderElement (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:5961:7)
    at renderNodeDestructiveImpl (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6119:11)
    at renderNodeDestructive (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6091:14)
    at renderNode (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6274:12)
    at renderChildrenArray (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6226:7)
    at renderNodeDestructiveImpl (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6156:7)
    at renderNodeDestructive (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6091:14)
    at renderNode (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6274:12)
    at renderHostElement (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:5646:3)
    at renderElement (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:5967:5)
    at renderNodeDestructiveImpl (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6119:11)
    at renderNodeDestructive (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6091:14)
    at renderIndeterminateComponent (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:5790:7)
    at renderElement (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:5961:7)
    at renderNodeDestructiveImpl (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6119:11)
    at renderNodeDestructive (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6091:14)
    at renderContextProvider (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:5935:3)
    at renderElement (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6032:11)
    at renderNodeDestructiveImpl (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6119:11)
    at renderNodeDestructive (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6091:14)
    at renderContextProvider (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:5935:3)
    at renderElement (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6032:11)
    at renderNodeDestructiveImpl (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6119:11)
    at renderNodeDestructive (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6091:14)
    at renderIndeterminateComponent (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:5790:7)
    at renderElement (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:5961:7)
    at renderNodeDestructiveImpl (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6119:11)
    at renderNodeDestructive (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6091:14)
    at renderContextProvider (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:5935:3)
    at renderElement (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6032:11)
    at renderNodeDestructiveImpl (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6119:11)
    at renderNodeDestructive (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6091:14)
    at renderIndeterminateComponent (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:5790:7)
    at renderElement (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:5961:7)
    at renderNodeDestructiveImpl (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6119:11)
    at renderNodeDestructive (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6091:14)
    at renderNode (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6274:12)
    at renderHostElement (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:5646:3)
    at renderElement (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:5967:5)
    at renderNodeDestructiveImpl (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6119:11)
    at renderNodeDestructive (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6091:14)
    at retryTask (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6543:5)
    at performWork (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6591:7)
    at E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6915:12
    at scheduleWork (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:78:3)
    at startWork (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6914:3)
 {"exception":"[object] (Inertia\\Ssr\\SsrException(code: 0): ReferenceError: document is not defined
    at useTranslations (file:///E:/Ticketgol/Code/Ticketgol/bootstrap/ssr/ssr.js:108:18)
    at Navbar (file:///E:/Ticketgol/Code/Ticketgol/bootstrap/ssr/ssr.js:255:25)
    at renderWithHooks (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:5662:16)
    at renderIndeterminateComponent (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:5736:15)
    at renderElement (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:5961:7)
    at renderNodeDestructiveImpl (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6119:11)
    at renderNodeDestructive (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6091:14)
    at renderNode (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6274:12)
    at renderChildrenArray (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6226:7)
    at renderNodeDestructiveImpl (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6156:7)
    at renderNodeDestructive (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6091:14)
    at renderNode (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6274:12)
    at renderHostElement (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:5646:3)
    at renderElement (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:5967:5)
    at renderNodeDestructiveImpl (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6119:11)
    at renderNodeDestructive (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6091:14)
    at renderIndeterminateComponent (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:5790:7)
    at renderElement (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:5961:7)
    at renderNodeDestructiveImpl (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6119:11)
    at renderNodeDestructive (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6091:14)
    at renderContextProvider (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:5935:3)
    at renderElement (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6032:11)
    at renderNodeDestructiveImpl (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6119:11)
    at renderNodeDestructive (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6091:14)
    at renderContextProvider (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:5935:3)
    at renderElement (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6032:11)
    at renderNodeDestructiveImpl (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6119:11)
    at renderNodeDestructive (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6091:14)
    at renderIndeterminateComponent (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:5790:7)
    at renderElement (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:5961:7)
    at renderNodeDestructiveImpl (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6119:11)
    at renderNodeDestructive (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6091:14)
    at renderContextProvider (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:5935:3)
    at renderElement (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6032:11)
    at renderNodeDestructiveImpl (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6119:11)
    at renderNodeDestructive (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6091:14)
    at renderIndeterminateComponent (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:5790:7)
    at renderElement (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:5961:7)
    at renderNodeDestructiveImpl (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6119:11)
    at renderNodeDestructive (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6091:14)
    at renderNode (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6274:12)
    at renderHostElement (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:5646:3)
    at renderElement (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:5967:5)
    at renderNodeDestructiveImpl (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6119:11)
    at renderNodeDestructive (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6091:14)
    at retryTask (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6543:5)
    at performWork (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6591:7)
    at E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6915:12
    at scheduleWork (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:78:3)
    at startWork (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6914:3)
 at E:\\Ticketgol\\Code\\Ticketgol\\vendor\\inertiajs\\inertia-laravel\\src\\Commands\\StartSsr.php:84)
[stacktrace]
#0 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Inertia\\Commands\\StartSsr->handle()
#1 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#2 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#3 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#4 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(694): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#5 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(213): Illuminate\\Container\\Container->call(Array)
#6 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#7 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(182): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#8 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#9 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Inertia\\Commands\\StartSsr), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#10 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(198): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#12 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1205): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#13 E:\\Ticketgol\\Code\\Ticketgol\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#14 {main}
"} 
[2025-07-02 19:14:21] local.ERROR: ReferenceError: document is not defined
    at useTranslations (file:///E:/Ticketgol/Code/Ticketgol/bootstrap/ssr/ssr.js:108:18)
    at Navbar (file:///E:/Ticketgol/Code/Ticketgol/bootstrap/ssr/ssr.js:255:25)
    at renderWithHooks (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:5662:16)
    at renderIndeterminateComponent (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:5736:15)
    at renderElement (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:5961:7)
    at renderNodeDestructiveImpl (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6119:11)
    at renderNodeDestructive (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6091:14)
    at renderNode (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6274:12)
    at renderChildrenArray (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6226:7)
    at renderNodeDestructiveImpl (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6156:7)
    at renderNodeDestructive (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6091:14)
    at renderNode (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6274:12)
    at renderHostElement (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:5646:3)
    at renderElement (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:5967:5)
    at renderNodeDestructiveImpl (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6119:11)
    at renderNodeDestructive (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6091:14)
    at renderIndeterminateComponent (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:5790:7)
    at renderElement (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:5961:7)
    at renderNodeDestructiveImpl (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6119:11)
    at renderNodeDestructive (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6091:14)
    at renderContextProvider (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:5935:3)
    at renderElement (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6032:11)
    at renderNodeDestructiveImpl (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6119:11)
    at renderNodeDestructive (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6091:14)
    at renderContextProvider (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:5935:3)
    at renderElement (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6032:11)
    at renderNodeDestructiveImpl (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6119:11)
    at renderNodeDestructive (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6091:14)
    at renderIndeterminateComponent (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:5790:7)
    at renderElement (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:5961:7)
    at renderNodeDestructiveImpl (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6119:11)
    at renderNodeDestructive (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6091:14)
    at renderContextProvider (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:5935:3)
    at renderElement (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6032:11)
    at renderNodeDestructiveImpl (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6119:11)
    at renderNodeDestructive (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6091:14)
    at renderIndeterminateComponent (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:5790:7)
    at renderElement (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:5961:7)
    at renderNodeDestructiveImpl (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6119:11)
    at renderNodeDestructive (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6091:14)
    at renderNode (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6274:12)
    at renderHostElement (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:5646:3)
    at renderElement (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:5967:5)
    at renderNodeDestructiveImpl (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6119:11)
    at renderNodeDestructive (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6091:14)
    at retryTask (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6543:5)
    at performWork (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6591:7)
    at E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6915:12
    at scheduleWork (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:78:3)
    at startWork (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6914:3)
 {"exception":"[object] (Inertia\\Ssr\\SsrException(code: 0): ReferenceError: document is not defined
    at useTranslations (file:///E:/Ticketgol/Code/Ticketgol/bootstrap/ssr/ssr.js:108:18)
    at Navbar (file:///E:/Ticketgol/Code/Ticketgol/bootstrap/ssr/ssr.js:255:25)
    at renderWithHooks (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:5662:16)
    at renderIndeterminateComponent (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:5736:15)
    at renderElement (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:5961:7)
    at renderNodeDestructiveImpl (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6119:11)
    at renderNodeDestructive (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6091:14)
    at renderNode (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6274:12)
    at renderChildrenArray (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6226:7)
    at renderNodeDestructiveImpl (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6156:7)
    at renderNodeDestructive (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6091:14)
    at renderNode (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6274:12)
    at renderHostElement (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:5646:3)
    at renderElement (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:5967:5)
    at renderNodeDestructiveImpl (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6119:11)
    at renderNodeDestructive (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6091:14)
    at renderIndeterminateComponent (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:5790:7)
    at renderElement (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:5961:7)
    at renderNodeDestructiveImpl (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6119:11)
    at renderNodeDestructive (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6091:14)
    at renderContextProvider (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:5935:3)
    at renderElement (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6032:11)
    at renderNodeDestructiveImpl (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6119:11)
    at renderNodeDestructive (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6091:14)
    at renderContextProvider (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:5935:3)
    at renderElement (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6032:11)
    at renderNodeDestructiveImpl (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6119:11)
    at renderNodeDestructive (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6091:14)
    at renderIndeterminateComponent (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:5790:7)
    at renderElement (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:5961:7)
    at renderNodeDestructiveImpl (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6119:11)
    at renderNodeDestructive (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6091:14)
    at renderContextProvider (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:5935:3)
    at renderElement (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6032:11)
    at renderNodeDestructiveImpl (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6119:11)
    at renderNodeDestructive (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6091:14)
    at renderIndeterminateComponent (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:5790:7)
    at renderElement (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:5961:7)
    at renderNodeDestructiveImpl (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6119:11)
    at renderNodeDestructive (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6091:14)
    at renderNode (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6274:12)
    at renderHostElement (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:5646:3)
    at renderElement (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:5967:5)
    at renderNodeDestructiveImpl (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6119:11)
    at renderNodeDestructive (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6091:14)
    at retryTask (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6543:5)
    at performWork (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6591:7)
    at E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6915:12
    at scheduleWork (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:78:3)
    at startWork (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6914:3)
 at E:\\Ticketgol\\Code\\Ticketgol\\vendor\\inertiajs\\inertia-laravel\\src\\Commands\\StartSsr.php:84)
[stacktrace]
#0 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Inertia\\Commands\\StartSsr->handle()
#1 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#2 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#3 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#4 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(694): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#5 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(213): Illuminate\\Container\\Container->call(Array)
#6 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#7 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(182): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#8 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#9 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Inertia\\Commands\\StartSsr), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#10 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(198): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#12 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1205): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#13 E:\\Ticketgol\\Code\\Ticketgol\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#14 {main}
"} 
[2025-07-02 19:15:27] local.ERROR: ReferenceError: document is not defined
    at useTranslations (file:///E:/Ticketgol/Code/Ticketgol/bootstrap/ssr/ssr.js:108:18)
    at Navbar (file:///E:/Ticketgol/Code/Ticketgol/bootstrap/ssr/ssr.js:255:25)
    at renderWithHooks (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:5662:16)
    at renderIndeterminateComponent (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:5736:15)
    at renderElement (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:5961:7)
    at renderNodeDestructiveImpl (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6119:11)
    at renderNodeDestructive (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6091:14)
    at renderNode (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6274:12)
    at renderChildrenArray (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6226:7)
    at renderNodeDestructiveImpl (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6156:7)
    at renderNodeDestructive (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6091:14)
    at renderNode (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6274:12)
    at renderHostElement (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:5646:3)
    at renderElement (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:5967:5)
    at renderNodeDestructiveImpl (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6119:11)
    at renderNodeDestructive (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6091:14)
    at renderIndeterminateComponent (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:5790:7)
    at renderElement (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:5961:7)
    at renderNodeDestructiveImpl (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6119:11)
    at renderNodeDestructive (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6091:14)
    at renderContextProvider (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:5935:3)
    at renderElement (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6032:11)
    at renderNodeDestructiveImpl (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6119:11)
    at renderNodeDestructive (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6091:14)
    at renderContextProvider (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:5935:3)
    at renderElement (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6032:11)
    at renderNodeDestructiveImpl (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6119:11)
    at renderNodeDestructive (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6091:14)
    at renderIndeterminateComponent (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:5790:7)
    at renderElement (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:5961:7)
    at renderNodeDestructiveImpl (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6119:11)
    at renderNodeDestructive (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6091:14)
    at renderContextProvider (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:5935:3)
    at renderElement (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6032:11)
    at renderNodeDestructiveImpl (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6119:11)
    at renderNodeDestructive (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6091:14)
    at renderIndeterminateComponent (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:5790:7)
    at renderElement (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:5961:7)
    at renderNodeDestructiveImpl (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6119:11)
    at renderNodeDestructive (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6091:14)
    at renderNode (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6274:12)
    at renderHostElement (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:5646:3)
    at renderElement (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:5967:5)
    at renderNodeDestructiveImpl (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6119:11)
    at renderNodeDestructive (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6091:14)
    at retryTask (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6543:5)
    at performWork (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6591:7)
    at E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6915:12
    at scheduleWork (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:78:3)
    at startWork (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6914:3)
 {"exception":"[object] (Inertia\\Ssr\\SsrException(code: 0): ReferenceError: document is not defined
    at useTranslations (file:///E:/Ticketgol/Code/Ticketgol/bootstrap/ssr/ssr.js:108:18)
    at Navbar (file:///E:/Ticketgol/Code/Ticketgol/bootstrap/ssr/ssr.js:255:25)
    at renderWithHooks (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:5662:16)
    at renderIndeterminateComponent (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:5736:15)
    at renderElement (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:5961:7)
    at renderNodeDestructiveImpl (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6119:11)
    at renderNodeDestructive (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6091:14)
    at renderNode (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6274:12)
    at renderChildrenArray (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6226:7)
    at renderNodeDestructiveImpl (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6156:7)
    at renderNodeDestructive (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6091:14)
    at renderNode (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6274:12)
    at renderHostElement (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:5646:3)
    at renderElement (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:5967:5)
    at renderNodeDestructiveImpl (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6119:11)
    at renderNodeDestructive (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6091:14)
    at renderIndeterminateComponent (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:5790:7)
    at renderElement (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:5961:7)
    at renderNodeDestructiveImpl (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6119:11)
    at renderNodeDestructive (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6091:14)
    at renderContextProvider (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:5935:3)
    at renderElement (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6032:11)
    at renderNodeDestructiveImpl (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6119:11)
    at renderNodeDestructive (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6091:14)
    at renderContextProvider (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:5935:3)
    at renderElement (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6032:11)
    at renderNodeDestructiveImpl (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6119:11)
    at renderNodeDestructive (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6091:14)
    at renderIndeterminateComponent (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:5790:7)
    at renderElement (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:5961:7)
    at renderNodeDestructiveImpl (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6119:11)
    at renderNodeDestructive (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6091:14)
    at renderContextProvider (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:5935:3)
    at renderElement (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6032:11)
    at renderNodeDestructiveImpl (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6119:11)
    at renderNodeDestructive (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6091:14)
    at renderIndeterminateComponent (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:5790:7)
    at renderElement (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:5961:7)
    at renderNodeDestructiveImpl (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6119:11)
    at renderNodeDestructive (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6091:14)
    at renderNode (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6274:12)
    at renderHostElement (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:5646:3)
    at renderElement (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:5967:5)
    at renderNodeDestructiveImpl (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6119:11)
    at renderNodeDestructive (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6091:14)
    at retryTask (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6543:5)
    at performWork (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6591:7)
    at E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6915:12
    at scheduleWork (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:78:3)
    at startWork (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6914:3)
 at E:\\Ticketgol\\Code\\Ticketgol\\vendor\\inertiajs\\inertia-laravel\\src\\Commands\\StartSsr.php:84)
[stacktrace]
#0 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Inertia\\Commands\\StartSsr->handle()
#1 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#2 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#3 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#4 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(694): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#5 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(213): Illuminate\\Container\\Container->call(Array)
#6 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#7 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(182): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#8 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#9 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Inertia\\Commands\\StartSsr), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#10 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(198): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#12 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1205): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#13 E:\\Ticketgol\\Code\\Ticketgol\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#14 {main}
"} 
[2025-07-02 19:17:02] local.ERROR: ReferenceError: document is not defined
    at useTranslations (file:///E:/Ticketgol/Code/Ticketgol/bootstrap/ssr/ssr.js:108:18)
    at Navbar (file:///E:/Ticketgol/Code/Ticketgol/bootstrap/ssr/ssr.js:255:25)
    at renderWithHooks (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:5662:16)
    at renderIndeterminateComponent (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:5736:15)
    at renderElement (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:5961:7)
    at renderNodeDestructiveImpl (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6119:11)
    at renderNodeDestructive (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6091:14)
    at renderNode (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6274:12)
    at renderChildrenArray (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6226:7)
    at renderNodeDestructiveImpl (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6156:7)
    at renderNodeDestructive (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6091:14)
    at renderNode (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6274:12)
    at renderHostElement (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:5646:3)
    at renderElement (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:5967:5)
    at renderNodeDestructiveImpl (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6119:11)
    at renderNodeDestructive (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6091:14)
    at renderIndeterminateComponent (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:5790:7)
    at renderElement (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:5961:7)
    at renderNodeDestructiveImpl (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6119:11)
    at renderNodeDestructive (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6091:14)
    at renderContextProvider (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:5935:3)
    at renderElement (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6032:11)
    at renderNodeDestructiveImpl (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6119:11)
    at renderNodeDestructive (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6091:14)
    at renderContextProvider (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:5935:3)
    at renderElement (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6032:11)
    at renderNodeDestructiveImpl (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6119:11)
    at renderNodeDestructive (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6091:14)
    at renderIndeterminateComponent (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:5790:7)
    at renderElement (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:5961:7)
    at renderNodeDestructiveImpl (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6119:11)
    at renderNodeDestructive (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6091:14)
    at renderContextProvider (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:5935:3)
    at renderElement (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6032:11)
    at renderNodeDestructiveImpl (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6119:11)
    at renderNodeDestructive (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6091:14)
    at renderIndeterminateComponent (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:5790:7)
    at renderElement (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:5961:7)
    at renderNodeDestructiveImpl (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6119:11)
    at renderNodeDestructive (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6091:14)
    at renderNode (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6274:12)
    at renderHostElement (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:5646:3)
    at renderElement (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:5967:5)
    at renderNodeDestructiveImpl (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6119:11)
    at renderNodeDestructive (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6091:14)
    at retryTask (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6543:5)
    at performWork (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6591:7)
    at E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6915:12
    at scheduleWork (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:78:3)
    at startWork (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6914:3)
 {"exception":"[object] (Inertia\\Ssr\\SsrException(code: 0): ReferenceError: document is not defined
    at useTranslations (file:///E:/Ticketgol/Code/Ticketgol/bootstrap/ssr/ssr.js:108:18)
    at Navbar (file:///E:/Ticketgol/Code/Ticketgol/bootstrap/ssr/ssr.js:255:25)
    at renderWithHooks (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:5662:16)
    at renderIndeterminateComponent (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:5736:15)
    at renderElement (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:5961:7)
    at renderNodeDestructiveImpl (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6119:11)
    at renderNodeDestructive (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6091:14)
    at renderNode (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6274:12)
    at renderChildrenArray (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6226:7)
    at renderNodeDestructiveImpl (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6156:7)
    at renderNodeDestructive (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6091:14)
    at renderNode (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6274:12)
    at renderHostElement (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:5646:3)
    at renderElement (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:5967:5)
    at renderNodeDestructiveImpl (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6119:11)
    at renderNodeDestructive (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6091:14)
    at renderIndeterminateComponent (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:5790:7)
    at renderElement (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:5961:7)
    at renderNodeDestructiveImpl (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6119:11)
    at renderNodeDestructive (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6091:14)
    at renderContextProvider (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:5935:3)
    at renderElement (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6032:11)
    at renderNodeDestructiveImpl (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6119:11)
    at renderNodeDestructive (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6091:14)
    at renderContextProvider (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:5935:3)
    at renderElement (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6032:11)
    at renderNodeDestructiveImpl (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6119:11)
    at renderNodeDestructive (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6091:14)
    at renderIndeterminateComponent (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:5790:7)
    at renderElement (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:5961:7)
    at renderNodeDestructiveImpl (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6119:11)
    at renderNodeDestructive (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6091:14)
    at renderContextProvider (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:5935:3)
    at renderElement (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6032:11)
    at renderNodeDestructiveImpl (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6119:11)
    at renderNodeDestructive (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6091:14)
    at renderIndeterminateComponent (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:5790:7)
    at renderElement (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:5961:7)
    at renderNodeDestructiveImpl (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6119:11)
    at renderNodeDestructive (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6091:14)
    at renderNode (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6274:12)
    at renderHostElement (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:5646:3)
    at renderElement (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:5967:5)
    at renderNodeDestructiveImpl (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6119:11)
    at renderNodeDestructive (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6091:14)
    at retryTask (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6543:5)
    at performWork (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6591:7)
    at E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6915:12
    at scheduleWork (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:78:3)
    at startWork (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6914:3)
 at E:\\Ticketgol\\Code\\Ticketgol\\vendor\\inertiajs\\inertia-laravel\\src\\Commands\\StartSsr.php:84)
[stacktrace]
#0 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Inertia\\Commands\\StartSsr->handle()
#1 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#2 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#3 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#4 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(694): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#5 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(213): Illuminate\\Container\\Container->call(Array)
#6 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#7 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(182): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#8 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#9 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Inertia\\Commands\\StartSsr), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#10 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(198): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#12 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1205): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#13 E:\\Ticketgol\\Code\\Ticketgol\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#14 {main}
"} 
[2025-07-03 00:14:01] local.ERROR: ReferenceError: route is not defined
    at Navbar (file:///E:/Ticketgol/Code/Ticketgol/bootstrap/ssr/ssr.js:317:25)
    at renderWithHooks (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:5662:16)
    at renderIndeterminateComponent (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:5736:15)
    at renderElement (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:5961:7)
    at renderNodeDestructiveImpl (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6119:11)
    at renderNodeDestructive (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6091:14)
    at renderNode (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6274:12)
    at renderChildrenArray (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6226:7)
    at renderNodeDestructiveImpl (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6156:7)
    at renderNodeDestructive (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6091:14)
    at renderNode (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6274:12)
    at renderHostElement (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:5646:3)
    at renderElement (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:5967:5)
    at renderNodeDestructiveImpl (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6119:11)
    at renderNodeDestructive (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6091:14)
    at renderIndeterminateComponent (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:5790:7)
    at renderElement (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:5961:7)
    at renderNodeDestructiveImpl (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6119:11)
    at renderNodeDestructive (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6091:14)
    at renderContextProvider (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:5935:3)
    at renderElement (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6032:11)
    at renderNodeDestructiveImpl (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6119:11)
    at renderNodeDestructive (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6091:14)
    at renderContextProvider (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:5935:3)
    at renderElement (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6032:11)
    at renderNodeDestructiveImpl (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6119:11)
    at renderNodeDestructive (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6091:14)
    at renderIndeterminateComponent (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:5790:7)
    at renderElement (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:5961:7)
    at renderNodeDestructiveImpl (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6119:11)
    at renderNodeDestructive (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6091:14)
    at renderContextProvider (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:5935:3)
    at renderElement (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6032:11)
    at renderNodeDestructiveImpl (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6119:11)
    at renderNodeDestructive (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6091:14)
    at renderIndeterminateComponent (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:5790:7)
    at renderElement (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:5961:7)
    at renderNodeDestructiveImpl (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6119:11)
    at renderNodeDestructive (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6091:14)
    at renderNode (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6274:12)
    at renderHostElement (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:5646:3)
    at renderElement (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:5967:5)
    at renderNodeDestructiveImpl (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6119:11)
    at renderNodeDestructive (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6091:14)
    at retryTask (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6543:5)
    at performWork (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6591:7)
    at E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6915:12
    at scheduleWork (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:78:3)
    at startWork (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6914:3)
    at renderToStringImpl (E:\Ticketgol\Code\Ticketgol\node_modules\react-dom\cjs\react-dom-server-legacy.node.development.js:6988:3)
 {"exception":"[object] (Inertia\\Ssr\\SsrException(code: 0): ReferenceError: route is not defined
    at Navbar (file:///E:/Ticketgol/Code/Ticketgol/bootstrap/ssr/ssr.js:317:25)
    at renderWithHooks (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:5662:16)
    at renderIndeterminateComponent (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:5736:15)
    at renderElement (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:5961:7)
    at renderNodeDestructiveImpl (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6119:11)
    at renderNodeDestructive (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6091:14)
    at renderNode (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6274:12)
    at renderChildrenArray (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6226:7)
    at renderNodeDestructiveImpl (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6156:7)
    at renderNodeDestructive (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6091:14)
    at renderNode (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6274:12)
    at renderHostElement (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:5646:3)
    at renderElement (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:5967:5)
    at renderNodeDestructiveImpl (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6119:11)
    at renderNodeDestructive (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6091:14)
    at renderIndeterminateComponent (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:5790:7)
    at renderElement (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:5961:7)
    at renderNodeDestructiveImpl (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6119:11)
    at renderNodeDestructive (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6091:14)
    at renderContextProvider (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:5935:3)
    at renderElement (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6032:11)
    at renderNodeDestructiveImpl (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6119:11)
    at renderNodeDestructive (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6091:14)
    at renderContextProvider (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:5935:3)
    at renderElement (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6032:11)
    at renderNodeDestructiveImpl (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6119:11)
    at renderNodeDestructive (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6091:14)
    at renderIndeterminateComponent (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:5790:7)
    at renderElement (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:5961:7)
    at renderNodeDestructiveImpl (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6119:11)
    at renderNodeDestructive (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6091:14)
    at renderContextProvider (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:5935:3)
    at renderElement (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6032:11)
    at renderNodeDestructiveImpl (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6119:11)
    at renderNodeDestructive (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6091:14)
    at renderIndeterminateComponent (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:5790:7)
    at renderElement (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:5961:7)
    at renderNodeDestructiveImpl (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6119:11)
    at renderNodeDestructive (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6091:14)
    at renderNode (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6274:12)
    at renderHostElement (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:5646:3)
    at renderElement (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:5967:5)
    at renderNodeDestructiveImpl (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6119:11)
    at renderNodeDestructive (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6091:14)
    at retryTask (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6543:5)
    at performWork (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6591:7)
    at E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6915:12
    at scheduleWork (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:78:3)
    at startWork (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6914:3)
    at renderToStringImpl (E:\\Ticketgol\\Code\\Ticketgol\\node_modules\\react-dom\\cjs\\react-dom-server-legacy.node.development.js:6988:3)
 at E:\\Ticketgol\\Code\\Ticketgol\\vendor\\inertiajs\\inertia-laravel\\src\\Commands\\StartSsr.php:84)
[stacktrace]
#0 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Inertia\\Commands\\StartSsr->handle()
#1 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#2 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#3 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#4 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(694): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#5 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(213): Illuminate\\Container\\Container->call(Array)
#6 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#7 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(182): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#8 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#9 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Inertia\\Commands\\StartSsr), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#10 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(198): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#12 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1205): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#13 E:\\Ticketgol\\Code\\Ticketgol\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#14 {main}
"} 
[2025-07-03 00:14:30] local.ERROR: SQLSTATE[23000]: Integrity constraint violation: 1048 Column 'order_id' cannot be null (Connection: mysql, SQL: insert into `payment_logs` (`order_id`, `stripe_event_id`, `event_type`, `event_data_id`, `status`, `webhook_data`, `updated_at`, `created_at`) values (?, evt_1RganIRhkfMMoe7tqOWdJv01, balance.available, ?, ?, {"id":"evt_1RganIRhkfMMoe7tqOWdJv01","object":"event","api_version":"2025-03-31.basil","created":1751501668,"data":{"object":{"object":"balance","available":[{"amount":26186926,"currency":"usd","source_types":{"card":26186926}},{"amount":29267526,"currency":"eur","source_types":{"card":29267526}}],"livemode":false,"pending":[{"amount":0,"currency":"usd","source_types":{"card":0}},{"amount":377214,"currency":"eur","source_types":{"card":377214}}],"refund_and_dispute_prefunding":{"available":[{"amount":0,"currency":"usd"},{"amount":0,"currency":"eur"}],"pending":[{"amount":0,"currency":"usd"},{"amount":0,"currency":"eur"}]}}},"livemode":false,"pending_webhooks":2,"request":{"id":null,"idempotency_key":null},"type":"balance.available"}, 2025-07-03 00:14:30, 2025-07-03 00:14:30)) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 23000): SQLSTATE[23000]: Integrity constraint violation: 1048 Column 'order_id' cannot be null (Connection: mysql, SQL: insert into `payment_logs` (`order_id`, `stripe_event_id`, `event_type`, `event_data_id`, `status`, `webhook_data`, `updated_at`, `created_at`) values (?, evt_1RganIRhkfMMoe7tqOWdJv01, balance.available, ?, ?, {\"id\":\"evt_1RganIRhkfMMoe7tqOWdJv01\",\"object\":\"event\",\"api_version\":\"2025-03-31.basil\",\"created\":1751501668,\"data\":{\"object\":{\"object\":\"balance\",\"available\":[{\"amount\":26186926,\"currency\":\"usd\",\"source_types\":{\"card\":26186926}},{\"amount\":29267526,\"currency\":\"eur\",\"source_types\":{\"card\":29267526}}],\"livemode\":false,\"pending\":[{\"amount\":0,\"currency\":\"usd\",\"source_types\":{\"card\":0}},{\"amount\":377214,\"currency\":\"eur\",\"source_types\":{\"card\":377214}}],\"refund_and_dispute_prefunding\":{\"available\":[{\"amount\":0,\"currency\":\"usd\"},{\"amount\":0,\"currency\":\"eur\"}],\"pending\":[{\"amount\":0,\"currency\":\"usd\"},{\"amount\":0,\"currency\":\"eur\"}]}}},\"livemode\":false,\"pending_webhooks\":2,\"request\":{\"id\":null,\"idempotency_key\":null},\"type\":\"balance.available\"}, 2025-07-03 00:14:30, 2025-07-03 00:14:30)) at E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:825)
[stacktrace]
#0 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback('insert into `pa...', Array, Object(Closure))
#1 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php(42): Illuminate\\Database\\Connection->run('insert into `pa...', Array, Object(Closure))
#2 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\MySqlProcessor.php(35): Illuminate\\Database\\MySqlConnection->insert('insert into `pa...', Array, 'id')
#3 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3799): Illuminate\\Database\\Query\\Processors\\MySqlProcessor->processInsertGetId(Object(Illuminate\\Database\\Query\\Builder), 'insert into `pa...', Array, 'id')
#4 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(2051): Illuminate\\Database\\Query\\Builder->insertGetId(Array, 'id')
#5 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1359): Illuminate\\Database\\Eloquent\\Builder->__call('insertGetId', Array)
#6 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1324): Illuminate\\Database\\Eloquent\\Model->insertAndSetId(Object(Illuminate\\Database\\Eloquent\\Builder), Array)
#7 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1163): Illuminate\\Database\\Eloquent\\Model->performInsert(Object(Illuminate\\Database\\Eloquent\\Builder))
#8 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1081): Illuminate\\Database\\Eloquent\\Model->save()
#9 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Database\\Eloquent\\Builder->{closure:Illuminate\\Database\\Eloquent\\Builder::create():1080}(Object(App\\Models\\PaymentLog))
#10 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1080): tap(Object(App\\Models\\PaymentLog), Object(Closure))
#11 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->create(Array)
#12 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2368): Illuminate\\Database\\Eloquent\\Model->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'create', Array)
#13 E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\PaymentLogRepository.php(18): Illuminate\\Database\\Eloquent\\Model->__call('create', Array)
#14 E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\StripeWebhookService.php(139): App\\Repositories\\PaymentLogRepository->createPaymentLog(Object(Stripe\\Event))
#15 E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\StripeWebHookController.php(33): App\\Services\\StripeWebhookService->addPaymentLog(Object(Stripe\\Event))
#16 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(47): App\\Http\\Controllers\\Api\\V1\\StripeWebHookController->handleCheckoutWebhook(Object(Illuminate\\Http\\Request))
#17 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php(21): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Api\\V1\\StripeWebHookController), 'handleCheckoutW...')
#18 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingRoutingDispatcher.php(35): Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing->{closure:Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing::dispatch():20}()
#19 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php(20): Sentry\\Laravel\\Tracing\\Routing\\TracingRoutingDispatcher->wrapRouteDispatch(Object(Closure), Object(Illuminate\\Routing\\Route))
#20 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(266): Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Api\\V1\\StripeWebHookController), 'handleCheckoutW...')
#21 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(212): Illuminate\\Routing\\Route->runController()
#22 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#23 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():807}(Object(Illuminate\\Http\\Request))
#24 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():142}(Object(Illuminate\\Http\\Request))
#25 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\sanctum\\src\\Http\\Middleware\\EnsureFrontendRequestsAreStateful.php(25): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#27 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Laravel\\Sanctum\\Http\\Middleware\\EnsureFrontendRequestsAreStateful->{closure:Laravel\\Sanctum\\Http\\Middleware\\EnsureFrontendRequestsAreStateful::handle():24}(Object(Illuminate\\Http\\Request))
#28 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():142}(Object(Illuminate\\Http\\Request))
#29 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\sanctum\\src\\Http\\Middleware\\EnsureFrontendRequestsAreStateful.php(24): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#30 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Laravel\\Sanctum\\Http\\Middleware\\EnsureFrontendRequestsAreStateful->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#32 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#33 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#34 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#35 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#36 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#37 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():198}(Object(Illuminate\\Http\\Request))
#38 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Http\\FlushEventsMiddleware.php(13): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():142}(Object(Illuminate\\Http\\Request))
#39 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Sentry\\Laravel\\Http\\FlushEventsMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Http\\SetRequestIpMiddleware.php(45): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#41 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Sentry\\Laravel\\Http\\SetRequestIpMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Http\\SetRequestMiddleware.php(31): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#43 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Sentry\\Laravel\\Http\\SetRequestMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#45 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\beyondcode\\laravel-query-detector\\src\\QueryDetectorMiddleware.php(33): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#47 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): BeyondCode\\QueryDetector\\QueryDetectorMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#49 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#51 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#54 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#57 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#59 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#61 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#63 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#64 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#65 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#66 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Middleware.php(79): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#67 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Sentry\\Laravel\\Tracing\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#68 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#69 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#70 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#71 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1190): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#72 E:\\Ticketgol\\Code\\Ticketgol\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#73 C:\\Program Files\\Herd\\resources\\app.asar.unpacked\\resources\\valet\\server.php(139): require('E:\\\\Ticketgol\\\\Co...')
#74 {main}

[previous exception] [object] (PDOException(code: 23000): SQLSTATE[23000]: Integrity constraint violation: 1048 Column 'order_id' cannot be null at E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php:53)
[stacktrace]
#0 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php(53): PDOStatement->execute()
#1 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(812): Illuminate\\Database\\MySqlConnection->{closure:Illuminate\\Database\\MySqlConnection::insert():42}('insert into `pa...', Array)
#2 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback('insert into `pa...', Array, Object(Closure))
#3 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php(42): Illuminate\\Database\\Connection->run('insert into `pa...', Array, Object(Closure))
#4 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\MySqlProcessor.php(35): Illuminate\\Database\\MySqlConnection->insert('insert into `pa...', Array, 'id')
#5 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3799): Illuminate\\Database\\Query\\Processors\\MySqlProcessor->processInsertGetId(Object(Illuminate\\Database\\Query\\Builder), 'insert into `pa...', Array, 'id')
#6 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(2051): Illuminate\\Database\\Query\\Builder->insertGetId(Array, 'id')
#7 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1359): Illuminate\\Database\\Eloquent\\Builder->__call('insertGetId', Array)
#8 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1324): Illuminate\\Database\\Eloquent\\Model->insertAndSetId(Object(Illuminate\\Database\\Eloquent\\Builder), Array)
#9 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1163): Illuminate\\Database\\Eloquent\\Model->performInsert(Object(Illuminate\\Database\\Eloquent\\Builder))
#10 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1081): Illuminate\\Database\\Eloquent\\Model->save()
#11 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Database\\Eloquent\\Builder->{closure:Illuminate\\Database\\Eloquent\\Builder::create():1080}(Object(App\\Models\\PaymentLog))
#12 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1080): tap(Object(App\\Models\\PaymentLog), Object(Closure))
#13 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->create(Array)
#14 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2368): Illuminate\\Database\\Eloquent\\Model->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'create', Array)
#15 E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\PaymentLogRepository.php(18): Illuminate\\Database\\Eloquent\\Model->__call('create', Array)
#16 E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\StripeWebhookService.php(139): App\\Repositories\\PaymentLogRepository->createPaymentLog(Object(Stripe\\Event))
#17 E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\StripeWebHookController.php(33): App\\Services\\StripeWebhookService->addPaymentLog(Object(Stripe\\Event))
#18 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(47): App\\Http\\Controllers\\Api\\V1\\StripeWebHookController->handleCheckoutWebhook(Object(Illuminate\\Http\\Request))
#19 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php(21): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Api\\V1\\StripeWebHookController), 'handleCheckoutW...')
#20 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingRoutingDispatcher.php(35): Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing->{closure:Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing::dispatch():20}()
#21 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php(20): Sentry\\Laravel\\Tracing\\Routing\\TracingRoutingDispatcher->wrapRouteDispatch(Object(Closure), Object(Illuminate\\Routing\\Route))
#22 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(266): Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Api\\V1\\StripeWebHookController), 'handleCheckoutW...')
#23 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(212): Illuminate\\Routing\\Route->runController()
#24 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#25 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():807}(Object(Illuminate\\Http\\Request))
#26 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():142}(Object(Illuminate\\Http\\Request))
#27 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\sanctum\\src\\Http\\Middleware\\EnsureFrontendRequestsAreStateful.php(25): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#29 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Laravel\\Sanctum\\Http\\Middleware\\EnsureFrontendRequestsAreStateful->{closure:Laravel\\Sanctum\\Http\\Middleware\\EnsureFrontendRequestsAreStateful::handle():24}(Object(Illuminate\\Http\\Request))
#30 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():142}(Object(Illuminate\\Http\\Request))
#31 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\sanctum\\src\\Http\\Middleware\\EnsureFrontendRequestsAreStateful.php(24): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#32 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Laravel\\Sanctum\\Http\\Middleware\\EnsureFrontendRequestsAreStateful->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#34 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#35 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#36 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#37 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#38 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#39 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():198}(Object(Illuminate\\Http\\Request))
#40 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Http\\FlushEventsMiddleware.php(13): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():142}(Object(Illuminate\\Http\\Request))
#41 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Sentry\\Laravel\\Http\\FlushEventsMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Http\\SetRequestIpMiddleware.php(45): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#43 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Sentry\\Laravel\\Http\\SetRequestIpMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Http\\SetRequestMiddleware.php(31): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#45 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Sentry\\Laravel\\Http\\SetRequestMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#47 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\beyondcode\\laravel-query-detector\\src\\QueryDetectorMiddleware.php(33): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#49 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): BeyondCode\\QueryDetector\\QueryDetectorMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#51 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#53 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#56 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#59 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#61 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#63 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#64 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#65 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#66 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#67 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#68 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Middleware.php(79): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#69 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Sentry\\Laravel\\Tracing\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#70 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#71 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#72 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#73 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1190): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#74 E:\\Ticketgol\\Code\\Ticketgol\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#75 C:\\Program Files\\Herd\\resources\\app.asar.unpacked\\resources\\valet\\server.php(139): require('E:\\\\Ticketgol\\\\Co...')
#76 {main}
"} 
[2025-07-03 00:21:12] local.ERROR: file:///E:/Ticketgol/Code/Ticketgol/bootstrap/ssr/ssr.js:12
import { Link, usePage, useForm, Head, router, createInertiaApp, route as route$1 } from "@inertiajs/react";
                                                                 ^^^^^
SyntaxError: The requested module '@inertiajs/react' does not provide an export named 'route'
    at ModuleJob._instantiate (node:internal/modules/esm/module_job:180:21)
    at async ModuleJob.run (node:internal/modules/esm/module_job:263:5)
    at async onImport.tracePromise.__proto__ (node:internal/modules/esm/loader:547:26)
    at async asyncRunEntryPointWithESMLoader (node:internal/modules/run_main:116:5)

Node.js v22.12.0
 {"exception":"[object] (Inertia\\Ssr\\SsrException(code: 0): file:///E:/Ticketgol/Code/Ticketgol/bootstrap/ssr/ssr.js:12

import { Link, usePage, useForm, Head, router, createInertiaApp, route as route$1 } from \"@inertiajs/react\";

                                                                 ^^^^^

SyntaxError: The requested module '@inertiajs/react' does not provide an export named 'route'

    at ModuleJob._instantiate (node:internal/modules/esm/module_job:180:21)

    at async ModuleJob.run (node:internal/modules/esm/module_job:263:5)

    at async onImport.tracePromise.__proto__ (node:internal/modules/esm/loader:547:26)

    at async asyncRunEntryPointWithESMLoader (node:internal/modules/run_main:116:5)



Node.js v22.12.0

 at E:\\Ticketgol\\Code\\Ticketgol\\vendor\\inertiajs\\inertia-laravel\\src\\Commands\\StartSsr.php:84)
[stacktrace]
#0 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Inertia\\Commands\\StartSsr->handle()
#1 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#2 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#3 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#4 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(694): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#5 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(213): Illuminate\\Container\\Container->call(Array)
#6 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#7 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(182): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#8 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#9 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Inertia\\Commands\\StartSsr), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#10 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(198): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#12 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1205): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#13 E:\\Ticketgol\\Code\\Ticketgol\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#14 {main}
"} 
[2025-07-03 00:26:01] local.ERROR: SQLSTATE[23000]: Integrity constraint violation: 1048 Column 'order_id' cannot be null (Connection: mysql, SQL: insert into `payment_logs` (`order_id`, `stripe_event_id`, `event_type`, `event_data_id`, `status`, `webhook_data`, `updated_at`, `created_at`) values (?, evt_1RgayRRhkfMMoe7tBC2oiuAU, balance.available, ?, ?, {"id":"evt_1RgayRRhkfMMoe7tBC2oiuAU","object":"event","api_version":"2025-03-31.basil","created":1751502359,"data":{"object":{"object":"balance","available":[{"amount":26186926,"currency":"usd","source_types":{"card":26186926}},{"amount":29267526,"currency":"eur","source_types":{"card":29267526}}],"livemode":false,"pending":[{"amount":0,"currency":"usd","source_types":{"card":0}},{"amount":377214,"currency":"eur","source_types":{"card":377214}}],"refund_and_dispute_prefunding":{"available":[{"amount":0,"currency":"usd"},{"amount":0,"currency":"eur"}],"pending":[{"amount":0,"currency":"usd"},{"amount":0,"currency":"eur"}]}}},"livemode":false,"pending_webhooks":2,"request":{"id":null,"idempotency_key":null},"type":"balance.available"}, 2025-07-03 00:26:01, 2025-07-03 00:26:01)) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 23000): SQLSTATE[23000]: Integrity constraint violation: 1048 Column 'order_id' cannot be null (Connection: mysql, SQL: insert into `payment_logs` (`order_id`, `stripe_event_id`, `event_type`, `event_data_id`, `status`, `webhook_data`, `updated_at`, `created_at`) values (?, evt_1RgayRRhkfMMoe7tBC2oiuAU, balance.available, ?, ?, {\"id\":\"evt_1RgayRRhkfMMoe7tBC2oiuAU\",\"object\":\"event\",\"api_version\":\"2025-03-31.basil\",\"created\":1751502359,\"data\":{\"object\":{\"object\":\"balance\",\"available\":[{\"amount\":26186926,\"currency\":\"usd\",\"source_types\":{\"card\":26186926}},{\"amount\":29267526,\"currency\":\"eur\",\"source_types\":{\"card\":29267526}}],\"livemode\":false,\"pending\":[{\"amount\":0,\"currency\":\"usd\",\"source_types\":{\"card\":0}},{\"amount\":377214,\"currency\":\"eur\",\"source_types\":{\"card\":377214}}],\"refund_and_dispute_prefunding\":{\"available\":[{\"amount\":0,\"currency\":\"usd\"},{\"amount\":0,\"currency\":\"eur\"}],\"pending\":[{\"amount\":0,\"currency\":\"usd\"},{\"amount\":0,\"currency\":\"eur\"}]}}},\"livemode\":false,\"pending_webhooks\":2,\"request\":{\"id\":null,\"idempotency_key\":null},\"type\":\"balance.available\"}, 2025-07-03 00:26:01, 2025-07-03 00:26:01)) at E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:825)
[stacktrace]
#0 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback('insert into `pa...', Array, Object(Closure))
#1 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php(42): Illuminate\\Database\\Connection->run('insert into `pa...', Array, Object(Closure))
#2 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\MySqlProcessor.php(35): Illuminate\\Database\\MySqlConnection->insert('insert into `pa...', Array, 'id')
#3 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3799): Illuminate\\Database\\Query\\Processors\\MySqlProcessor->processInsertGetId(Object(Illuminate\\Database\\Query\\Builder), 'insert into `pa...', Array, 'id')
#4 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(2051): Illuminate\\Database\\Query\\Builder->insertGetId(Array, 'id')
#5 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1359): Illuminate\\Database\\Eloquent\\Builder->__call('insertGetId', Array)
#6 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1324): Illuminate\\Database\\Eloquent\\Model->insertAndSetId(Object(Illuminate\\Database\\Eloquent\\Builder), Array)
#7 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1163): Illuminate\\Database\\Eloquent\\Model->performInsert(Object(Illuminate\\Database\\Eloquent\\Builder))
#8 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1081): Illuminate\\Database\\Eloquent\\Model->save()
#9 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Database\\Eloquent\\Builder->{closure:Illuminate\\Database\\Eloquent\\Builder::create():1080}(Object(App\\Models\\PaymentLog))
#10 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1080): tap(Object(App\\Models\\PaymentLog), Object(Closure))
#11 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->create(Array)
#12 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2368): Illuminate\\Database\\Eloquent\\Model->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'create', Array)
#13 E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\PaymentLogRepository.php(18): Illuminate\\Database\\Eloquent\\Model->__call('create', Array)
#14 E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\StripeWebhookService.php(139): App\\Repositories\\PaymentLogRepository->createPaymentLog(Object(Stripe\\Event))
#15 E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\StripeWebHookController.php(33): App\\Services\\StripeWebhookService->addPaymentLog(Object(Stripe\\Event))
#16 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(47): App\\Http\\Controllers\\Api\\V1\\StripeWebHookController->handleCheckoutWebhook(Object(Illuminate\\Http\\Request))
#17 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php(21): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Api\\V1\\StripeWebHookController), 'handleCheckoutW...')
#18 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingRoutingDispatcher.php(35): Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing->{closure:Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing::dispatch():20}()
#19 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php(20): Sentry\\Laravel\\Tracing\\Routing\\TracingRoutingDispatcher->wrapRouteDispatch(Object(Closure), Object(Illuminate\\Routing\\Route))
#20 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(266): Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Api\\V1\\StripeWebHookController), 'handleCheckoutW...')
#21 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(212): Illuminate\\Routing\\Route->runController()
#22 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#23 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():807}(Object(Illuminate\\Http\\Request))
#24 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():142}(Object(Illuminate\\Http\\Request))
#25 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\sanctum\\src\\Http\\Middleware\\EnsureFrontendRequestsAreStateful.php(25): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#27 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Laravel\\Sanctum\\Http\\Middleware\\EnsureFrontendRequestsAreStateful->{closure:Laravel\\Sanctum\\Http\\Middleware\\EnsureFrontendRequestsAreStateful::handle():24}(Object(Illuminate\\Http\\Request))
#28 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():142}(Object(Illuminate\\Http\\Request))
#29 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\sanctum\\src\\Http\\Middleware\\EnsureFrontendRequestsAreStateful.php(24): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#30 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Laravel\\Sanctum\\Http\\Middleware\\EnsureFrontendRequestsAreStateful->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#32 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#33 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#34 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#35 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#36 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#37 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():198}(Object(Illuminate\\Http\\Request))
#38 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Http\\FlushEventsMiddleware.php(13): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():142}(Object(Illuminate\\Http\\Request))
#39 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Sentry\\Laravel\\Http\\FlushEventsMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Http\\SetRequestIpMiddleware.php(45): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#41 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Sentry\\Laravel\\Http\\SetRequestIpMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Http\\SetRequestMiddleware.php(31): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#43 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Sentry\\Laravel\\Http\\SetRequestMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#45 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\beyondcode\\laravel-query-detector\\src\\QueryDetectorMiddleware.php(33): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#47 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): BeyondCode\\QueryDetector\\QueryDetectorMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#49 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#51 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#54 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#57 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#59 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#61 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#63 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#64 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#65 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#66 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Middleware.php(79): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#67 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Sentry\\Laravel\\Tracing\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#68 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#69 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#70 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#71 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1190): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#72 E:\\Ticketgol\\Code\\Ticketgol\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#73 C:\\Program Files\\Herd\\resources\\app.asar.unpacked\\resources\\valet\\server.php(139): require('E:\\\\Ticketgol\\\\Co...')
#74 {main}

[previous exception] [object] (PDOException(code: 23000): SQLSTATE[23000]: Integrity constraint violation: 1048 Column 'order_id' cannot be null at E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php:53)
[stacktrace]
#0 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php(53): PDOStatement->execute()
#1 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(812): Illuminate\\Database\\MySqlConnection->{closure:Illuminate\\Database\\MySqlConnection::insert():42}('insert into `pa...', Array)
#2 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback('insert into `pa...', Array, Object(Closure))
#3 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php(42): Illuminate\\Database\\Connection->run('insert into `pa...', Array, Object(Closure))
#4 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\MySqlProcessor.php(35): Illuminate\\Database\\MySqlConnection->insert('insert into `pa...', Array, 'id')
#5 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3799): Illuminate\\Database\\Query\\Processors\\MySqlProcessor->processInsertGetId(Object(Illuminate\\Database\\Query\\Builder), 'insert into `pa...', Array, 'id')
#6 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(2051): Illuminate\\Database\\Query\\Builder->insertGetId(Array, 'id')
#7 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1359): Illuminate\\Database\\Eloquent\\Builder->__call('insertGetId', Array)
#8 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1324): Illuminate\\Database\\Eloquent\\Model->insertAndSetId(Object(Illuminate\\Database\\Eloquent\\Builder), Array)
#9 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1163): Illuminate\\Database\\Eloquent\\Model->performInsert(Object(Illuminate\\Database\\Eloquent\\Builder))
#10 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1081): Illuminate\\Database\\Eloquent\\Model->save()
#11 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Database\\Eloquent\\Builder->{closure:Illuminate\\Database\\Eloquent\\Builder::create():1080}(Object(App\\Models\\PaymentLog))
#12 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1080): tap(Object(App\\Models\\PaymentLog), Object(Closure))
#13 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->create(Array)
#14 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2368): Illuminate\\Database\\Eloquent\\Model->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'create', Array)
#15 E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\PaymentLogRepository.php(18): Illuminate\\Database\\Eloquent\\Model->__call('create', Array)
#16 E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\StripeWebhookService.php(139): App\\Repositories\\PaymentLogRepository->createPaymentLog(Object(Stripe\\Event))
#17 E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\StripeWebHookController.php(33): App\\Services\\StripeWebhookService->addPaymentLog(Object(Stripe\\Event))
#18 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(47): App\\Http\\Controllers\\Api\\V1\\StripeWebHookController->handleCheckoutWebhook(Object(Illuminate\\Http\\Request))
#19 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php(21): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Api\\V1\\StripeWebHookController), 'handleCheckoutW...')
#20 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingRoutingDispatcher.php(35): Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing->{closure:Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing::dispatch():20}()
#21 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php(20): Sentry\\Laravel\\Tracing\\Routing\\TracingRoutingDispatcher->wrapRouteDispatch(Object(Closure), Object(Illuminate\\Routing\\Route))
#22 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(266): Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Api\\V1\\StripeWebHookController), 'handleCheckoutW...')
#23 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(212): Illuminate\\Routing\\Route->runController()
#24 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#25 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():807}(Object(Illuminate\\Http\\Request))
#26 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():142}(Object(Illuminate\\Http\\Request))
#27 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\sanctum\\src\\Http\\Middleware\\EnsureFrontendRequestsAreStateful.php(25): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#29 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Laravel\\Sanctum\\Http\\Middleware\\EnsureFrontendRequestsAreStateful->{closure:Laravel\\Sanctum\\Http\\Middleware\\EnsureFrontendRequestsAreStateful::handle():24}(Object(Illuminate\\Http\\Request))
#30 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():142}(Object(Illuminate\\Http\\Request))
#31 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\sanctum\\src\\Http\\Middleware\\EnsureFrontendRequestsAreStateful.php(24): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#32 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Laravel\\Sanctum\\Http\\Middleware\\EnsureFrontendRequestsAreStateful->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#34 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#35 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#36 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#37 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#38 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#39 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():198}(Object(Illuminate\\Http\\Request))
#40 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Http\\FlushEventsMiddleware.php(13): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():142}(Object(Illuminate\\Http\\Request))
#41 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Sentry\\Laravel\\Http\\FlushEventsMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Http\\SetRequestIpMiddleware.php(45): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#43 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Sentry\\Laravel\\Http\\SetRequestIpMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Http\\SetRequestMiddleware.php(31): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#45 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Sentry\\Laravel\\Http\\SetRequestMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#47 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\beyondcode\\laravel-query-detector\\src\\QueryDetectorMiddleware.php(33): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#49 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): BeyondCode\\QueryDetector\\QueryDetectorMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#51 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#53 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#56 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#59 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#61 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#63 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#64 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#65 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#66 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#67 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#68 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Middleware.php(79): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#69 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Sentry\\Laravel\\Tracing\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#70 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#71 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#72 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#73 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1190): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#74 E:\\Ticketgol\\Code\\Ticketgol\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#75 C:\\Program Files\\Herd\\resources\\app.asar.unpacked\\resources\\valet\\server.php(139): require('E:\\\\Ticketgol\\\\Co...')
#76 {main}
"} 
[2025-07-03 00:34:42] local.ERROR: file:///E:/Ticketgol/Code/Ticketgol/bootstrap/ssr/ssr.js:12
import { Link, usePage, useForm, Head, router, createInertiaApp, route as route$1 } from "@inertiajs/react";
                                                                 ^^^^^
SyntaxError: The requested module '@inertiajs/react' does not provide an export named 'route'
    at ModuleJob._instantiate (node:internal/modules/esm/module_job:180:21)
    at async ModuleJob.run (node:internal/modules/esm/module_job:263:5)
    at async onImport.tracePromise.__proto__ (node:internal/modules/esm/loader:547:26)
    at async asyncRunEntryPointWithESMLoader (node:internal/modules/run_main:116:5)

Node.js v22.12.0
 {"exception":"[object] (Inertia\\Ssr\\SsrException(code: 0): file:///E:/Ticketgol/Code/Ticketgol/bootstrap/ssr/ssr.js:12

import { Link, usePage, useForm, Head, router, createInertiaApp, route as route$1 } from \"@inertiajs/react\";

                                                                 ^^^^^

SyntaxError: The requested module '@inertiajs/react' does not provide an export named 'route'

    at ModuleJob._instantiate (node:internal/modules/esm/module_job:180:21)

    at async ModuleJob.run (node:internal/modules/esm/module_job:263:5)

    at async onImport.tracePromise.__proto__ (node:internal/modules/esm/loader:547:26)

    at async asyncRunEntryPointWithESMLoader (node:internal/modules/run_main:116:5)



Node.js v22.12.0

 at E:\\Ticketgol\\Code\\Ticketgol\\vendor\\inertiajs\\inertia-laravel\\src\\Commands\\StartSsr.php:84)
[stacktrace]
#0 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Inertia\\Commands\\StartSsr->handle()
#1 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#2 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#3 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#4 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(694): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#5 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(213): Illuminate\\Container\\Container->call(Array)
#6 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#7 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(182): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#8 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#9 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Inertia\\Commands\\StartSsr), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#10 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(198): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#12 E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1205): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#13 E:\\Ticketgol\\Code\\Ticketgol\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#14 {main}
"} 
