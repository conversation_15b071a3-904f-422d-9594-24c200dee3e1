<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class OrderResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'order_no' => $this->order_no,
            'total_price' => $this->total_price,
            'quantity' => $this->quantity,
            'status' => [
                'value' => $this->status->value,
                'label' => $this->status->getLabel(),
                'color' => $this->status->getBadgeColour(),
            ],
            'purchase_date' => $this->purchase_date,
            'created_at' => $this->created_at,
            'ticket' => $this->order_meta_data && isset($this->order_meta_data->ticket) ? (function () {
                $ticket = $this->order_meta_data->ticket;
                $event = $this->order_meta_data->event ?? null;
                $currentLocale = app()->getLocale();

                return [
                    'ticket_no' => $ticket->ticket_no,
                    'type' => $ticket->ticket_type->label ?? null,
                    'event' => $event ? [
                        'name' => $event->name->{$currentLocale} ?? '',
                        'date' => $event->date,
                        'time' => $event->time,
                        'stadium' => [
                            'id' => $event->stadium->id ?? null,
                            'name' => $event->stadium->name->{$currentLocale} ?? '',
                            'image' => $event->image ?? '',
                            'image_alt' => $event->image_alt ?? '',
                        ],
                        'home_club' => $event->home_club->name->{$currentLocale} ?? '',
                        'guest_club' => $event->guest_club->name->{$currentLocale} ?? '',
                    ] : null,
                ];
            })() : [],
        ];
    }
}
