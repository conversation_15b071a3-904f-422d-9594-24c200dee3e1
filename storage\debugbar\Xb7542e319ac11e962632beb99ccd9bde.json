{"__meta": {"id": "Xb7542e319ac11e962632beb99ccd9bde", "datetime": "2025-07-02 10:23:46", "utime": **********.353265, "method": "GET", "uri": "/api/v1/translations", "ip": "127.0.0.1"}, "php": {"version": "8.4.6", "interface": "cgi-fcgi"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.161026, "end": **********.353283, "duration": 0.19225692749023438, "duration_str": "192ms", "measures": [{"label": "Booting", "start": **********.161026, "relative_start": 0, "end": **********.313333, "relative_end": **********.313333, "duration": 0.15230703353881836, "duration_str": "152ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.313351, "relative_start": 0.15232491493225098, "end": **********.353286, "relative_end": 3.0994415283203125e-06, "duration": 0.03993511199951172, "duration_str": "39.94ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 6069752, "peak_usage_str": "6MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET api/v1/translations", "middleware": "api, set-locale", "controller": "App\\Http\\Controllers\\Api\\V1\\TranslationsController@index", "namespace": null, "prefix": "api/v1/translations", "where": [], "as": "api.translations.index", "file": "<a href=\"phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FHttp%2FControllers%2FApi%2FV1%2FTranslationsController.php&line=16\" onclick=\"\">app/Http/Controllers/Api/V1/TranslationsController.php:16-54</a>"}, "queries": {"nb_statements": 2, "nb_visible_statements": 2, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.00795, "accumulated_duration_str": "7.95ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `sessions` where `id` = 'WlvzHOjVFhjJ1Ur8Bv8M5DD9AeIwLCdCXefXyrg6' limit 1", "type": "query", "params": [], "bindings": ["WlvzHOjVFhjJ1Ur8Bv8M5DD9AeIwLCdCXefXyrg6"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 147}], "start": **********.326579, "duration": 0.0022, "duration_str": "2.2ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:97", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=97", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "97"}, "connection": "ticketgol", "explain": null, "start_percent": 0, "width_percent": 27.673}, {"sql": "update `sessions` set `payload` = 'YTo0OntzOjY6Il90b2tlbiI7czo0MDoiRTBuVkhWNFZnbmV2QXMyNFhNNWs5M2d4M0tXMVlLODZNOWpzZVRtOCI7czozOiJ1cmwiO2E6MTp7czo4OiJpbnRlbmRlZCI7czo0MjoiaHR0cDovL3RpY2tldGdvbC50ZXN0L215LWFjY291bnQvb3JkZXJzLzI1Ijt9czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6Mjc6Imh0dHA6Ly90aWNrZXRnb2wudGVzdC9sb2dpbiI7fXM6NjoiX2ZsYXNoIjthOjI6e3M6Mzoib2xkIjthOjA6e31zOjM6Im5ldyI7YTowOnt9fX0=', `last_activity` = **********, `user_id` = null, `ip_address` = '127.0.0.1', `user_agent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' where `id` = 'WlvzHOjVFhjJ1Ur8Bv8M5DD9AeIwLCdCXefXyrg6'", "type": "query", "params": [], "bindings": ["YTo0OntzOjY6Il90b2tlbiI7czo0MDoiRTBuVkhWNFZnbmV2QXMyNFhNNWs5M2d4M0tXMVlLODZNOWpzZVRtOCI7czozOiJ1cmwiO2E6MTp7czo4OiJpbnRlbmRlZCI7czo0MjoiaHR0cDovL3RpY2tldGdvbC50ZXN0L215LWFjY291bnQvb3JkZXJzLzI1Ijt9czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6Mjc6Imh0dHA6Ly90aWNrZXRnb2wudGVzdC9sb2dpbiI7fXM6NjoiX2ZsYXNoIjthOjI6e3M6Mzoib2xkIjthOjA6e31zOjM6Im5ldyI7YTowOnt9fX0=", **********, null, "127.0.0.1", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "WlvzHOjVFhjJ1Ur8Bv8M5DD9AeIwLCdCXefXyrg6"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 173}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 140}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 175}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 245}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 130}], "start": **********.345354, "duration": 0.00575, "duration_str": "5.75ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:173", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 173}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=173", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "173"}, "connection": "ticketgol", "explain": null, "start_percent": 27.673, "width_percent": 72.327}]}, "models": {"data": [], "count": 0, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "E0nVHV4VgnevAs24XM5k93gx3KW1YK86M9jseTm8", "url": "array:1 [\n  \"intended\" => \"http://ticketgol.test/my-account/orders/25\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://ticketgol.test/login\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]"}, "request": {"telescope": "<a href=\"http://ticketgol.test/_debugbar/telescope/9f4b2a65-2634-43a6-8580-9923a68b385b\" target=\"_blank\">View in Telescope</a>", "path_info": "/api/v1/translations", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:11</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"853 characters\">ajs_anonymous_id=%2205cf8d6b-8a14-40b9-9d4f-c4ebd1620a6e%22; selected_locale=en; __stripe_mid=fd60eaa0-e90c-4914-bb27-ce58ed87fc6066be49; XSRF-TOKEN=eyJpdiI6ImFPTGpjbkpKTklkSEZRUEY5bnI5blE9PSIsInZhbHVlIjoieVJ0TFNzVHIxWHNQV2M2eU1PR2x4WFpyVkltNXI5SUUwaCs0UytnYW56bEFHbE1Wc1FwbU01WkV6ZVhqVkhNcFJwV1FOS3FMajhiQkRtbmRoV0lTeG82WHpYR2NSSXpnZ1hQNmZCaVhVbVZDZE9TTjFDZ2EvNW10MGFsVDFPYWoiLCJtYWMiOiIwYzI0MGQ2ZDdiNjgxOGM5NWIxNTI1YjdlY2E1NjM2NTM4MGJlN2JjYWU3ZTI4MjU4ODRkNzAwOGQwNjFlOTAwIiwidGFnIjoiIn0%3D; ticketgol_session=eyJpdiI6IllYZnpaRjZ4S0JFUkFkWVE5K296ZFE9PSIsInZhbHVlIjoiQlBFMFUyaDQzMksveDF6RUtjd01WeXQ4U0szd0owVUJ5QURXbVNMU0RodmJCc0tMTS83TVRaVEVvb0Z5WGVNMEFBdFdNYVVCTUFWY3VHSWY0ZnV3bHA2RkN1eTA0YzYxTTNlNjJLNVBmZ2lJakZrL056c3NHVTg3NW1hWEhra08iLCJtYWMiOiJlM2UxMzFmYjc5ZGFmNTk3MzMzMTdlNzJjMWFhOTJiOGM4NDY1MDMzM2E0NDBmZWUyMWI3NzljYjk2ZGVkYjZhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">en-GB,en;q=0.9,es;q=0.8,en-US;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://ticketgol.test/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-locale</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6ImFPTGpjbkpKTklkSEZRUEY5bnI5blE9PSIsInZhbHVlIjoieVJ0TFNzVHIxWHNQV2M2eU1PR2x4WFpyVkltNXI5SUUwaCs0UytnYW56bEFHbE1Wc1FwbU01WkV6ZVhqVkhNcFJwV1FOS3FMajhiQkRtbmRoV0lTeG82WHpYR2NSSXpnZ1hQNmZCaVhVbVZDZE9TTjFDZ2EvNW10MGFsVDFPYWoiLCJtYWMiOiIwYzI0MGQ2ZDdiNjgxOGM5NWIxNTI1YjdlY2E1NjM2NTM4MGJlN2JjYWU3ZTI4MjU4ODRkNzAwOGQwNjFlOTAwIiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">ticketgol.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1383940962 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>ajs_anonymous_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>selected_locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">E0nVHV4VgnevAs24XM5k93gx3KW1YK86M9jseTm8</span>\"\n  \"<span class=sf-dump-key>ticketgol_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WlvzHOjVFhjJ1Ur8Bv8M5DD9AeIwLCdCXefXyrg6</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1383940962\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1194616956 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 02 Jul 2025 10:23:46 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IlBYWndwVmU4RDBsN3lJODNOL2J1b1E9PSIsInZhbHVlIjoiNEp2cWNhQW9Ua00waFhKVmNkUXBVNm0yWXl1WmcyYkgxZTE2QzloQ0ppWkFVTVcvcGlQYkJQMHpuZ00wNHVLcVpSbHpRQWJXY0hiYzB4UytIcjBUYmRHUXF4dmFGN1ZOUlNWRWVldENIa1NnaThNaENYT2phL0JBUHVyeWg3YUciLCJtYWMiOiI5NGRmZDYwYmU2MDE4NGYzN2U0NTI1ZTgyZGUzYmUwZmE0MTIwMmE2Y2Y3MGFiMmRiYTkyY2RmZmIxYjUwNzcxIiwidGFnIjoiIn0%3D; expires=Wed, 02 Jul 2025 12:23:46 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"445 characters\">ticketgol_session=eyJpdiI6IjNMaG5NRzg5MmFVSVkxQVNjZVpCT1E9PSIsInZhbHVlIjoiY3F3TzZ0SGdheExyZ0dMVENDQkFSRmtMNU1IQVpWYkU4QnBhZDBJejVLRHFNWlYxV3JXWDhEcExTMHBoWlk1V1p4dUdnNEdMWGNsMGRFaVNZejlHMXZnTWNEay9XMkkwb2tjcmgzMzlFQnBmMFR5cTk5RjNHQm1SZTRNaUxIV1QiLCJtYWMiOiI3ZGIzNzM1ZDQyNmJiN2I5M2EwMmVkZGI3ODlmMWZkYzA1ZGNhNDg1YTM2YmMyZmE5YjkzNGZkYjUwZjhjYjk0IiwidGFnIjoiIn0%3D; expires=Wed, 02 Jul 2025 12:23:46 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IlBYWndwVmU4RDBsN3lJODNOL2J1b1E9PSIsInZhbHVlIjoiNEp2cWNhQW9Ua00waFhKVmNkUXBVNm0yWXl1WmcyYkgxZTE2QzloQ0ppWkFVTVcvcGlQYkJQMHpuZ00wNHVLcVpSbHpRQWJXY0hiYzB4UytIcjBUYmRHUXF4dmFGN1ZOUlNWRWVldENIa1NnaThNaENYT2phL0JBUHVyeWg3YUciLCJtYWMiOiI5NGRmZDYwYmU2MDE4NGYzN2U0NTI1ZTgyZGUzYmUwZmE0MTIwMmE2Y2Y3MGFiMmRiYTkyY2RmZmIxYjUwNzcxIiwidGFnIjoiIn0%3D; expires=Wed, 02-Jul-2025 12:23:46 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"417 characters\">ticketgol_session=eyJpdiI6IjNMaG5NRzg5MmFVSVkxQVNjZVpCT1E9PSIsInZhbHVlIjoiY3F3TzZ0SGdheExyZ0dMVENDQkFSRmtMNU1IQVpWYkU4QnBhZDBJejVLRHFNWlYxV3JXWDhEcExTMHBoWlk1V1p4dUdnNEdMWGNsMGRFaVNZejlHMXZnTWNEay9XMkkwb2tjcmgzMzlFQnBmMFR5cTk5RjNHQm1SZTRNaUxIV1QiLCJtYWMiOiI3ZGIzNzM1ZDQyNmJiN2I5M2EwMmVkZGI3ODlmMWZkYzA1ZGNhNDg1YTM2YmMyZmE5YjkzNGZkYjUwZjhjYjk0IiwidGFnIjoiIn0%3D; expires=Wed, 02-Jul-2025 12:23:46 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1194616956\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">E0nVHV4VgnevAs24XM5k93gx3KW1YK86M9jseTm8</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"42 characters\">http://ticketgol.test/my-account/orders/25</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://ticketgol.test/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}