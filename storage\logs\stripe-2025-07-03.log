[2025-07-03 00:00:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 00:00:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 00:00:01] local.INFO: Cron job executed {"time":"2025-07-03 00:00:01"} 
[2025-07-03 00:00:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 00:00:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 00:00:02] local.INFO: Cron job executed {"time":"2025-07-03 00:00:02"} 
[2025-07-03 00:01:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 00:01:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 00:01:01] local.INFO: Cron job executed {"time":"2025-07-03 00:01:01"} 
[2025-07-03 00:01:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 00:01:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 00:01:02] local.INFO: Cron job executed {"time":"2025-07-03 00:01:02"} 
[2025-07-03 00:02:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 00:02:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 00:02:01] local.INFO: Cron job executed {"time":"2025-07-03 00:02:01"} 
[2025-07-03 00:02:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 00:02:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 00:02:03] local.INFO: Cron job executed {"time":"2025-07-03 00:02:03"} 
[2025-07-03 00:03:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 00:03:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 00:03:01] local.INFO: Cron job executed {"time":"2025-07-03 00:03:01"} 
[2025-07-03 00:03:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 00:03:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 00:03:03] local.INFO: Cron job executed {"time":"2025-07-03 00:03:03"} 
[2025-07-03 00:04:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 00:04:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 00:04:01] local.INFO: Cron job executed {"time":"2025-07-03 00:04:01"} 
[2025-07-03 00:04:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 00:04:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 00:04:02] local.INFO: Cron job executed {"time":"2025-07-03 00:04:02"} 
[2025-07-03 00:05:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 00:05:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 00:05:01] local.INFO: Cron job executed {"time":"2025-07-03 00:05:01"} 
[2025-07-03 00:05:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 00:05:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 00:05:02] local.INFO: Cron job executed {"time":"2025-07-03 00:05:02"} 
[2025-07-03 00:06:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 00:06:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 00:06:01] local.INFO: Cron job executed {"time":"2025-07-03 00:06:01"} 
[2025-07-03 00:06:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 00:06:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 00:06:02] local.INFO: Cron job executed {"time":"2025-07-03 00:06:02"} 
[2025-07-03 00:07:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 00:07:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 00:07:01] local.INFO: Cron job executed {"time":"2025-07-03 00:07:01"} 
[2025-07-03 00:07:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 00:07:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 00:07:02] local.INFO: Cron job executed {"time":"2025-07-03 00:07:02"} 
[2025-07-03 00:08:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 00:08:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 00:08:01] local.INFO: Cron job executed {"time":"2025-07-03 00:08:01"} 
[2025-07-03 00:08:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 00:08:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 00:08:02] local.INFO: Cron job executed {"time":"2025-07-03 00:08:02"} 
[2025-07-03 00:09:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 00:09:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 00:09:01] local.INFO: Cron job executed {"time":"2025-07-03 00:09:01"} 
[2025-07-03 00:09:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 00:09:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 00:09:02] local.INFO: Cron job executed {"time":"2025-07-03 00:09:02"} 
[2025-07-03 00:10:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 00:10:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 00:10:01] local.INFO: Cron job executed {"time":"2025-07-03 00:10:01"} 
[2025-07-03 00:10:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 00:10:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 00:10:02] local.INFO: Cron job executed {"time":"2025-07-03 00:10:02"} 
[2025-07-03 00:11:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 00:11:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 00:11:01] local.INFO: Cron job executed {"time":"2025-07-03 00:11:01"} 
[2025-07-03 00:11:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 00:11:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 00:11:02] local.INFO: Cron job executed {"time":"2025-07-03 00:11:02"} 
[2025-07-03 00:12:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 00:12:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 00:12:01] local.INFO: Cron job executed {"time":"2025-07-03 00:12:01"} 
[2025-07-03 00:12:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 00:12:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 00:12:02] local.INFO: Cron job executed {"time":"2025-07-03 00:12:02"} 
[2025-07-03 00:13:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 00:13:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 00:13:01] local.INFO: Cron job executed {"time":"2025-07-03 00:13:01"} 
[2025-07-03 00:13:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 00:13:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 00:13:02] local.INFO: Cron job executed {"time":"2025-07-03 00:13:02"} 
[2025-07-03 00:14:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 00:14:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 00:14:01] local.INFO: Cron job executed {"time":"2025-07-03 00:14:01"} 
[2025-07-03 00:14:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 00:14:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 00:14:03] local.INFO: Cron job executed {"time":"2025-07-03 00:14:03"} 
[2025-07-03 00:15:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 00:15:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 00:15:01] local.INFO: Cron job executed {"time":"2025-07-03 00:15:01"} 
[2025-07-03 00:15:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 00:15:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 00:15:02] local.INFO: Cron job executed {"time":"2025-07-03 00:15:02"} 
[2025-07-03 00:16:04] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 00:16:04] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 00:16:04] local.INFO: Cron job executed {"time":"2025-07-03 00:16:04"} 
[2025-07-03 00:16:06] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 00:16:06] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 00:16:06] local.INFO: Cron job executed {"time":"2025-07-03 00:16:06"} 
[2025-07-03 00:17:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 00:17:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 00:17:01] local.INFO: Cron job executed {"time":"2025-07-03 00:17:01"} 
[2025-07-03 00:17:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 00:17:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 00:17:02] local.INFO: Cron job executed {"time":"2025-07-03 00:17:02"} 
[2025-07-03 00:18:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 00:18:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 00:18:01] local.INFO: Cron job executed {"time":"2025-07-03 00:18:01"} 
[2025-07-03 00:18:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 00:18:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 00:18:02] local.INFO: Cron job executed {"time":"2025-07-03 00:18:02"} 
[2025-07-03 00:19:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 00:19:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 00:19:01] local.INFO: Cron job executed {"time":"2025-07-03 00:19:01"} 
[2025-07-03 00:19:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 00:19:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 00:19:02] local.INFO: Cron job executed {"time":"2025-07-03 00:19:02"} 
[2025-07-03 00:20:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 00:20:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 00:20:01] local.INFO: Cron job executed {"time":"2025-07-03 00:20:01"} 
[2025-07-03 00:20:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 00:20:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 00:20:02] local.INFO: Cron job executed {"time":"2025-07-03 00:20:02"} 
[2025-07-03 00:21:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 00:21:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 00:21:01] local.INFO: Cron job executed {"time":"2025-07-03 00:21:01"} 
[2025-07-03 00:21:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 00:21:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 00:21:02] local.INFO: Cron job executed {"time":"2025-07-03 00:21:02"} 
[2025-07-03 00:22:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 00:22:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 00:22:01] local.INFO: Cron job executed {"time":"2025-07-03 00:22:01"} 
[2025-07-03 00:22:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 00:22:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 00:22:02] local.INFO: Cron job executed {"time":"2025-07-03 00:22:02"} 
[2025-07-03 00:23:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 00:23:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 00:23:01] local.INFO: Cron job executed {"time":"2025-07-03 00:23:01"} 
[2025-07-03 00:23:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 00:23:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 00:23:02] local.INFO: Cron job executed {"time":"2025-07-03 00:23:02"} 
[2025-07-03 00:24:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 00:24:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 00:24:01] local.INFO: Cron job executed {"time":"2025-07-03 00:24:01"} 
[2025-07-03 00:24:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 00:24:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 00:24:03] local.INFO: Cron job executed {"time":"2025-07-03 00:24:03"} 
[2025-07-03 00:25:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 00:25:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 00:25:01] local.INFO: Cron job executed {"time":"2025-07-03 00:25:01"} 
[2025-07-03 00:25:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 00:25:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 00:25:02] local.INFO: Cron job executed {"time":"2025-07-03 00:25:02"} 
[2025-07-03 00:26:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 00:26:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 00:26:01] local.INFO: Cron job executed {"time":"2025-07-03 00:26:01"} 
[2025-07-03 00:26:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 00:26:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 00:26:02] local.INFO: Cron job executed {"time":"2025-07-03 00:26:02"} 
[2025-07-03 00:27:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 00:27:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 00:27:01] local.INFO: Cron job executed {"time":"2025-07-03 00:27:01"} 
[2025-07-03 00:27:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 00:27:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 00:27:02] local.INFO: Cron job executed {"time":"2025-07-03 00:27:02"} 
[2025-07-03 00:28:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 00:28:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 00:28:01] local.INFO: Cron job executed {"time":"2025-07-03 00:28:01"} 
[2025-07-03 00:28:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 00:28:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 00:28:02] local.INFO: Cron job executed {"time":"2025-07-03 00:28:02"} 
[2025-07-03 00:29:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 00:29:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 00:29:01] local.INFO: Cron job executed {"time":"2025-07-03 00:29:01"} 
[2025-07-03 00:29:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 00:29:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 00:29:02] local.INFO: Cron job executed {"time":"2025-07-03 00:29:02"} 
[2025-07-03 00:30:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 00:30:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 00:30:01] local.INFO: Cron job executed {"time":"2025-07-03 00:30:01"} 
[2025-07-03 00:30:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 00:30:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 00:30:02] local.INFO: Cron job executed {"time":"2025-07-03 00:30:02"} 
[2025-07-03 00:31:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 00:31:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 00:31:01] local.INFO: Cron job executed {"time":"2025-07-03 00:31:01"} 
[2025-07-03 00:31:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 00:31:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 00:31:02] local.INFO: Cron job executed {"time":"2025-07-03 00:31:02"} 
[2025-07-03 00:32:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 00:32:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 00:32:01] local.INFO: Cron job executed {"time":"2025-07-03 00:32:01"} 
[2025-07-03 00:32:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 00:32:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 00:32:02] local.INFO: Cron job executed {"time":"2025-07-03 00:32:02"} 
[2025-07-03 00:33:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 00:33:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 00:33:01] local.INFO: Cron job executed {"time":"2025-07-03 00:33:01"} 
[2025-07-03 00:33:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 00:33:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 00:33:02] local.INFO: Cron job executed {"time":"2025-07-03 00:33:02"} 
[2025-07-03 00:34:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 00:34:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 00:34:01] local.INFO: Cron job executed {"time":"2025-07-03 00:34:01"} 
[2025-07-03 00:34:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 00:34:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 00:34:02] local.INFO: Cron job executed {"time":"2025-07-03 00:34:02"} 
[2025-07-03 00:35:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 00:35:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 00:35:01] local.INFO: Cron job executed {"time":"2025-07-03 00:35:01"} 
[2025-07-03 00:35:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 00:35:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 00:35:02] local.INFO: Cron job executed {"time":"2025-07-03 00:35:02"} 
[2025-07-03 00:36:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 00:36:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 00:36:01] local.INFO: Cron job executed {"time":"2025-07-03 00:36:01"} 
[2025-07-03 00:36:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 00:36:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 00:36:02] local.INFO: Cron job executed {"time":"2025-07-03 00:36:02"} 
[2025-07-03 00:37:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 00:37:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 00:37:01] local.INFO: Cron job executed {"time":"2025-07-03 00:37:01"} 
[2025-07-03 00:37:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 00:37:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 00:37:02] local.INFO: Cron job executed {"time":"2025-07-03 00:37:02"} 
[2025-07-03 00:38:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 00:38:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 00:38:01] local.INFO: Cron job executed {"time":"2025-07-03 00:38:01"} 
[2025-07-03 00:38:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 00:38:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 00:38:03] local.INFO: Cron job executed {"time":"2025-07-03 00:38:03"} 
[2025-07-03 00:39:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 00:39:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 00:39:01] local.INFO: Cron job executed {"time":"2025-07-03 00:39:01"} 
[2025-07-03 00:39:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 00:39:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 00:39:02] local.INFO: Cron job executed {"time":"2025-07-03 00:39:02"} 
[2025-07-03 00:40:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 00:40:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 00:40:01] local.INFO: Cron job executed {"time":"2025-07-03 00:40:01"} 
[2025-07-03 00:40:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 00:40:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 00:40:02] local.INFO: Cron job executed {"time":"2025-07-03 00:40:02"} 
[2025-07-03 00:41:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 00:41:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 00:41:01] local.INFO: Cron job executed {"time":"2025-07-03 00:41:01"} 
[2025-07-03 00:41:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 00:41:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 00:41:02] local.INFO: Cron job executed {"time":"2025-07-03 00:41:02"} 
[2025-07-03 00:42:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 00:42:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 00:42:01] local.INFO: Cron job executed {"time":"2025-07-03 00:42:01"} 
[2025-07-03 00:42:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 00:42:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 00:42:03] local.INFO: Cron job executed {"time":"2025-07-03 00:42:03"} 
[2025-07-03 00:43:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 00:43:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 00:43:01] local.INFO: Cron job executed {"time":"2025-07-03 00:43:01"} 
[2025-07-03 00:43:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 00:43:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 00:43:03] local.INFO: Cron job executed {"time":"2025-07-03 00:43:03"} 
[2025-07-03 00:44:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 00:44:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 00:44:01] local.INFO: Cron job executed {"time":"2025-07-03 00:44:01"} 
[2025-07-03 00:44:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 00:44:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 00:44:03] local.INFO: Cron job executed {"time":"2025-07-03 00:44:03"} 
[2025-07-03 00:45:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 00:45:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 00:45:01] local.INFO: Cron job executed {"time":"2025-07-03 00:45:01"} 
[2025-07-03 00:45:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 00:45:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 00:45:02] local.INFO: Cron job executed {"time":"2025-07-03 00:45:02"} 
[2025-07-03 00:46:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 00:46:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 00:46:01] local.INFO: Cron job executed {"time":"2025-07-03 00:46:01"} 
[2025-07-03 00:46:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 00:46:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 00:46:02] local.INFO: Cron job executed {"time":"2025-07-03 00:46:02"} 
[2025-07-03 00:47:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 00:47:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 00:47:01] local.INFO: Cron job executed {"time":"2025-07-03 00:47:01"} 
[2025-07-03 00:47:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 00:47:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 00:47:03] local.INFO: Cron job executed {"time":"2025-07-03 00:47:03"} 
[2025-07-03 00:48:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 00:48:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 00:48:01] local.INFO: Cron job executed {"time":"2025-07-03 00:48:01"} 
[2025-07-03 00:48:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 00:48:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 00:48:02] local.INFO: Cron job executed {"time":"2025-07-03 00:48:02"} 
[2025-07-03 00:49:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 00:49:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 00:49:01] local.INFO: Cron job executed {"time":"2025-07-03 00:49:01"} 
[2025-07-03 00:49:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 00:49:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 00:49:02] local.INFO: Cron job executed {"time":"2025-07-03 00:49:02"} 
[2025-07-03 00:50:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 00:50:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 00:50:01] local.INFO: Cron job executed {"time":"2025-07-03 00:50:01"} 
[2025-07-03 00:50:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 00:50:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 00:50:02] local.INFO: Cron job executed {"time":"2025-07-03 00:50:02"} 
[2025-07-03 00:51:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 00:51:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 00:51:01] local.INFO: Cron job executed {"time":"2025-07-03 00:51:01"} 
[2025-07-03 00:51:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 00:51:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 00:51:02] local.INFO: Cron job executed {"time":"2025-07-03 00:51:02"} 
[2025-07-03 00:52:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 00:52:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 00:52:01] local.INFO: Cron job executed {"time":"2025-07-03 00:52:01"} 
[2025-07-03 00:52:04] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 00:52:04] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 00:52:04] local.INFO: Cron job executed {"time":"2025-07-03 00:52:04"} 
[2025-07-03 00:53:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 00:53:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 00:53:01] local.INFO: Cron job executed {"time":"2025-07-03 00:53:01"} 
[2025-07-03 00:53:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 00:53:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 00:53:02] local.INFO: Cron job executed {"time":"2025-07-03 00:53:02"} 
[2025-07-03 00:54:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 00:54:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 00:54:01] local.INFO: Cron job executed {"time":"2025-07-03 00:54:01"} 
[2025-07-03 00:54:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 00:54:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 00:54:03] local.INFO: Cron job executed {"time":"2025-07-03 00:54:03"} 
[2025-07-03 00:55:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 00:55:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 00:55:01] local.INFO: Cron job executed {"time":"2025-07-03 00:55:01"} 
[2025-07-03 00:55:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 00:55:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 00:55:02] local.INFO: Cron job executed {"time":"2025-07-03 00:55:02"} 
[2025-07-03 00:56:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 00:56:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 00:56:01] local.INFO: Cron job executed {"time":"2025-07-03 00:56:01"} 
[2025-07-03 00:56:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 00:56:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 00:56:02] local.INFO: Cron job executed {"time":"2025-07-03 00:56:02"} 
[2025-07-03 00:57:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 00:57:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 00:57:01] local.INFO: Cron job executed {"time":"2025-07-03 00:57:01"} 
[2025-07-03 00:57:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 00:57:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 00:57:02] local.INFO: Cron job executed {"time":"2025-07-03 00:57:02"} 
[2025-07-03 00:58:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 00:58:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 00:58:01] local.INFO: Cron job executed {"time":"2025-07-03 00:58:01"} 
[2025-07-03 00:58:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 00:58:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 00:58:03] local.INFO: Cron job executed {"time":"2025-07-03 00:58:03"} 
[2025-07-03 00:59:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 00:59:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 00:59:01] local.INFO: Cron job executed {"time":"2025-07-03 00:59:01"} 
[2025-07-03 00:59:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 00:59:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 00:59:03] local.INFO: Cron job executed {"time":"2025-07-03 00:59:03"} 
[2025-07-03 01:00:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 01:00:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 01:00:01] local.INFO: Cron job executed {"time":"2025-07-03 01:00:01"} 
[2025-07-03 01:00:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 01:00:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 01:00:02] local.INFO: Cron job executed {"time":"2025-07-03 01:00:02"} 
[2025-07-03 01:01:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 01:01:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 01:01:01] local.INFO: Cron job executed {"time":"2025-07-03 01:01:01"} 
[2025-07-03 01:01:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 01:01:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 01:01:02] local.INFO: Cron job executed {"time":"2025-07-03 01:01:02"} 
[2025-07-03 01:02:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 01:02:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 01:02:01] local.INFO: Cron job executed {"time":"2025-07-03 01:02:01"} 
[2025-07-03 01:02:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 01:02:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 01:02:02] local.INFO: Cron job executed {"time":"2025-07-03 01:02:02"} 
[2025-07-03 01:03:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 01:03:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 01:03:01] local.INFO: Cron job executed {"time":"2025-07-03 01:03:01"} 
[2025-07-03 01:03:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 01:03:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 01:03:02] local.INFO: Cron job executed {"time":"2025-07-03 01:03:02"} 
[2025-07-03 01:04:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 01:04:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 01:04:01] local.INFO: Cron job executed {"time":"2025-07-03 01:04:01"} 
[2025-07-03 01:04:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 01:04:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 01:04:02] local.INFO: Cron job executed {"time":"2025-07-03 01:04:02"} 
[2025-07-03 01:05:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 01:05:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 01:05:01] local.INFO: Cron job executed {"time":"2025-07-03 01:05:01"} 
[2025-07-03 01:05:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 01:05:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 01:05:02] local.INFO: Cron job executed {"time":"2025-07-03 01:05:02"} 
[2025-07-03 01:06:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 01:06:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 01:06:01] local.INFO: Cron job executed {"time":"2025-07-03 01:06:01"} 
[2025-07-03 01:06:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 01:06:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 01:06:02] local.INFO: Cron job executed {"time":"2025-07-03 01:06:02"} 
[2025-07-03 01:07:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 01:07:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 01:07:01] local.INFO: Cron job executed {"time":"2025-07-03 01:07:01"} 
[2025-07-03 01:07:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 01:07:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 01:07:02] local.INFO: Cron job executed {"time":"2025-07-03 01:07:02"} 
[2025-07-03 01:08:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 01:08:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 01:08:01] local.INFO: Cron job executed {"time":"2025-07-03 01:08:01"} 
[2025-07-03 01:08:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 01:08:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 01:08:02] local.INFO: Cron job executed {"time":"2025-07-03 01:08:02"} 
[2025-07-03 01:09:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 01:09:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 01:09:01] local.INFO: Cron job executed {"time":"2025-07-03 01:09:01"} 
[2025-07-03 01:09:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 01:09:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 01:09:02] local.INFO: Cron job executed {"time":"2025-07-03 01:09:02"} 
[2025-07-03 01:10:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 01:10:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 01:10:01] local.INFO: Cron job executed {"time":"2025-07-03 01:10:01"} 
[2025-07-03 01:10:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 01:10:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 01:10:02] local.INFO: Cron job executed {"time":"2025-07-03 01:10:02"} 
[2025-07-03 01:11:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 01:11:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 01:11:01] local.INFO: Cron job executed {"time":"2025-07-03 01:11:01"} 
[2025-07-03 01:11:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 01:11:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 01:11:02] local.INFO: Cron job executed {"time":"2025-07-03 01:11:02"} 
[2025-07-03 01:12:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 01:12:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 01:12:01] local.INFO: Cron job executed {"time":"2025-07-03 01:12:01"} 
[2025-07-03 01:12:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 01:12:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 01:12:02] local.INFO: Cron job executed {"time":"2025-07-03 01:12:02"} 
[2025-07-03 01:13:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 01:13:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 01:13:01] local.INFO: Cron job executed {"time":"2025-07-03 01:13:01"} 
[2025-07-03 01:13:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 01:13:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 01:13:02] local.INFO: Cron job executed {"time":"2025-07-03 01:13:02"} 
[2025-07-03 01:14:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 01:14:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 01:14:01] local.INFO: Cron job executed {"time":"2025-07-03 01:14:01"} 
[2025-07-03 01:14:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 01:14:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 01:14:03] local.INFO: Cron job executed {"time":"2025-07-03 01:14:03"} 
[2025-07-03 01:15:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 01:15:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 01:15:01] local.INFO: Cron job executed {"time":"2025-07-03 01:15:01"} 
[2025-07-03 01:15:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 01:15:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 01:15:02] local.INFO: Cron job executed {"time":"2025-07-03 01:15:02"} 
[2025-07-03 01:16:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 01:16:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 01:16:01] local.INFO: Cron job executed {"time":"2025-07-03 01:16:01"} 
[2025-07-03 01:16:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 01:16:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 01:16:03] local.INFO: Cron job executed {"time":"2025-07-03 01:16:03"} 
[2025-07-03 01:17:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 01:17:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 01:17:01] local.INFO: Cron job executed {"time":"2025-07-03 01:17:01"} 
[2025-07-03 01:17:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 01:17:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 01:17:02] local.INFO: Cron job executed {"time":"2025-07-03 01:17:02"} 
[2025-07-03 01:18:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 01:18:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 01:18:01] local.INFO: Cron job executed {"time":"2025-07-03 01:18:01"} 
[2025-07-03 01:18:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 01:18:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 01:18:02] local.INFO: Cron job executed {"time":"2025-07-03 01:18:02"} 
[2025-07-03 01:19:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 01:19:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 01:19:01] local.INFO: Cron job executed {"time":"2025-07-03 01:19:01"} 
[2025-07-03 01:19:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 01:19:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 01:19:02] local.INFO: Cron job executed {"time":"2025-07-03 01:19:02"} 
[2025-07-03 01:20:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 01:20:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 01:20:01] local.INFO: Cron job executed {"time":"2025-07-03 01:20:01"} 
[2025-07-03 01:20:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 01:20:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 01:20:02] local.INFO: Cron job executed {"time":"2025-07-03 01:20:02"} 
[2025-07-03 01:21:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 01:21:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 01:21:01] local.INFO: Cron job executed {"time":"2025-07-03 01:21:01"} 
[2025-07-03 01:21:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 01:21:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 01:21:02] local.INFO: Cron job executed {"time":"2025-07-03 01:21:02"} 
[2025-07-03 01:22:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 01:22:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 01:22:01] local.INFO: Cron job executed {"time":"2025-07-03 01:22:01"} 
[2025-07-03 01:22:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 01:22:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 01:22:03] local.INFO: Cron job executed {"time":"2025-07-03 01:22:03"} 
[2025-07-03 01:23:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 01:23:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 01:23:01] local.INFO: Cron job executed {"time":"2025-07-03 01:23:01"} 
[2025-07-03 01:23:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 01:23:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 01:23:02] local.INFO: Cron job executed {"time":"2025-07-03 01:23:02"} 
[2025-07-03 01:24:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 01:24:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 01:24:01] local.INFO: Cron job executed {"time":"2025-07-03 01:24:01"} 
[2025-07-03 01:24:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 01:24:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 01:24:02] local.INFO: Cron job executed {"time":"2025-07-03 01:24:02"} 
[2025-07-03 01:25:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 01:25:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 01:25:01] local.INFO: Cron job executed {"time":"2025-07-03 01:25:01"} 
[2025-07-03 01:25:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 01:25:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 01:25:02] local.INFO: Cron job executed {"time":"2025-07-03 01:25:02"} 
[2025-07-03 01:26:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 01:26:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 01:26:01] local.INFO: Cron job executed {"time":"2025-07-03 01:26:01"} 
[2025-07-03 01:26:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 01:26:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 01:26:02] local.INFO: Cron job executed {"time":"2025-07-03 01:26:02"} 
[2025-07-03 01:27:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 01:27:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 01:27:01] local.INFO: Cron job executed {"time":"2025-07-03 01:27:01"} 
[2025-07-03 01:27:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 01:27:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 01:27:02] local.INFO: Cron job executed {"time":"2025-07-03 01:27:02"} 
[2025-07-03 01:28:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 01:28:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 01:28:01] local.INFO: Cron job executed {"time":"2025-07-03 01:28:01"} 
[2025-07-03 01:28:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 01:28:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 01:28:02] local.INFO: Cron job executed {"time":"2025-07-03 01:28:02"} 
[2025-07-03 01:29:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 01:29:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 01:29:01] local.INFO: Cron job executed {"time":"2025-07-03 01:29:01"} 
[2025-07-03 01:29:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 01:29:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 01:29:02] local.INFO: Cron job executed {"time":"2025-07-03 01:29:02"} 
[2025-07-03 01:30:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 01:30:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 01:30:01] local.INFO: Cron job executed {"time":"2025-07-03 01:30:01"} 
[2025-07-03 01:30:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 01:30:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 01:30:02] local.INFO: Cron job executed {"time":"2025-07-03 01:30:02"} 
[2025-07-03 01:31:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 01:31:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 01:31:01] local.INFO: Cron job executed {"time":"2025-07-03 01:31:01"} 
[2025-07-03 01:31:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 01:31:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 01:31:02] local.INFO: Cron job executed {"time":"2025-07-03 01:31:02"} 
[2025-07-03 01:32:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 01:32:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 01:32:01] local.INFO: Cron job executed {"time":"2025-07-03 01:32:01"} 
[2025-07-03 01:32:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 01:32:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 01:32:02] local.INFO: Cron job executed {"time":"2025-07-03 01:32:02"} 
[2025-07-03 01:33:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 01:33:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 01:33:01] local.INFO: Cron job executed {"time":"2025-07-03 01:33:01"} 
[2025-07-03 01:33:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 01:33:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 01:33:02] local.INFO: Cron job executed {"time":"2025-07-03 01:33:02"} 
[2025-07-03 01:34:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 01:34:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 01:34:01] local.INFO: Cron job executed {"time":"2025-07-03 01:34:01"} 
[2025-07-03 01:34:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 01:34:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 01:34:02] local.INFO: Cron job executed {"time":"2025-07-03 01:34:02"} 
[2025-07-03 01:35:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 01:35:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 01:35:01] local.INFO: Cron job executed {"time":"2025-07-03 01:35:01"} 
[2025-07-03 01:35:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 01:35:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 01:35:03] local.INFO: Cron job executed {"time":"2025-07-03 01:35:03"} 
[2025-07-03 01:36:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 01:36:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 01:36:01] local.INFO: Cron job executed {"time":"2025-07-03 01:36:01"} 
[2025-07-03 01:36:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 01:36:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 01:36:02] local.INFO: Cron job executed {"time":"2025-07-03 01:36:02"} 
[2025-07-03 01:37:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 01:37:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 01:37:01] local.INFO: Cron job executed {"time":"2025-07-03 01:37:01"} 
[2025-07-03 01:37:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 01:37:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 01:37:02] local.INFO: Cron job executed {"time":"2025-07-03 01:37:02"} 
[2025-07-03 01:38:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 01:38:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 01:38:01] local.INFO: Cron job executed {"time":"2025-07-03 01:38:01"} 
[2025-07-03 01:38:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 01:38:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 01:38:02] local.INFO: Cron job executed {"time":"2025-07-03 01:38:02"} 
[2025-07-03 01:39:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 01:39:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 01:39:01] local.INFO: Cron job executed {"time":"2025-07-03 01:39:01"} 
[2025-07-03 01:39:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 01:39:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 01:39:03] local.INFO: Cron job executed {"time":"2025-07-03 01:39:03"} 
[2025-07-03 01:40:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 01:40:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 01:40:01] local.INFO: Cron job executed {"time":"2025-07-03 01:40:01"} 
[2025-07-03 01:40:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 01:40:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 01:40:02] local.INFO: Cron job executed {"time":"2025-07-03 01:40:02"} 
[2025-07-03 01:41:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 01:41:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 01:41:01] local.INFO: Cron job executed {"time":"2025-07-03 01:41:01"} 
[2025-07-03 01:41:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 01:41:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 01:41:03] local.INFO: Cron job executed {"time":"2025-07-03 01:41:03"} 
[2025-07-03 01:42:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 01:42:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 01:42:01] local.INFO: Cron job executed {"time":"2025-07-03 01:42:01"} 
[2025-07-03 01:42:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 01:42:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 01:42:03] local.INFO: Cron job executed {"time":"2025-07-03 01:42:03"} 
[2025-07-03 01:43:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 01:43:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 01:43:01] local.INFO: Cron job executed {"time":"2025-07-03 01:43:01"} 
[2025-07-03 01:43:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 01:43:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 01:43:02] local.INFO: Cron job executed {"time":"2025-07-03 01:43:02"} 
[2025-07-03 01:44:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 01:44:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 01:44:01] local.INFO: Cron job executed {"time":"2025-07-03 01:44:01"} 
[2025-07-03 01:44:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 01:44:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 01:44:02] local.INFO: Cron job executed {"time":"2025-07-03 01:44:02"} 
[2025-07-03 01:45:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 01:45:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 01:45:01] local.INFO: Cron job executed {"time":"2025-07-03 01:45:01"} 
[2025-07-03 01:45:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 01:45:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 01:45:02] local.INFO: Cron job executed {"time":"2025-07-03 01:45:02"} 
[2025-07-03 01:46:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 01:46:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 01:46:01] local.INFO: Cron job executed {"time":"2025-07-03 01:46:01"} 
[2025-07-03 01:46:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 01:46:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 01:46:02] local.INFO: Cron job executed {"time":"2025-07-03 01:46:02"} 
[2025-07-03 01:47:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 01:47:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 01:47:01] local.INFO: Cron job executed {"time":"2025-07-03 01:47:01"} 
[2025-07-03 01:47:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 01:47:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 01:47:03] local.INFO: Cron job executed {"time":"2025-07-03 01:47:03"} 
[2025-07-03 01:48:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 01:48:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 01:48:01] local.INFO: Cron job executed {"time":"2025-07-03 01:48:01"} 
[2025-07-03 01:48:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 01:48:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 01:48:02] local.INFO: Cron job executed {"time":"2025-07-03 01:48:02"} 
[2025-07-03 01:49:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 01:49:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 01:49:01] local.INFO: Cron job executed {"time":"2025-07-03 01:49:01"} 
[2025-07-03 01:49:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 01:49:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 01:49:02] local.INFO: Cron job executed {"time":"2025-07-03 01:49:02"} 
[2025-07-03 01:50:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 01:50:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 01:50:01] local.INFO: Cron job executed {"time":"2025-07-03 01:50:01"} 
[2025-07-03 01:50:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 01:50:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 01:50:02] local.INFO: Cron job executed {"time":"2025-07-03 01:50:02"} 
[2025-07-03 01:51:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 01:51:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 01:51:01] local.INFO: Cron job executed {"time":"2025-07-03 01:51:01"} 
[2025-07-03 01:51:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 01:51:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 01:51:02] local.INFO: Cron job executed {"time":"2025-07-03 01:51:02"} 
[2025-07-03 01:52:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 01:52:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 01:52:01] local.INFO: Cron job executed {"time":"2025-07-03 01:52:01"} 
[2025-07-03 01:52:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 01:52:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 01:52:02] local.INFO: Cron job executed {"time":"2025-07-03 01:52:02"} 
[2025-07-03 01:53:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 01:53:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 01:53:01] local.INFO: Cron job executed {"time":"2025-07-03 01:53:01"} 
[2025-07-03 01:53:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 01:53:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 01:53:02] local.INFO: Cron job executed {"time":"2025-07-03 01:53:02"} 
[2025-07-03 01:54:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 01:54:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 01:54:01] local.INFO: Cron job executed {"time":"2025-07-03 01:54:01"} 
[2025-07-03 01:54:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 01:54:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 01:54:02] local.INFO: Cron job executed {"time":"2025-07-03 01:54:02"} 
[2025-07-03 01:55:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 01:55:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 01:55:01] local.INFO: Cron job executed {"time":"2025-07-03 01:55:01"} 
[2025-07-03 01:55:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 01:55:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 01:55:02] local.INFO: Cron job executed {"time":"2025-07-03 01:55:02"} 
[2025-07-03 01:56:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 01:56:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 01:56:01] local.INFO: Cron job executed {"time":"2025-07-03 01:56:01"} 
[2025-07-03 01:56:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 01:56:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 01:56:02] local.INFO: Cron job executed {"time":"2025-07-03 01:56:02"} 
[2025-07-03 01:57:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 01:57:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 01:57:01] local.INFO: Cron job executed {"time":"2025-07-03 01:57:01"} 
[2025-07-03 01:57:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 01:57:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 01:57:03] local.INFO: Cron job executed {"time":"2025-07-03 01:57:03"} 
[2025-07-03 01:58:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 01:58:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 01:58:01] local.INFO: Cron job executed {"time":"2025-07-03 01:58:01"} 
[2025-07-03 01:58:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 01:58:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 01:58:02] local.INFO: Cron job executed {"time":"2025-07-03 01:58:02"} 
[2025-07-03 01:59:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 01:59:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 01:59:01] local.INFO: Cron job executed {"time":"2025-07-03 01:59:01"} 
[2025-07-03 01:59:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 01:59:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 01:59:02] local.INFO: Cron job executed {"time":"2025-07-03 01:59:02"} 
[2025-07-03 02:00:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 02:00:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 02:00:01] local.INFO: Cron job executed {"time":"2025-07-03 02:00:01"} 
[2025-07-03 02:00:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 02:00:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 02:00:02] local.INFO: Cron job executed {"time":"2025-07-03 02:00:02"} 
[2025-07-03 02:01:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 02:01:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 02:01:01] local.INFO: Cron job executed {"time":"2025-07-03 02:01:01"} 
[2025-07-03 02:01:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 02:01:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 02:01:02] local.INFO: Cron job executed {"time":"2025-07-03 02:01:02"} 
[2025-07-03 02:02:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 02:02:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 02:02:01] local.INFO: Cron job executed {"time":"2025-07-03 02:02:01"} 
[2025-07-03 02:02:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 02:02:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 02:02:03] local.INFO: Cron job executed {"time":"2025-07-03 02:02:03"} 
[2025-07-03 02:03:43] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 02:03:43] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 02:03:43] local.INFO: Cron job executed {"time":"2025-07-03 02:03:43"} 
[2025-07-03 02:03:47] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 02:03:47] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 02:03:47] local.INFO: Cron job executed {"time":"2025-07-03 02:03:47"} 
[2025-07-03 02:04:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 02:04:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 02:04:01] local.INFO: Cron job executed {"time":"2025-07-03 02:04:01"} 
[2025-07-03 02:04:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 02:04:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 02:04:03] local.INFO: Cron job executed {"time":"2025-07-03 02:04:03"} 
[2025-07-03 02:05:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 02:05:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 02:05:01] local.INFO: Cron job executed {"time":"2025-07-03 02:05:01"} 
[2025-07-03 02:05:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 02:05:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 02:05:03] local.INFO: Cron job executed {"time":"2025-07-03 02:05:03"} 
[2025-07-03 02:06:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 02:06:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 02:06:01] local.INFO: Cron job executed {"time":"2025-07-03 02:06:01"} 
[2025-07-03 02:06:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 02:06:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 02:06:03] local.INFO: Cron job executed {"time":"2025-07-03 02:06:03"} 
[2025-07-03 02:07:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 02:07:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 02:07:01] local.INFO: Cron job executed {"time":"2025-07-03 02:07:01"} 
[2025-07-03 02:07:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 02:07:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 02:07:03] local.INFO: Cron job executed {"time":"2025-07-03 02:07:03"} 
[2025-07-03 02:08:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 02:08:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 02:08:01] local.INFO: Cron job executed {"time":"2025-07-03 02:08:01"} 
[2025-07-03 02:08:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 02:08:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 02:08:02] local.INFO: Cron job executed {"time":"2025-07-03 02:08:02"} 
[2025-07-03 02:09:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 02:09:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 02:09:01] local.INFO: Cron job executed {"time":"2025-07-03 02:09:01"} 
[2025-07-03 02:09:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 02:09:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 02:09:02] local.INFO: Cron job executed {"time":"2025-07-03 02:09:02"} 
[2025-07-03 02:10:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 02:10:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 02:10:01] local.INFO: Cron job executed {"time":"2025-07-03 02:10:01"} 
[2025-07-03 02:10:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 02:10:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 02:10:02] local.INFO: Cron job executed {"time":"2025-07-03 02:10:02"} 
[2025-07-03 02:11:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 02:11:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 02:11:01] local.INFO: Cron job executed {"time":"2025-07-03 02:11:01"} 
[2025-07-03 02:11:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 02:11:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 02:11:02] local.INFO: Cron job executed {"time":"2025-07-03 02:11:02"} 
[2025-07-03 02:12:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 02:12:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 02:12:01] local.INFO: Cron job executed {"time":"2025-07-03 02:12:01"} 
[2025-07-03 02:12:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 02:12:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 02:12:02] local.INFO: Cron job executed {"time":"2025-07-03 02:12:02"} 
[2025-07-03 02:13:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 02:13:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 02:13:01] local.INFO: Cron job executed {"time":"2025-07-03 02:13:01"} 
[2025-07-03 02:13:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 02:13:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 02:13:02] local.INFO: Cron job executed {"time":"2025-07-03 02:13:02"} 
[2025-07-03 02:14:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 02:14:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 02:14:01] local.INFO: Cron job executed {"time":"2025-07-03 02:14:01"} 
[2025-07-03 02:14:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 02:14:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 02:14:02] local.INFO: Cron job executed {"time":"2025-07-03 02:14:02"} 
[2025-07-03 02:15:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 02:15:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 02:15:01] local.INFO: Cron job executed {"time":"2025-07-03 02:15:01"} 
[2025-07-03 02:15:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 02:15:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 02:15:02] local.INFO: Cron job executed {"time":"2025-07-03 02:15:02"} 
[2025-07-03 02:16:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 02:16:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 02:16:01] local.INFO: Cron job executed {"time":"2025-07-03 02:16:01"} 
[2025-07-03 02:16:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 02:16:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 02:16:03] local.INFO: Cron job executed {"time":"2025-07-03 02:16:03"} 
[2025-07-03 02:17:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 02:17:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 02:17:01] local.INFO: Cron job executed {"time":"2025-07-03 02:17:01"} 
[2025-07-03 02:17:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 02:17:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 02:17:02] local.INFO: Cron job executed {"time":"2025-07-03 02:17:02"} 
[2025-07-03 02:18:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 02:18:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 02:18:01] local.INFO: Cron job executed {"time":"2025-07-03 02:18:01"} 
[2025-07-03 02:18:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 02:18:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 02:18:03] local.INFO: Cron job executed {"time":"2025-07-03 02:18:03"} 
[2025-07-03 02:19:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 02:19:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 02:19:01] local.INFO: Cron job executed {"time":"2025-07-03 02:19:01"} 
[2025-07-03 02:19:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 02:19:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 02:19:03] local.INFO: Cron job executed {"time":"2025-07-03 02:19:03"} 
[2025-07-03 02:20:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 02:20:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 02:20:01] local.INFO: Cron job executed {"time":"2025-07-03 02:20:01"} 
[2025-07-03 02:20:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 02:20:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 02:20:03] local.INFO: Cron job executed {"time":"2025-07-03 02:20:03"} 
[2025-07-03 02:21:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 02:21:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 02:21:01] local.INFO: Cron job executed {"time":"2025-07-03 02:21:01"} 
[2025-07-03 02:21:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 02:21:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 02:21:02] local.INFO: Cron job executed {"time":"2025-07-03 02:21:02"} 
[2025-07-03 02:22:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 02:22:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 02:22:01] local.INFO: Cron job executed {"time":"2025-07-03 02:22:01"} 
[2025-07-03 02:22:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 02:22:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 02:22:03] local.INFO: Cron job executed {"time":"2025-07-03 02:22:03"} 
[2025-07-03 02:23:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 02:23:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 02:23:01] local.INFO: Cron job executed {"time":"2025-07-03 02:23:01"} 
[2025-07-03 02:23:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 02:23:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 02:23:03] local.INFO: Cron job executed {"time":"2025-07-03 02:23:03"} 
[2025-07-03 02:24:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 02:24:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 02:24:01] local.INFO: Cron job executed {"time":"2025-07-03 02:24:01"} 
[2025-07-03 02:24:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 02:24:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 02:24:04] local.INFO: Cron job executed {"time":"2025-07-03 02:24:04"} 
[2025-07-03 02:25:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 02:25:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 02:25:01] local.INFO: Cron job executed {"time":"2025-07-03 02:25:01"} 
[2025-07-03 02:25:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 02:25:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 02:25:03] local.INFO: Cron job executed {"time":"2025-07-03 02:25:03"} 
[2025-07-03 02:26:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 02:26:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 02:26:01] local.INFO: Cron job executed {"time":"2025-07-03 02:26:01"} 
[2025-07-03 02:26:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 02:26:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 02:26:02] local.INFO: Cron job executed {"time":"2025-07-03 02:26:02"} 
[2025-07-03 02:27:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 02:27:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 02:27:01] local.INFO: Cron job executed {"time":"2025-07-03 02:27:01"} 
[2025-07-03 02:27:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 02:27:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 02:27:03] local.INFO: Cron job executed {"time":"2025-07-03 02:27:03"} 
[2025-07-03 02:28:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 02:28:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 02:28:01] local.INFO: Cron job executed {"time":"2025-07-03 02:28:01"} 
[2025-07-03 02:28:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 02:28:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 02:28:03] local.INFO: Cron job executed {"time":"2025-07-03 02:28:03"} 
[2025-07-03 02:29:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 02:29:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 02:29:01] local.INFO: Cron job executed {"time":"2025-07-03 02:29:01"} 
[2025-07-03 02:29:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 02:29:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 02:29:03] local.INFO: Cron job executed {"time":"2025-07-03 02:29:03"} 
[2025-07-03 02:30:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 02:30:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 02:30:01] local.INFO: Cron job executed {"time":"2025-07-03 02:30:01"} 
[2025-07-03 02:30:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 02:30:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 02:30:03] local.INFO: Cron job executed {"time":"2025-07-03 02:30:03"} 
[2025-07-03 02:31:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 02:31:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 02:31:01] local.INFO: Cron job executed {"time":"2025-07-03 02:31:01"} 
[2025-07-03 02:31:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 02:31:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 02:31:02] local.INFO: Cron job executed {"time":"2025-07-03 02:31:02"} 
[2025-07-03 02:32:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 02:32:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 02:32:01] local.INFO: Cron job executed {"time":"2025-07-03 02:32:01"} 
[2025-07-03 02:32:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 02:32:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 02:32:02] local.INFO: Cron job executed {"time":"2025-07-03 02:32:02"} 
[2025-07-03 02:33:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 02:33:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 02:33:01] local.INFO: Cron job executed {"time":"2025-07-03 02:33:01"} 
[2025-07-03 02:33:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 02:33:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 02:33:02] local.INFO: Cron job executed {"time":"2025-07-03 02:33:02"} 
[2025-07-03 02:34:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 02:34:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 02:34:01] local.INFO: Cron job executed {"time":"2025-07-03 02:34:01"} 
[2025-07-03 02:34:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 02:34:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 02:34:02] local.INFO: Cron job executed {"time":"2025-07-03 02:34:02"} 
[2025-07-03 02:35:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 02:35:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 02:35:01] local.INFO: Cron job executed {"time":"2025-07-03 02:35:01"} 
[2025-07-03 02:35:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 02:35:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 02:35:03] local.INFO: Cron job executed {"time":"2025-07-03 02:35:03"} 
[2025-07-03 02:36:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 02:36:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 02:36:01] local.INFO: Cron job executed {"time":"2025-07-03 02:36:01"} 
[2025-07-03 02:36:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 02:36:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 02:36:02] local.INFO: Cron job executed {"time":"2025-07-03 02:36:02"} 
[2025-07-03 02:37:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 02:37:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 02:37:01] local.INFO: Cron job executed {"time":"2025-07-03 02:37:01"} 
[2025-07-03 02:37:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 02:37:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 02:37:02] local.INFO: Cron job executed {"time":"2025-07-03 02:37:02"} 
[2025-07-03 02:38:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 02:38:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 02:38:01] local.INFO: Cron job executed {"time":"2025-07-03 02:38:01"} 
[2025-07-03 02:38:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 02:38:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 02:38:02] local.INFO: Cron job executed {"time":"2025-07-03 02:38:02"} 
[2025-07-03 02:39:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 02:39:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 02:39:01] local.INFO: Cron job executed {"time":"2025-07-03 02:39:01"} 
[2025-07-03 02:39:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 02:39:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 02:39:02] local.INFO: Cron job executed {"time":"2025-07-03 02:39:02"} 
[2025-07-03 02:40:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 02:40:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 02:40:01] local.INFO: Cron job executed {"time":"2025-07-03 02:40:01"} 
[2025-07-03 02:40:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 02:40:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 02:40:02] local.INFO: Cron job executed {"time":"2025-07-03 02:40:02"} 
[2025-07-03 02:41:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 02:41:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 02:41:01] local.INFO: Cron job executed {"time":"2025-07-03 02:41:01"} 
[2025-07-03 02:41:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 02:41:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 02:41:02] local.INFO: Cron job executed {"time":"2025-07-03 02:41:02"} 
[2025-07-03 02:42:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 02:42:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 02:42:01] local.INFO: Cron job executed {"time":"2025-07-03 02:42:01"} 
[2025-07-03 02:42:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 02:42:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 02:42:02] local.INFO: Cron job executed {"time":"2025-07-03 02:42:02"} 
[2025-07-03 02:43:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 02:43:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 02:43:01] local.INFO: Cron job executed {"time":"2025-07-03 02:43:01"} 
[2025-07-03 02:43:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 02:43:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 02:43:03] local.INFO: Cron job executed {"time":"2025-07-03 02:43:03"} 
[2025-07-03 02:44:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 02:44:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 02:44:01] local.INFO: Cron job executed {"time":"2025-07-03 02:44:01"} 
[2025-07-03 02:44:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 02:44:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 02:44:02] local.INFO: Cron job executed {"time":"2025-07-03 02:44:02"} 
[2025-07-03 02:45:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 02:45:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 02:45:01] local.INFO: Cron job executed {"time":"2025-07-03 02:45:01"} 
[2025-07-03 02:45:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 02:45:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 02:45:02] local.INFO: Cron job executed {"time":"2025-07-03 02:45:02"} 
[2025-07-03 02:46:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 02:46:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 02:46:01] local.INFO: Cron job executed {"time":"2025-07-03 02:46:01"} 
[2025-07-03 02:46:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 02:46:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 02:46:02] local.INFO: Cron job executed {"time":"2025-07-03 02:46:02"} 
[2025-07-03 02:47:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 02:47:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 02:47:01] local.INFO: Cron job executed {"time":"2025-07-03 02:47:01"} 
[2025-07-03 02:47:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 02:47:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 02:47:02] local.INFO: Cron job executed {"time":"2025-07-03 02:47:02"} 
[2025-07-03 02:48:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 02:48:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 02:48:01] local.INFO: Cron job executed {"time":"2025-07-03 02:48:01"} 
[2025-07-03 02:48:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 02:48:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 02:48:03] local.INFO: Cron job executed {"time":"2025-07-03 02:48:03"} 
[2025-07-03 02:49:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 02:49:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 02:49:01] local.INFO: Cron job executed {"time":"2025-07-03 02:49:01"} 
[2025-07-03 02:49:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 02:49:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 02:49:02] local.INFO: Cron job executed {"time":"2025-07-03 02:49:02"} 
[2025-07-03 02:50:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 02:50:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 02:50:01] local.INFO: Cron job executed {"time":"2025-07-03 02:50:01"} 
[2025-07-03 02:50:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 02:50:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 02:50:02] local.INFO: Cron job executed {"time":"2025-07-03 02:50:02"} 
[2025-07-03 02:51:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 02:51:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 02:51:01] local.INFO: Cron job executed {"time":"2025-07-03 02:51:01"} 
[2025-07-03 02:51:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 02:51:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 02:51:02] local.INFO: Cron job executed {"time":"2025-07-03 02:51:02"} 
[2025-07-03 02:52:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 02:52:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 02:52:01] local.INFO: Cron job executed {"time":"2025-07-03 02:52:01"} 
[2025-07-03 02:52:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 02:52:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 02:52:03] local.INFO: Cron job executed {"time":"2025-07-03 02:52:03"} 
[2025-07-03 02:53:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 02:53:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 02:53:01] local.INFO: Cron job executed {"time":"2025-07-03 02:53:01"} 
[2025-07-03 02:53:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 02:53:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 02:53:02] local.INFO: Cron job executed {"time":"2025-07-03 02:53:02"} 
[2025-07-03 02:54:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 02:54:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 02:54:01] local.INFO: Cron job executed {"time":"2025-07-03 02:54:01"} 
[2025-07-03 02:54:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 02:54:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 02:54:02] local.INFO: Cron job executed {"time":"2025-07-03 02:54:02"} 
[2025-07-03 02:55:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 02:55:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 02:55:01] local.INFO: Cron job executed {"time":"2025-07-03 02:55:01"} 
[2025-07-03 02:55:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 02:55:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 02:55:02] local.INFO: Cron job executed {"time":"2025-07-03 02:55:02"} 
[2025-07-03 02:56:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 02:56:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 02:56:01] local.INFO: Cron job executed {"time":"2025-07-03 02:56:01"} 
[2025-07-03 02:56:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 02:56:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 02:56:02] local.INFO: Cron job executed {"time":"2025-07-03 02:56:02"} 
[2025-07-03 02:57:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 02:57:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 02:57:01] local.INFO: Cron job executed {"time":"2025-07-03 02:57:01"} 
[2025-07-03 02:57:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 02:57:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 02:57:02] local.INFO: Cron job executed {"time":"2025-07-03 02:57:02"} 
[2025-07-03 02:58:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 02:58:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 02:58:01] local.INFO: Cron job executed {"time":"2025-07-03 02:58:01"} 
[2025-07-03 02:58:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 02:58:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 02:58:02] local.INFO: Cron job executed {"time":"2025-07-03 02:58:02"} 
[2025-07-03 02:59:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 02:59:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 02:59:01] local.INFO: Cron job executed {"time":"2025-07-03 02:59:01"} 
[2025-07-03 02:59:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 02:59:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 02:59:03] local.INFO: Cron job executed {"time":"2025-07-03 02:59:03"} 
[2025-07-03 03:00:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 03:00:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 03:00:01] local.INFO: Cron job executed {"time":"2025-07-03 03:00:01"} 
[2025-07-03 03:00:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 03:00:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 03:00:02] local.INFO: Cron job executed {"time":"2025-07-03 03:00:02"} 
[2025-07-03 03:01:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 03:01:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 03:01:01] local.INFO: Cron job executed {"time":"2025-07-03 03:01:01"} 
[2025-07-03 03:01:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 03:01:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 03:01:02] local.INFO: Cron job executed {"time":"2025-07-03 03:01:02"} 
[2025-07-03 03:02:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 03:02:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 03:02:01] local.INFO: Cron job executed {"time":"2025-07-03 03:02:01"} 
[2025-07-03 03:02:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 03:02:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 03:02:02] local.INFO: Cron job executed {"time":"2025-07-03 03:02:02"} 
[2025-07-03 03:03:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 03:03:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 03:03:01] local.INFO: Cron job executed {"time":"2025-07-03 03:03:01"} 
[2025-07-03 03:03:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 03:03:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 03:03:02] local.INFO: Cron job executed {"time":"2025-07-03 03:03:02"} 
[2025-07-03 03:04:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 03:04:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 03:04:01] local.INFO: Cron job executed {"time":"2025-07-03 03:04:01"} 
[2025-07-03 03:04:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 03:04:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 03:04:02] local.INFO: Cron job executed {"time":"2025-07-03 03:04:02"} 
[2025-07-03 03:05:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 03:05:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 03:05:01] local.INFO: Cron job executed {"time":"2025-07-03 03:05:01"} 
[2025-07-03 03:05:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 03:05:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 03:05:02] local.INFO: Cron job executed {"time":"2025-07-03 03:05:02"} 
[2025-07-03 03:06:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 03:06:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 03:06:01] local.INFO: Cron job executed {"time":"2025-07-03 03:06:01"} 
[2025-07-03 03:06:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 03:06:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 03:06:03] local.INFO: Cron job executed {"time":"2025-07-03 03:06:03"} 
[2025-07-03 03:07:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 03:07:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 03:07:01] local.INFO: Cron job executed {"time":"2025-07-03 03:07:01"} 
[2025-07-03 03:07:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 03:07:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 03:07:02] local.INFO: Cron job executed {"time":"2025-07-03 03:07:02"} 
[2025-07-03 03:08:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 03:08:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 03:08:01] local.INFO: Cron job executed {"time":"2025-07-03 03:08:01"} 
[2025-07-03 03:08:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 03:08:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 03:08:03] local.INFO: Cron job executed {"time":"2025-07-03 03:08:03"} 
[2025-07-03 03:09:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 03:09:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 03:09:01] local.INFO: Cron job executed {"time":"2025-07-03 03:09:01"} 
[2025-07-03 03:09:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 03:09:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 03:09:02] local.INFO: Cron job executed {"time":"2025-07-03 03:09:02"} 
[2025-07-03 03:10:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 03:10:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 03:10:01] local.INFO: Cron job executed {"time":"2025-07-03 03:10:01"} 
[2025-07-03 03:10:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 03:10:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 03:10:02] local.INFO: Cron job executed {"time":"2025-07-03 03:10:02"} 
[2025-07-03 03:11:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 03:11:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 03:11:01] local.INFO: Cron job executed {"time":"2025-07-03 03:11:01"} 
[2025-07-03 03:11:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 03:11:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 03:11:02] local.INFO: Cron job executed {"time":"2025-07-03 03:11:02"} 
[2025-07-03 03:12:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 03:12:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 03:12:01] local.INFO: Cron job executed {"time":"2025-07-03 03:12:01"} 
[2025-07-03 03:12:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 03:12:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 03:12:02] local.INFO: Cron job executed {"time":"2025-07-03 03:12:02"} 
[2025-07-03 03:13:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 03:13:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 03:13:01] local.INFO: Cron job executed {"time":"2025-07-03 03:13:01"} 
[2025-07-03 03:13:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 03:13:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 03:13:02] local.INFO: Cron job executed {"time":"2025-07-03 03:13:02"} 
[2025-07-03 03:14:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 03:14:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 03:14:01] local.INFO: Cron job executed {"time":"2025-07-03 03:14:01"} 
[2025-07-03 03:14:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 03:14:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 03:14:02] local.INFO: Cron job executed {"time":"2025-07-03 03:14:02"} 
[2025-07-03 03:15:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 03:15:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 03:15:01] local.INFO: Cron job executed {"time":"2025-07-03 03:15:01"} 
[2025-07-03 03:15:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 03:15:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 03:15:02] local.INFO: Cron job executed {"time":"2025-07-03 03:15:02"} 
[2025-07-03 03:16:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 03:16:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 03:16:01] local.INFO: Cron job executed {"time":"2025-07-03 03:16:01"} 
[2025-07-03 03:16:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 03:16:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 03:16:02] local.INFO: Cron job executed {"time":"2025-07-03 03:16:02"} 
[2025-07-03 03:17:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 03:17:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 03:17:01] local.INFO: Cron job executed {"time":"2025-07-03 03:17:01"} 
[2025-07-03 03:17:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 03:17:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 03:17:02] local.INFO: Cron job executed {"time":"2025-07-03 03:17:02"} 
[2025-07-03 03:18:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 03:18:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 03:18:01] local.INFO: Cron job executed {"time":"2025-07-03 03:18:01"} 
[2025-07-03 03:18:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 03:18:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 03:18:03] local.INFO: Cron job executed {"time":"2025-07-03 03:18:03"} 
[2025-07-03 03:19:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 03:19:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 03:19:01] local.INFO: Cron job executed {"time":"2025-07-03 03:19:01"} 
[2025-07-03 03:19:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 03:19:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 03:19:02] local.INFO: Cron job executed {"time":"2025-07-03 03:19:02"} 
[2025-07-03 03:20:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 03:20:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 03:20:01] local.INFO: Cron job executed {"time":"2025-07-03 03:20:01"} 
[2025-07-03 03:20:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 03:20:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 03:20:02] local.INFO: Cron job executed {"time":"2025-07-03 03:20:02"} 
[2025-07-03 03:21:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 03:21:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 03:21:01] local.INFO: Cron job executed {"time":"2025-07-03 03:21:01"} 
[2025-07-03 03:21:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 03:21:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 03:21:02] local.INFO: Cron job executed {"time":"2025-07-03 03:21:02"} 
[2025-07-03 03:22:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 03:22:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 03:22:01] local.INFO: Cron job executed {"time":"2025-07-03 03:22:01"} 
[2025-07-03 03:22:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 03:22:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 03:22:02] local.INFO: Cron job executed {"time":"2025-07-03 03:22:02"} 
[2025-07-03 03:23:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 03:23:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 03:23:01] local.INFO: Cron job executed {"time":"2025-07-03 03:23:01"} 
[2025-07-03 03:23:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 03:23:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 03:23:02] local.INFO: Cron job executed {"time":"2025-07-03 03:23:02"} 
[2025-07-03 03:24:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 03:24:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 03:24:01] local.INFO: Cron job executed {"time":"2025-07-03 03:24:01"} 
[2025-07-03 03:24:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 03:24:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 03:24:02] local.INFO: Cron job executed {"time":"2025-07-03 03:24:02"} 
[2025-07-03 03:25:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 03:25:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 03:25:01] local.INFO: Cron job executed {"time":"2025-07-03 03:25:01"} 
[2025-07-03 03:25:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 03:25:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 03:25:02] local.INFO: Cron job executed {"time":"2025-07-03 03:25:02"} 
[2025-07-03 03:26:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 03:26:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 03:26:01] local.INFO: Cron job executed {"time":"2025-07-03 03:26:01"} 
[2025-07-03 03:26:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 03:26:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 03:26:02] local.INFO: Cron job executed {"time":"2025-07-03 03:26:02"} 
[2025-07-03 03:27:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 03:27:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 03:27:01] local.INFO: Cron job executed {"time":"2025-07-03 03:27:01"} 
[2025-07-03 03:27:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 03:27:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 03:27:02] local.INFO: Cron job executed {"time":"2025-07-03 03:27:02"} 
[2025-07-03 03:28:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 03:28:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 03:28:01] local.INFO: Cron job executed {"time":"2025-07-03 03:28:01"} 
[2025-07-03 03:28:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 03:28:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 03:28:02] local.INFO: Cron job executed {"time":"2025-07-03 03:28:02"} 
[2025-07-03 03:29:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 03:29:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 03:29:01] local.INFO: Cron job executed {"time":"2025-07-03 03:29:01"} 
[2025-07-03 03:29:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 03:29:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 03:29:03] local.INFO: Cron job executed {"time":"2025-07-03 03:29:03"} 
[2025-07-03 03:30:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 03:30:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 03:30:01] local.INFO: Cron job executed {"time":"2025-07-03 03:30:01"} 
[2025-07-03 03:30:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 03:30:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 03:30:03] local.INFO: Cron job executed {"time":"2025-07-03 03:30:03"} 
[2025-07-03 03:31:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 03:31:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 03:31:01] local.INFO: Cron job executed {"time":"2025-07-03 03:31:01"} 
[2025-07-03 03:31:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 03:31:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 03:31:02] local.INFO: Cron job executed {"time":"2025-07-03 03:31:02"} 
[2025-07-03 03:32:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 03:32:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 03:32:01] local.INFO: Cron job executed {"time":"2025-07-03 03:32:01"} 
[2025-07-03 03:32:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 03:32:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 03:32:02] local.INFO: Cron job executed {"time":"2025-07-03 03:32:02"} 
[2025-07-03 03:33:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 03:33:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 03:33:01] local.INFO: Cron job executed {"time":"2025-07-03 03:33:01"} 
[2025-07-03 03:33:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 03:33:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 03:33:02] local.INFO: Cron job executed {"time":"2025-07-03 03:33:02"} 
[2025-07-03 03:34:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 03:34:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 03:34:01] local.INFO: Cron job executed {"time":"2025-07-03 03:34:01"} 
[2025-07-03 03:34:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 03:34:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 03:34:03] local.INFO: Cron job executed {"time":"2025-07-03 03:34:03"} 
[2025-07-03 03:35:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 03:35:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 03:35:01] local.INFO: Cron job executed {"time":"2025-07-03 03:35:01"} 
[2025-07-03 03:35:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 03:35:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 03:35:02] local.INFO: Cron job executed {"time":"2025-07-03 03:35:02"} 
[2025-07-03 03:36:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 03:36:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 03:36:01] local.INFO: Cron job executed {"time":"2025-07-03 03:36:01"} 
[2025-07-03 03:36:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 03:36:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 03:36:02] local.INFO: Cron job executed {"time":"2025-07-03 03:36:02"} 
[2025-07-03 03:37:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 03:37:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 03:37:01] local.INFO: Cron job executed {"time":"2025-07-03 03:37:01"} 
[2025-07-03 03:37:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 03:37:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 03:37:02] local.INFO: Cron job executed {"time":"2025-07-03 03:37:02"} 
[2025-07-03 03:38:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 03:38:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 03:38:01] local.INFO: Cron job executed {"time":"2025-07-03 03:38:01"} 
[2025-07-03 03:38:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 03:38:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 03:38:03] local.INFO: Cron job executed {"time":"2025-07-03 03:38:03"} 
[2025-07-03 03:39:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 03:39:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 03:39:01] local.INFO: Cron job executed {"time":"2025-07-03 03:39:01"} 
[2025-07-03 03:39:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 03:39:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 03:39:02] local.INFO: Cron job executed {"time":"2025-07-03 03:39:02"} 
[2025-07-03 03:40:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 03:40:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 03:40:01] local.INFO: Cron job executed {"time":"2025-07-03 03:40:01"} 
[2025-07-03 03:40:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 03:40:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 03:40:02] local.INFO: Cron job executed {"time":"2025-07-03 03:40:02"} 
[2025-07-03 03:41:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 03:41:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 03:41:01] local.INFO: Cron job executed {"time":"2025-07-03 03:41:01"} 
[2025-07-03 03:41:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 03:41:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 03:41:02] local.INFO: Cron job executed {"time":"2025-07-03 03:41:02"} 
[2025-07-03 03:42:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 03:42:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 03:42:01] local.INFO: Cron job executed {"time":"2025-07-03 03:42:01"} 
[2025-07-03 03:42:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 03:42:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 03:42:02] local.INFO: Cron job executed {"time":"2025-07-03 03:42:02"} 
[2025-07-03 03:43:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 03:43:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 03:43:01] local.INFO: Cron job executed {"time":"2025-07-03 03:43:01"} 
[2025-07-03 03:43:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 03:43:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 03:43:03] local.INFO: Cron job executed {"time":"2025-07-03 03:43:03"} 
[2025-07-03 03:44:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 03:44:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 03:44:01] local.INFO: Cron job executed {"time":"2025-07-03 03:44:01"} 
[2025-07-03 03:44:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 03:44:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 03:44:03] local.INFO: Cron job executed {"time":"2025-07-03 03:44:03"} 
[2025-07-03 03:45:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 03:45:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 03:45:01] local.INFO: Cron job executed {"time":"2025-07-03 03:45:01"} 
[2025-07-03 03:45:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 03:45:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 03:45:02] local.INFO: Cron job executed {"time":"2025-07-03 03:45:02"} 
[2025-07-03 03:46:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 03:46:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 03:46:01] local.INFO: Cron job executed {"time":"2025-07-03 03:46:01"} 
[2025-07-03 03:46:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 03:46:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 03:46:03] local.INFO: Cron job executed {"time":"2025-07-03 03:46:03"} 
[2025-07-03 03:47:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 03:47:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 03:47:01] local.INFO: Cron job executed {"time":"2025-07-03 03:47:01"} 
[2025-07-03 03:47:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 03:47:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 03:47:03] local.INFO: Cron job executed {"time":"2025-07-03 03:47:03"} 
[2025-07-03 03:48:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 03:48:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 03:48:01] local.INFO: Cron job executed {"time":"2025-07-03 03:48:01"} 
[2025-07-03 03:48:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 03:48:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 03:48:02] local.INFO: Cron job executed {"time":"2025-07-03 03:48:02"} 
[2025-07-03 03:49:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 03:49:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 03:49:01] local.INFO: Cron job executed {"time":"2025-07-03 03:49:01"} 
[2025-07-03 03:49:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 03:49:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 03:49:02] local.INFO: Cron job executed {"time":"2025-07-03 03:49:02"} 
[2025-07-03 03:50:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 03:50:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 03:50:01] local.INFO: Cron job executed {"time":"2025-07-03 03:50:01"} 
[2025-07-03 03:50:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 03:50:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 03:50:03] local.INFO: Cron job executed {"time":"2025-07-03 03:50:03"} 
[2025-07-03 03:51:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 03:51:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 03:51:01] local.INFO: Cron job executed {"time":"2025-07-03 03:51:01"} 
[2025-07-03 03:51:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 03:51:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 03:51:02] local.INFO: Cron job executed {"time":"2025-07-03 03:51:02"} 
[2025-07-03 03:52:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 03:52:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 03:52:01] local.INFO: Cron job executed {"time":"2025-07-03 03:52:01"} 
[2025-07-03 03:52:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 03:52:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 03:52:02] local.INFO: Cron job executed {"time":"2025-07-03 03:52:02"} 
[2025-07-03 03:53:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 03:53:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 03:53:01] local.INFO: Cron job executed {"time":"2025-07-03 03:53:01"} 
[2025-07-03 03:53:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 03:53:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 03:53:02] local.INFO: Cron job executed {"time":"2025-07-03 03:53:02"} 
[2025-07-03 03:54:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 03:54:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 03:54:01] local.INFO: Cron job executed {"time":"2025-07-03 03:54:01"} 
[2025-07-03 03:54:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 03:54:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 03:54:03] local.INFO: Cron job executed {"time":"2025-07-03 03:54:03"} 
[2025-07-03 03:55:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 03:55:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 03:55:01] local.INFO: Cron job executed {"time":"2025-07-03 03:55:01"} 
[2025-07-03 03:55:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 03:55:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 03:55:02] local.INFO: Cron job executed {"time":"2025-07-03 03:55:02"} 
[2025-07-03 03:56:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 03:56:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 03:56:01] local.INFO: Cron job executed {"time":"2025-07-03 03:56:01"} 
[2025-07-03 03:56:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 03:56:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 03:56:03] local.INFO: Cron job executed {"time":"2025-07-03 03:56:03"} 
[2025-07-03 03:57:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 03:57:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 03:57:01] local.INFO: Cron job executed {"time":"2025-07-03 03:57:01"} 
[2025-07-03 03:57:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 03:57:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 03:57:02] local.INFO: Cron job executed {"time":"2025-07-03 03:57:02"} 
[2025-07-03 03:58:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 03:58:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 03:58:01] local.INFO: Cron job executed {"time":"2025-07-03 03:58:01"} 
[2025-07-03 03:58:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 03:58:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 03:58:03] local.INFO: Cron job executed {"time":"2025-07-03 03:58:03"} 
[2025-07-03 03:59:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 03:59:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 03:59:01] local.INFO: Cron job executed {"time":"2025-07-03 03:59:01"} 
[2025-07-03 03:59:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 03:59:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 03:59:02] local.INFO: Cron job executed {"time":"2025-07-03 03:59:02"} 
[2025-07-03 04:00:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 04:00:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 04:00:01] local.INFO: Cron job executed {"time":"2025-07-03 04:00:01"} 
[2025-07-03 04:00:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 04:00:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 04:00:03] local.INFO: Cron job executed {"time":"2025-07-03 04:00:03"} 
[2025-07-03 04:01:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 04:01:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 04:01:01] local.INFO: Cron job executed {"time":"2025-07-03 04:01:01"} 
[2025-07-03 04:01:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 04:01:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 04:01:02] local.INFO: Cron job executed {"time":"2025-07-03 04:01:02"} 
[2025-07-03 04:02:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 04:02:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 04:02:01] local.INFO: Cron job executed {"time":"2025-07-03 04:02:01"} 
[2025-07-03 04:02:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 04:02:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 04:02:03] local.INFO: Cron job executed {"time":"2025-07-03 04:02:03"} 
[2025-07-03 04:03:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 04:03:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 04:03:01] local.INFO: Cron job executed {"time":"2025-07-03 04:03:01"} 
[2025-07-03 04:03:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 04:03:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 04:03:02] local.INFO: Cron job executed {"time":"2025-07-03 04:03:02"} 
[2025-07-03 04:04:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 04:04:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 04:04:01] local.INFO: Cron job executed {"time":"2025-07-03 04:04:01"} 
[2025-07-03 04:04:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 04:04:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 04:04:02] local.INFO: Cron job executed {"time":"2025-07-03 04:04:02"} 
[2025-07-03 04:05:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 04:05:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 04:05:01] local.INFO: Cron job executed {"time":"2025-07-03 04:05:01"} 
[2025-07-03 04:05:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 04:05:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 04:05:02] local.INFO: Cron job executed {"time":"2025-07-03 04:05:02"} 
[2025-07-03 04:06:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 04:06:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 04:06:01] local.INFO: Cron job executed {"time":"2025-07-03 04:06:01"} 
[2025-07-03 04:06:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 04:06:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 04:06:02] local.INFO: Cron job executed {"time":"2025-07-03 04:06:02"} 
[2025-07-03 04:07:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 04:07:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 04:07:01] local.INFO: Cron job executed {"time":"2025-07-03 04:07:01"} 
[2025-07-03 04:07:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 04:07:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 04:07:02] local.INFO: Cron job executed {"time":"2025-07-03 04:07:02"} 
[2025-07-03 04:08:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 04:08:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 04:08:01] local.INFO: Cron job executed {"time":"2025-07-03 04:08:01"} 
[2025-07-03 04:08:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 04:08:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 04:08:02] local.INFO: Cron job executed {"time":"2025-07-03 04:08:02"} 
[2025-07-03 04:09:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 04:09:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 04:09:01] local.INFO: Cron job executed {"time":"2025-07-03 04:09:01"} 
[2025-07-03 04:09:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 04:09:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 04:09:02] local.INFO: Cron job executed {"time":"2025-07-03 04:09:02"} 
[2025-07-03 04:10:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 04:10:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 04:10:01] local.INFO: Cron job executed {"time":"2025-07-03 04:10:01"} 
[2025-07-03 04:10:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 04:10:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 04:10:02] local.INFO: Cron job executed {"time":"2025-07-03 04:10:02"} 
[2025-07-03 04:11:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 04:11:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 04:11:01] local.INFO: Cron job executed {"time":"2025-07-03 04:11:01"} 
[2025-07-03 04:11:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 04:11:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 04:11:02] local.INFO: Cron job executed {"time":"2025-07-03 04:11:02"} 
[2025-07-03 04:12:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 04:12:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 04:12:01] local.INFO: Cron job executed {"time":"2025-07-03 04:12:01"} 
[2025-07-03 04:12:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 04:12:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 04:12:03] local.INFO: Cron job executed {"time":"2025-07-03 04:12:03"} 
[2025-07-03 04:13:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 04:13:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 04:13:01] local.INFO: Cron job executed {"time":"2025-07-03 04:13:01"} 
[2025-07-03 04:13:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 04:13:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 04:13:02] local.INFO: Cron job executed {"time":"2025-07-03 04:13:02"} 
[2025-07-03 04:14:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 04:14:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 04:14:01] local.INFO: Cron job executed {"time":"2025-07-03 04:14:01"} 
[2025-07-03 04:14:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 04:14:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 04:14:02] local.INFO: Cron job executed {"time":"2025-07-03 04:14:02"} 
[2025-07-03 04:15:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 04:15:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 04:15:01] local.INFO: Cron job executed {"time":"2025-07-03 04:15:01"} 
[2025-07-03 04:15:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 04:15:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 04:15:02] local.INFO: Cron job executed {"time":"2025-07-03 04:15:02"} 
[2025-07-03 04:16:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 04:16:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 04:16:01] local.INFO: Cron job executed {"time":"2025-07-03 04:16:01"} 
[2025-07-03 04:16:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 04:16:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 04:16:02] local.INFO: Cron job executed {"time":"2025-07-03 04:16:02"} 
[2025-07-03 04:17:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 04:17:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 04:17:01] local.INFO: Cron job executed {"time":"2025-07-03 04:17:01"} 
[2025-07-03 04:17:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 04:17:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 04:17:02] local.INFO: Cron job executed {"time":"2025-07-03 04:17:02"} 
[2025-07-03 04:18:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 04:18:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 04:18:01] local.INFO: Cron job executed {"time":"2025-07-03 04:18:01"} 
[2025-07-03 04:18:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 04:18:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 04:18:02] local.INFO: Cron job executed {"time":"2025-07-03 04:18:02"} 
[2025-07-03 04:19:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 04:19:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 04:19:01] local.INFO: Cron job executed {"time":"2025-07-03 04:19:01"} 
[2025-07-03 04:19:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 04:19:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 04:19:03] local.INFO: Cron job executed {"time":"2025-07-03 04:19:03"} 
[2025-07-03 04:20:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 04:20:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 04:20:01] local.INFO: Cron job executed {"time":"2025-07-03 04:20:01"} 
[2025-07-03 04:20:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 04:20:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 04:20:02] local.INFO: Cron job executed {"time":"2025-07-03 04:20:02"} 
[2025-07-03 04:21:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 04:21:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 04:21:01] local.INFO: Cron job executed {"time":"2025-07-03 04:21:01"} 
[2025-07-03 04:21:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 04:21:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 04:21:02] local.INFO: Cron job executed {"time":"2025-07-03 04:21:02"} 
[2025-07-03 04:22:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 04:22:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 04:22:01] local.INFO: Cron job executed {"time":"2025-07-03 04:22:01"} 
[2025-07-03 04:22:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 04:22:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 04:22:02] local.INFO: Cron job executed {"time":"2025-07-03 04:22:02"} 
[2025-07-03 04:23:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 04:23:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 04:23:01] local.INFO: Cron job executed {"time":"2025-07-03 04:23:01"} 
[2025-07-03 04:23:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 04:23:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 04:23:02] local.INFO: Cron job executed {"time":"2025-07-03 04:23:02"} 
[2025-07-03 04:24:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 04:24:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 04:24:01] local.INFO: Cron job executed {"time":"2025-07-03 04:24:01"} 
[2025-07-03 04:24:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 04:24:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 04:24:02] local.INFO: Cron job executed {"time":"2025-07-03 04:24:02"} 
[2025-07-03 04:25:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 04:25:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 04:25:01] local.INFO: Cron job executed {"time":"2025-07-03 04:25:01"} 
[2025-07-03 04:25:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 04:25:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 04:25:02] local.INFO: Cron job executed {"time":"2025-07-03 04:25:02"} 
[2025-07-03 04:26:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 04:26:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 04:26:01] local.INFO: Cron job executed {"time":"2025-07-03 04:26:01"} 
[2025-07-03 04:26:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 04:26:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 04:26:03] local.INFO: Cron job executed {"time":"2025-07-03 04:26:03"} 
[2025-07-03 04:27:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 04:27:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 04:27:01] local.INFO: Cron job executed {"time":"2025-07-03 04:27:01"} 
[2025-07-03 04:27:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 04:27:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 04:27:02] local.INFO: Cron job executed {"time":"2025-07-03 04:27:02"} 
[2025-07-03 04:28:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 04:28:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 04:28:01] local.INFO: Cron job executed {"time":"2025-07-03 04:28:01"} 
[2025-07-03 04:28:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 04:28:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 04:28:03] local.INFO: Cron job executed {"time":"2025-07-03 04:28:03"} 
[2025-07-03 04:29:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 04:29:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 04:29:01] local.INFO: Cron job executed {"time":"2025-07-03 04:29:01"} 
[2025-07-03 04:29:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 04:29:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 04:29:02] local.INFO: Cron job executed {"time":"2025-07-03 04:29:02"} 
[2025-07-03 04:30:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 04:30:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 04:30:01] local.INFO: Cron job executed {"time":"2025-07-03 04:30:01"} 
[2025-07-03 04:30:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 04:30:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 04:30:02] local.INFO: Cron job executed {"time":"2025-07-03 04:30:02"} 
[2025-07-03 04:31:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 04:31:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 04:31:01] local.INFO: Cron job executed {"time":"2025-07-03 04:31:01"} 
[2025-07-03 04:31:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 04:31:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 04:31:03] local.INFO: Cron job executed {"time":"2025-07-03 04:31:03"} 
[2025-07-03 04:32:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 04:32:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 04:32:01] local.INFO: Cron job executed {"time":"2025-07-03 04:32:01"} 
[2025-07-03 04:32:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 04:32:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 04:32:02] local.INFO: Cron job executed {"time":"2025-07-03 04:32:02"} 
[2025-07-03 04:33:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 04:33:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 04:33:01] local.INFO: Cron job executed {"time":"2025-07-03 04:33:01"} 
[2025-07-03 04:33:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 04:33:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 04:33:02] local.INFO: Cron job executed {"time":"2025-07-03 04:33:02"} 
[2025-07-03 04:34:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 04:34:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 04:34:01] local.INFO: Cron job executed {"time":"2025-07-03 04:34:01"} 
[2025-07-03 04:34:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 04:34:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 04:34:02] local.INFO: Cron job executed {"time":"2025-07-03 04:34:02"} 
[2025-07-03 04:35:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 04:35:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 04:35:01] local.INFO: Cron job executed {"time":"2025-07-03 04:35:01"} 
[2025-07-03 04:35:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 04:35:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 04:35:02] local.INFO: Cron job executed {"time":"2025-07-03 04:35:02"} 
[2025-07-03 04:36:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 04:36:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 04:36:01] local.INFO: Cron job executed {"time":"2025-07-03 04:36:01"} 
[2025-07-03 04:36:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 04:36:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 04:36:02] local.INFO: Cron job executed {"time":"2025-07-03 04:36:02"} 
[2025-07-03 04:37:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 04:37:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 04:37:01] local.INFO: Cron job executed {"time":"2025-07-03 04:37:01"} 
[2025-07-03 04:37:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 04:37:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 04:37:02] local.INFO: Cron job executed {"time":"2025-07-03 04:37:02"} 
[2025-07-03 04:38:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 04:38:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 04:38:01] local.INFO: Cron job executed {"time":"2025-07-03 04:38:01"} 
[2025-07-03 04:38:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 04:38:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 04:38:03] local.INFO: Cron job executed {"time":"2025-07-03 04:38:03"} 
[2025-07-03 04:39:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 04:39:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 04:39:01] local.INFO: Cron job executed {"time":"2025-07-03 04:39:01"} 
[2025-07-03 04:39:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 04:39:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 04:39:02] local.INFO: Cron job executed {"time":"2025-07-03 04:39:02"} 
[2025-07-03 04:40:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 04:40:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 04:40:01] local.INFO: Cron job executed {"time":"2025-07-03 04:40:01"} 
[2025-07-03 04:40:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 04:40:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 04:40:03] local.INFO: Cron job executed {"time":"2025-07-03 04:40:03"} 
[2025-07-03 04:41:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 04:41:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 04:41:01] local.INFO: Cron job executed {"time":"2025-07-03 04:41:01"} 
[2025-07-03 04:41:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 04:41:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 04:41:02] local.INFO: Cron job executed {"time":"2025-07-03 04:41:02"} 
[2025-07-03 04:42:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 04:42:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 04:42:01] local.INFO: Cron job executed {"time":"2025-07-03 04:42:01"} 
[2025-07-03 04:42:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 04:42:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 04:42:02] local.INFO: Cron job executed {"time":"2025-07-03 04:42:02"} 
[2025-07-03 04:43:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 04:43:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 04:43:01] local.INFO: Cron job executed {"time":"2025-07-03 04:43:01"} 
[2025-07-03 04:43:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 04:43:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 04:43:02] local.INFO: Cron job executed {"time":"2025-07-03 04:43:02"} 
[2025-07-03 04:44:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 04:44:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 04:44:01] local.INFO: Cron job executed {"time":"2025-07-03 04:44:01"} 
[2025-07-03 04:44:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 04:44:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 04:44:03] local.INFO: Cron job executed {"time":"2025-07-03 04:44:03"} 
[2025-07-03 04:45:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 04:45:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 04:45:01] local.INFO: Cron job executed {"time":"2025-07-03 04:45:01"} 
[2025-07-03 04:45:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 04:45:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 04:45:02] local.INFO: Cron job executed {"time":"2025-07-03 04:45:02"} 
[2025-07-03 04:46:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 04:46:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 04:46:01] local.INFO: Cron job executed {"time":"2025-07-03 04:46:01"} 
[2025-07-03 04:46:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 04:46:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 04:46:02] local.INFO: Cron job executed {"time":"2025-07-03 04:46:02"} 
[2025-07-03 04:47:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 04:47:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 04:47:01] local.INFO: Cron job executed {"time":"2025-07-03 04:47:01"} 
[2025-07-03 04:47:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 04:47:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 04:47:03] local.INFO: Cron job executed {"time":"2025-07-03 04:47:03"} 
[2025-07-03 04:48:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 04:48:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 04:48:01] local.INFO: Cron job executed {"time":"2025-07-03 04:48:01"} 
[2025-07-03 04:48:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 04:48:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 04:48:02] local.INFO: Cron job executed {"time":"2025-07-03 04:48:02"} 
[2025-07-03 04:49:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 04:49:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 04:49:01] local.INFO: Cron job executed {"time":"2025-07-03 04:49:01"} 
[2025-07-03 04:49:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 04:49:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 04:49:02] local.INFO: Cron job executed {"time":"2025-07-03 04:49:02"} 
[2025-07-03 04:50:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 04:50:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 04:50:01] local.INFO: Cron job executed {"time":"2025-07-03 04:50:01"} 
[2025-07-03 04:50:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 04:50:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 04:50:03] local.INFO: Cron job executed {"time":"2025-07-03 04:50:03"} 
[2025-07-03 04:51:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 04:51:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 04:51:01] local.INFO: Cron job executed {"time":"2025-07-03 04:51:01"} 
[2025-07-03 04:51:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 04:51:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 04:51:02] local.INFO: Cron job executed {"time":"2025-07-03 04:51:02"} 
[2025-07-03 04:52:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 04:52:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 04:52:01] local.INFO: Cron job executed {"time":"2025-07-03 04:52:01"} 
[2025-07-03 04:52:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 04:52:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 04:52:02] local.INFO: Cron job executed {"time":"2025-07-03 04:52:02"} 
[2025-07-03 04:53:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 04:53:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 04:53:01] local.INFO: Cron job executed {"time":"2025-07-03 04:53:01"} 
[2025-07-03 04:53:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 04:53:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 04:53:02] local.INFO: Cron job executed {"time":"2025-07-03 04:53:02"} 
[2025-07-03 04:54:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 04:54:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 04:54:01] local.INFO: Cron job executed {"time":"2025-07-03 04:54:01"} 
[2025-07-03 04:54:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 04:54:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 04:54:02] local.INFO: Cron job executed {"time":"2025-07-03 04:54:02"} 
[2025-07-03 04:55:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 04:55:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 04:55:01] local.INFO: Cron job executed {"time":"2025-07-03 04:55:01"} 
[2025-07-03 04:55:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 04:55:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 04:55:02] local.INFO: Cron job executed {"time":"2025-07-03 04:55:02"} 
[2025-07-03 04:56:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 04:56:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 04:56:01] local.INFO: Cron job executed {"time":"2025-07-03 04:56:01"} 
[2025-07-03 04:56:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 04:56:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 04:56:02] local.INFO: Cron job executed {"time":"2025-07-03 04:56:02"} 
[2025-07-03 04:57:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 04:57:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 04:57:01] local.INFO: Cron job executed {"time":"2025-07-03 04:57:01"} 
[2025-07-03 04:57:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 04:57:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 04:57:02] local.INFO: Cron job executed {"time":"2025-07-03 04:57:02"} 
[2025-07-03 04:58:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 04:58:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 04:58:01] local.INFO: Cron job executed {"time":"2025-07-03 04:58:01"} 
[2025-07-03 04:58:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 04:58:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 04:58:03] local.INFO: Cron job executed {"time":"2025-07-03 04:58:03"} 
[2025-07-03 04:59:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 04:59:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 04:59:01] local.INFO: Cron job executed {"time":"2025-07-03 04:59:01"} 
[2025-07-03 04:59:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 04:59:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 04:59:02] local.INFO: Cron job executed {"time":"2025-07-03 04:59:02"} 
[2025-07-03 05:00:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 05:00:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 05:00:01] local.INFO: Cron job executed {"time":"2025-07-03 05:00:01"} 
[2025-07-03 05:00:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 05:00:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 05:00:02] local.INFO: Cron job executed {"time":"2025-07-03 05:00:02"} 
[2025-07-03 05:01:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 05:01:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 05:01:01] local.INFO: Cron job executed {"time":"2025-07-03 05:01:01"} 
[2025-07-03 05:01:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 05:01:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 05:01:02] local.INFO: Cron job executed {"time":"2025-07-03 05:01:02"} 
[2025-07-03 05:02:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 05:02:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 05:02:01] local.INFO: Cron job executed {"time":"2025-07-03 05:02:01"} 
[2025-07-03 05:02:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 05:02:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 05:02:03] local.INFO: Cron job executed {"time":"2025-07-03 05:02:03"} 
[2025-07-03 05:03:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 05:03:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 05:03:01] local.INFO: Cron job executed {"time":"2025-07-03 05:03:01"} 
[2025-07-03 05:03:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 05:03:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 05:03:02] local.INFO: Cron job executed {"time":"2025-07-03 05:03:02"} 
[2025-07-03 05:04:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 05:04:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 05:04:01] local.INFO: Cron job executed {"time":"2025-07-03 05:04:01"} 
[2025-07-03 05:04:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 05:04:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 05:04:02] local.INFO: Cron job executed {"time":"2025-07-03 05:04:02"} 
[2025-07-03 05:05:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 05:05:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 05:05:01] local.INFO: Cron job executed {"time":"2025-07-03 05:05:01"} 
[2025-07-03 05:05:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 05:05:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 05:05:02] local.INFO: Cron job executed {"time":"2025-07-03 05:05:02"} 
[2025-07-03 05:06:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 05:06:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 05:06:01] local.INFO: Cron job executed {"time":"2025-07-03 05:06:01"} 
[2025-07-03 05:06:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 05:06:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 05:06:02] local.INFO: Cron job executed {"time":"2025-07-03 05:06:02"} 
[2025-07-03 05:07:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 05:07:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 05:07:01] local.INFO: Cron job executed {"time":"2025-07-03 05:07:01"} 
[2025-07-03 05:07:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 05:07:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 05:07:02] local.INFO: Cron job executed {"time":"2025-07-03 05:07:02"} 
[2025-07-03 05:08:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 05:08:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 05:08:01] local.INFO: Cron job executed {"time":"2025-07-03 05:08:01"} 
[2025-07-03 05:08:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 05:08:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 05:08:02] local.INFO: Cron job executed {"time":"2025-07-03 05:08:02"} 
[2025-07-03 05:09:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 05:09:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 05:09:01] local.INFO: Cron job executed {"time":"2025-07-03 05:09:01"} 
[2025-07-03 05:09:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 05:09:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 05:09:03] local.INFO: Cron job executed {"time":"2025-07-03 05:09:03"} 
[2025-07-03 05:10:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 05:10:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 05:10:01] local.INFO: Cron job executed {"time":"2025-07-03 05:10:01"} 
[2025-07-03 05:10:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 05:10:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 05:10:02] local.INFO: Cron job executed {"time":"2025-07-03 05:10:02"} 
[2025-07-03 05:11:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 05:11:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 05:11:01] local.INFO: Cron job executed {"time":"2025-07-03 05:11:01"} 
[2025-07-03 05:11:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 05:11:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 05:11:03] local.INFO: Cron job executed {"time":"2025-07-03 05:11:03"} 
[2025-07-03 05:12:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 05:12:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 05:12:01] local.INFO: Cron job executed {"time":"2025-07-03 05:12:01"} 
[2025-07-03 05:12:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 05:12:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 05:12:03] local.INFO: Cron job executed {"time":"2025-07-03 05:12:03"} 
[2025-07-03 05:13:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 05:13:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 05:13:01] local.INFO: Cron job executed {"time":"2025-07-03 05:13:01"} 
[2025-07-03 05:13:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 05:13:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 05:13:02] local.INFO: Cron job executed {"time":"2025-07-03 05:13:02"} 
[2025-07-03 05:14:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 05:14:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 05:14:01] local.INFO: Cron job executed {"time":"2025-07-03 05:14:01"} 
[2025-07-03 05:14:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 05:14:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 05:14:02] local.INFO: Cron job executed {"time":"2025-07-03 05:14:02"} 
[2025-07-03 05:15:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 05:15:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 05:15:01] local.INFO: Cron job executed {"time":"2025-07-03 05:15:01"} 
[2025-07-03 05:15:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 05:15:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 05:15:02] local.INFO: Cron job executed {"time":"2025-07-03 05:15:02"} 
[2025-07-03 05:16:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 05:16:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 05:16:01] local.INFO: Cron job executed {"time":"2025-07-03 05:16:01"} 
[2025-07-03 05:16:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 05:16:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 05:16:02] local.INFO: Cron job executed {"time":"2025-07-03 05:16:02"} 
[2025-07-03 05:17:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 05:17:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 05:17:01] local.INFO: Cron job executed {"time":"2025-07-03 05:17:01"} 
[2025-07-03 05:17:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 05:17:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 05:17:02] local.INFO: Cron job executed {"time":"2025-07-03 05:17:02"} 
[2025-07-03 05:18:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 05:18:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 05:18:01] local.INFO: Cron job executed {"time":"2025-07-03 05:18:01"} 
[2025-07-03 05:18:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 05:18:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 05:18:02] local.INFO: Cron job executed {"time":"2025-07-03 05:18:02"} 
[2025-07-03 05:19:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 05:19:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 05:19:01] local.INFO: Cron job executed {"time":"2025-07-03 05:19:01"} 
[2025-07-03 05:19:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 05:19:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 05:19:03] local.INFO: Cron job executed {"time":"2025-07-03 05:19:03"} 
[2025-07-03 05:20:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 05:20:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 05:20:01] local.INFO: Cron job executed {"time":"2025-07-03 05:20:01"} 
[2025-07-03 05:20:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 05:20:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 05:20:02] local.INFO: Cron job executed {"time":"2025-07-03 05:20:02"} 
[2025-07-03 05:21:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 05:21:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 05:21:01] local.INFO: Cron job executed {"time":"2025-07-03 05:21:01"} 
[2025-07-03 05:21:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 05:21:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 05:21:02] local.INFO: Cron job executed {"time":"2025-07-03 05:21:02"} 
[2025-07-03 05:22:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 05:22:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 05:22:01] local.INFO: Cron job executed {"time":"2025-07-03 05:22:01"} 
[2025-07-03 05:22:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 05:22:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 05:22:03] local.INFO: Cron job executed {"time":"2025-07-03 05:22:03"} 
[2025-07-03 05:23:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 05:23:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 05:23:01] local.INFO: Cron job executed {"time":"2025-07-03 05:23:01"} 
[2025-07-03 05:23:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 05:23:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 05:23:02] local.INFO: Cron job executed {"time":"2025-07-03 05:23:02"} 
[2025-07-03 05:24:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 05:24:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 05:24:01] local.INFO: Cron job executed {"time":"2025-07-03 05:24:01"} 
[2025-07-03 05:24:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 05:24:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 05:24:02] local.INFO: Cron job executed {"time":"2025-07-03 05:24:02"} 
[2025-07-03 05:25:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 05:25:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 05:25:01] local.INFO: Cron job executed {"time":"2025-07-03 05:25:01"} 
[2025-07-03 05:25:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 05:25:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 05:25:02] local.INFO: Cron job executed {"time":"2025-07-03 05:25:02"} 
[2025-07-03 05:26:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 05:26:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 05:26:01] local.INFO: Cron job executed {"time":"2025-07-03 05:26:01"} 
[2025-07-03 05:26:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 05:26:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 05:26:03] local.INFO: Cron job executed {"time":"2025-07-03 05:26:03"} 
[2025-07-03 05:27:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 05:27:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 05:27:01] local.INFO: Cron job executed {"time":"2025-07-03 05:27:01"} 
[2025-07-03 05:27:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 05:27:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 05:27:02] local.INFO: Cron job executed {"time":"2025-07-03 05:27:02"} 
[2025-07-03 05:28:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 05:28:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 05:28:01] local.INFO: Cron job executed {"time":"2025-07-03 05:28:01"} 
[2025-07-03 05:28:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 05:28:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 05:28:02] local.INFO: Cron job executed {"time":"2025-07-03 05:28:02"} 
[2025-07-03 05:29:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 05:29:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 05:29:01] local.INFO: Cron job executed {"time":"2025-07-03 05:29:01"} 
[2025-07-03 05:29:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 05:29:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 05:29:03] local.INFO: Cron job executed {"time":"2025-07-03 05:29:03"} 
[2025-07-03 05:30:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 05:30:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 05:30:01] local.INFO: Cron job executed {"time":"2025-07-03 05:30:01"} 
[2025-07-03 05:30:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 05:30:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 05:30:02] local.INFO: Cron job executed {"time":"2025-07-03 05:30:02"} 
[2025-07-03 05:31:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 05:31:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 05:31:01] local.INFO: Cron job executed {"time":"2025-07-03 05:31:01"} 
[2025-07-03 05:31:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 05:31:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 05:31:03] local.INFO: Cron job executed {"time":"2025-07-03 05:31:03"} 
[2025-07-03 05:32:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 05:32:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 05:32:01] local.INFO: Cron job executed {"time":"2025-07-03 05:32:01"} 
[2025-07-03 05:32:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 05:32:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 05:32:02] local.INFO: Cron job executed {"time":"2025-07-03 05:32:02"} 
[2025-07-03 05:33:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 05:33:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 05:33:01] local.INFO: Cron job executed {"time":"2025-07-03 05:33:01"} 
[2025-07-03 05:33:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 05:33:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 05:33:02] local.INFO: Cron job executed {"time":"2025-07-03 05:33:02"} 
[2025-07-03 05:34:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 05:34:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 05:34:01] local.INFO: Cron job executed {"time":"2025-07-03 05:34:01"} 
[2025-07-03 05:34:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 05:34:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 05:34:02] local.INFO: Cron job executed {"time":"2025-07-03 05:34:02"} 
[2025-07-03 05:35:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 05:35:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 05:35:01] local.INFO: Cron job executed {"time":"2025-07-03 05:35:01"} 
[2025-07-03 05:35:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 05:35:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 05:35:02] local.INFO: Cron job executed {"time":"2025-07-03 05:35:02"} 
[2025-07-03 05:36:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 05:36:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 05:36:01] local.INFO: Cron job executed {"time":"2025-07-03 05:36:01"} 
[2025-07-03 05:36:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 05:36:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 05:36:02] local.INFO: Cron job executed {"time":"2025-07-03 05:36:02"} 
[2025-07-03 05:37:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 05:37:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 05:37:01] local.INFO: Cron job executed {"time":"2025-07-03 05:37:01"} 
[2025-07-03 05:37:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 05:37:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 05:37:02] local.INFO: Cron job executed {"time":"2025-07-03 05:37:02"} 
[2025-07-03 05:38:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 05:38:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 05:38:01] local.INFO: Cron job executed {"time":"2025-07-03 05:38:01"} 
[2025-07-03 05:38:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 05:38:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 05:38:02] local.INFO: Cron job executed {"time":"2025-07-03 05:38:02"} 
[2025-07-03 05:39:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 05:39:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 05:39:01] local.INFO: Cron job executed {"time":"2025-07-03 05:39:01"} 
[2025-07-03 05:39:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 05:39:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 05:39:02] local.INFO: Cron job executed {"time":"2025-07-03 05:39:02"} 
[2025-07-03 05:40:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 05:40:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 05:40:01] local.INFO: Cron job executed {"time":"2025-07-03 05:40:01"} 
[2025-07-03 05:40:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 05:40:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 05:40:02] local.INFO: Cron job executed {"time":"2025-07-03 05:40:02"} 
[2025-07-03 05:41:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 05:41:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 05:41:01] local.INFO: Cron job executed {"time":"2025-07-03 05:41:01"} 
[2025-07-03 05:41:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 05:41:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 05:41:02] local.INFO: Cron job executed {"time":"2025-07-03 05:41:02"} 
[2025-07-03 05:42:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 05:42:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 05:42:01] local.INFO: Cron job executed {"time":"2025-07-03 05:42:01"} 
[2025-07-03 05:42:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 05:42:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 05:42:02] local.INFO: Cron job executed {"time":"2025-07-03 05:42:02"} 
[2025-07-03 05:43:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 05:43:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 05:43:01] local.INFO: Cron job executed {"time":"2025-07-03 05:43:01"} 
[2025-07-03 05:43:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 05:43:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 05:43:02] local.INFO: Cron job executed {"time":"2025-07-03 05:43:02"} 
[2025-07-03 05:44:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 05:44:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 05:44:01] local.INFO: Cron job executed {"time":"2025-07-03 05:44:01"} 
[2025-07-03 05:44:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 05:44:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 05:44:02] local.INFO: Cron job executed {"time":"2025-07-03 05:44:02"} 
[2025-07-03 05:45:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 05:45:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 05:45:01] local.INFO: Cron job executed {"time":"2025-07-03 05:45:01"} 
[2025-07-03 05:45:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 05:45:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 05:45:03] local.INFO: Cron job executed {"time":"2025-07-03 05:45:03"} 
[2025-07-03 05:46:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 05:46:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 05:46:01] local.INFO: Cron job executed {"time":"2025-07-03 05:46:01"} 
[2025-07-03 05:46:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 05:46:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 05:46:02] local.INFO: Cron job executed {"time":"2025-07-03 05:46:02"} 
[2025-07-03 05:47:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 05:47:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 05:47:01] local.INFO: Cron job executed {"time":"2025-07-03 05:47:01"} 
[2025-07-03 05:47:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 05:47:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 05:47:02] local.INFO: Cron job executed {"time":"2025-07-03 05:47:02"} 
[2025-07-03 05:48:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 05:48:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 05:48:01] local.INFO: Cron job executed {"time":"2025-07-03 05:48:01"} 
[2025-07-03 05:48:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 05:48:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 05:48:02] local.INFO: Cron job executed {"time":"2025-07-03 05:48:02"} 
[2025-07-03 05:49:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 05:49:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 05:49:01] local.INFO: Cron job executed {"time":"2025-07-03 05:49:01"} 
[2025-07-03 05:49:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 05:49:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 05:49:02] local.INFO: Cron job executed {"time":"2025-07-03 05:49:02"} 
[2025-07-03 05:50:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 05:50:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 05:50:01] local.INFO: Cron job executed {"time":"2025-07-03 05:50:01"} 
[2025-07-03 05:50:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 05:50:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 05:50:02] local.INFO: Cron job executed {"time":"2025-07-03 05:50:02"} 
[2025-07-03 05:51:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 05:51:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 05:51:01] local.INFO: Cron job executed {"time":"2025-07-03 05:51:01"} 
[2025-07-03 05:51:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 05:51:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 05:51:02] local.INFO: Cron job executed {"time":"2025-07-03 05:51:02"} 
[2025-07-03 05:52:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 05:52:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 05:52:01] local.INFO: Cron job executed {"time":"2025-07-03 05:52:01"} 
[2025-07-03 05:52:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 05:52:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 05:52:02] local.INFO: Cron job executed {"time":"2025-07-03 05:52:02"} 
[2025-07-03 05:53:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 05:53:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 05:53:01] local.INFO: Cron job executed {"time":"2025-07-03 05:53:01"} 
[2025-07-03 05:53:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 05:53:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 05:53:02] local.INFO: Cron job executed {"time":"2025-07-03 05:53:02"} 
[2025-07-03 05:54:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 05:54:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 05:54:01] local.INFO: Cron job executed {"time":"2025-07-03 05:54:01"} 
[2025-07-03 05:54:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 05:54:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 05:54:02] local.INFO: Cron job executed {"time":"2025-07-03 05:54:02"} 
[2025-07-03 05:55:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 05:55:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 05:55:01] local.INFO: Cron job executed {"time":"2025-07-03 05:55:01"} 
[2025-07-03 05:55:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 05:55:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 05:55:02] local.INFO: Cron job executed {"time":"2025-07-03 05:55:02"} 
[2025-07-03 05:56:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 05:56:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 05:56:01] local.INFO: Cron job executed {"time":"2025-07-03 05:56:01"} 
[2025-07-03 05:56:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 05:56:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 05:56:02] local.INFO: Cron job executed {"time":"2025-07-03 05:56:02"} 
[2025-07-03 05:57:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 05:57:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 05:57:01] local.INFO: Cron job executed {"time":"2025-07-03 05:57:01"} 
[2025-07-03 05:57:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 05:57:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 05:57:02] local.INFO: Cron job executed {"time":"2025-07-03 05:57:02"} 
[2025-07-03 05:58:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 05:58:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 05:58:01] local.INFO: Cron job executed {"time":"2025-07-03 05:58:01"} 
[2025-07-03 05:58:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 05:58:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 05:58:02] local.INFO: Cron job executed {"time":"2025-07-03 05:58:02"} 
[2025-07-03 05:59:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 05:59:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 05:59:01] local.INFO: Cron job executed {"time":"2025-07-03 05:59:01"} 
[2025-07-03 05:59:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 05:59:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 05:59:03] local.INFO: Cron job executed {"time":"2025-07-03 05:59:03"} 
[2025-07-03 06:00:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 06:00:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 06:00:01] local.INFO: Cron job executed {"time":"2025-07-03 06:00:01"} 
[2025-07-03 06:00:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 06:00:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 06:00:02] local.INFO: Cron job executed {"time":"2025-07-03 06:00:02"} 
[2025-07-03 06:01:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 06:01:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 06:01:01] local.INFO: Cron job executed {"time":"2025-07-03 06:01:01"} 
[2025-07-03 06:01:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 06:01:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 06:01:03] local.INFO: Cron job executed {"time":"2025-07-03 06:01:03"} 
[2025-07-03 06:02:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 06:02:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 06:02:01] local.INFO: Cron job executed {"time":"2025-07-03 06:02:01"} 
[2025-07-03 06:02:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 06:02:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 06:02:03] local.INFO: Cron job executed {"time":"2025-07-03 06:02:03"} 
[2025-07-03 06:03:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 06:03:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 06:03:01] local.INFO: Cron job executed {"time":"2025-07-03 06:03:01"} 
[2025-07-03 06:03:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 06:03:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 06:03:02] local.INFO: Cron job executed {"time":"2025-07-03 06:03:02"} 
[2025-07-03 06:04:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 06:04:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 06:04:01] local.INFO: Cron job executed {"time":"2025-07-03 06:04:01"} 
[2025-07-03 06:04:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 06:04:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 06:04:02] local.INFO: Cron job executed {"time":"2025-07-03 06:04:02"} 
[2025-07-03 06:05:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 06:05:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 06:05:01] local.INFO: Cron job executed {"time":"2025-07-03 06:05:01"} 
[2025-07-03 06:05:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 06:05:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 06:05:02] local.INFO: Cron job executed {"time":"2025-07-03 06:05:02"} 
[2025-07-03 06:06:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 06:06:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 06:06:01] local.INFO: Cron job executed {"time":"2025-07-03 06:06:01"} 
[2025-07-03 06:06:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 06:06:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 06:06:03] local.INFO: Cron job executed {"time":"2025-07-03 06:06:03"} 
[2025-07-03 06:07:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 06:07:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 06:07:01] local.INFO: Cron job executed {"time":"2025-07-03 06:07:01"} 
[2025-07-03 06:07:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 06:07:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 06:07:02] local.INFO: Cron job executed {"time":"2025-07-03 06:07:02"} 
[2025-07-03 06:08:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 06:08:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 06:08:01] local.INFO: Cron job executed {"time":"2025-07-03 06:08:01"} 
[2025-07-03 06:08:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 06:08:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 06:08:02] local.INFO: Cron job executed {"time":"2025-07-03 06:08:02"} 
[2025-07-03 06:09:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 06:09:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 06:09:01] local.INFO: Cron job executed {"time":"2025-07-03 06:09:01"} 
[2025-07-03 06:09:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 06:09:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 06:09:02] local.INFO: Cron job executed {"time":"2025-07-03 06:09:02"} 
[2025-07-03 06:10:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 06:10:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 06:10:01] local.INFO: Cron job executed {"time":"2025-07-03 06:10:01"} 
[2025-07-03 06:10:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 06:10:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 06:10:02] local.INFO: Cron job executed {"time":"2025-07-03 06:10:02"} 
[2025-07-03 06:11:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 06:11:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 06:11:01] local.INFO: Cron job executed {"time":"2025-07-03 06:11:01"} 
[2025-07-03 06:11:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 06:11:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 06:11:02] local.INFO: Cron job executed {"time":"2025-07-03 06:11:02"} 
[2025-07-03 06:12:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 06:12:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 06:12:01] local.INFO: Cron job executed {"time":"2025-07-03 06:12:01"} 
[2025-07-03 06:12:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 06:12:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 06:12:02] local.INFO: Cron job executed {"time":"2025-07-03 06:12:02"} 
[2025-07-03 06:13:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 06:13:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 06:13:01] local.INFO: Cron job executed {"time":"2025-07-03 06:13:01"} 
[2025-07-03 06:13:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 06:13:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 06:13:02] local.INFO: Cron job executed {"time":"2025-07-03 06:13:02"} 
[2025-07-03 06:14:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 06:14:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 06:14:01] local.INFO: Cron job executed {"time":"2025-07-03 06:14:01"} 
[2025-07-03 06:14:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 06:14:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 06:14:02] local.INFO: Cron job executed {"time":"2025-07-03 06:14:02"} 
[2025-07-03 06:15:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 06:15:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 06:15:01] local.INFO: Cron job executed {"time":"2025-07-03 06:15:01"} 
[2025-07-03 06:15:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 06:15:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 06:15:02] local.INFO: Cron job executed {"time":"2025-07-03 06:15:02"} 
[2025-07-03 06:16:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 06:16:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 06:16:01] local.INFO: Cron job executed {"time":"2025-07-03 06:16:01"} 
[2025-07-03 06:16:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 06:16:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 06:16:02] local.INFO: Cron job executed {"time":"2025-07-03 06:16:02"} 
[2025-07-03 06:17:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 06:17:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 06:17:01] local.INFO: Cron job executed {"time":"2025-07-03 06:17:01"} 
[2025-07-03 06:17:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 06:17:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 06:17:02] local.INFO: Cron job executed {"time":"2025-07-03 06:17:02"} 
[2025-07-03 06:18:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 06:18:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 06:18:01] local.INFO: Cron job executed {"time":"2025-07-03 06:18:01"} 
[2025-07-03 06:18:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 06:18:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 06:18:02] local.INFO: Cron job executed {"time":"2025-07-03 06:18:02"} 
[2025-07-03 06:19:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 06:19:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 06:19:01] local.INFO: Cron job executed {"time":"2025-07-03 06:19:01"} 
[2025-07-03 06:19:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 06:19:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 06:19:02] local.INFO: Cron job executed {"time":"2025-07-03 06:19:02"} 
[2025-07-03 06:20:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 06:20:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 06:20:01] local.INFO: Cron job executed {"time":"2025-07-03 06:20:01"} 
[2025-07-03 06:20:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 06:20:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 06:20:02] local.INFO: Cron job executed {"time":"2025-07-03 06:20:02"} 
[2025-07-03 06:21:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 06:21:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 06:21:01] local.INFO: Cron job executed {"time":"2025-07-03 06:21:01"} 
[2025-07-03 06:21:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 06:21:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 06:21:03] local.INFO: Cron job executed {"time":"2025-07-03 06:21:03"} 
[2025-07-03 06:22:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 06:22:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 06:22:01] local.INFO: Cron job executed {"time":"2025-07-03 06:22:01"} 
[2025-07-03 06:22:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 06:22:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 06:22:02] local.INFO: Cron job executed {"time":"2025-07-03 06:22:02"} 
[2025-07-03 06:23:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 06:23:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 06:23:01] local.INFO: Cron job executed {"time":"2025-07-03 06:23:01"} 
[2025-07-03 06:23:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 06:23:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 06:23:02] local.INFO: Cron job executed {"time":"2025-07-03 06:23:02"} 
[2025-07-03 06:24:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 06:24:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 06:24:01] local.INFO: Cron job executed {"time":"2025-07-03 06:24:01"} 
[2025-07-03 06:24:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 06:24:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 06:24:02] local.INFO: Cron job executed {"time":"2025-07-03 06:24:02"} 
[2025-07-03 06:25:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 06:25:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 06:25:01] local.INFO: Cron job executed {"time":"2025-07-03 06:25:01"} 
[2025-07-03 06:25:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 06:25:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 06:25:03] local.INFO: Cron job executed {"time":"2025-07-03 06:25:03"} 
[2025-07-03 06:26:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 06:26:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 06:26:01] local.INFO: Cron job executed {"time":"2025-07-03 06:26:01"} 
[2025-07-03 06:26:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 06:26:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 06:26:02] local.INFO: Cron job executed {"time":"2025-07-03 06:26:02"} 
[2025-07-03 06:27:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 06:27:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 06:27:01] local.INFO: Cron job executed {"time":"2025-07-03 06:27:01"} 
[2025-07-03 06:27:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 06:27:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 06:27:02] local.INFO: Cron job executed {"time":"2025-07-03 06:27:02"} 
[2025-07-03 06:28:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 06:28:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 06:28:01] local.INFO: Cron job executed {"time":"2025-07-03 06:28:01"} 
[2025-07-03 06:28:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 06:28:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 06:28:02] local.INFO: Cron job executed {"time":"2025-07-03 06:28:02"} 
[2025-07-03 06:29:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 06:29:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 06:29:01] local.INFO: Cron job executed {"time":"2025-07-03 06:29:01"} 
[2025-07-03 06:29:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 06:29:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 06:29:02] local.INFO: Cron job executed {"time":"2025-07-03 06:29:02"} 
[2025-07-03 06:30:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 06:30:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 06:30:01] local.INFO: Cron job executed {"time":"2025-07-03 06:30:01"} 
[2025-07-03 06:30:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 06:30:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 06:30:02] local.INFO: Cron job executed {"time":"2025-07-03 06:30:02"} 
[2025-07-03 06:31:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 06:31:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 06:31:01] local.INFO: Cron job executed {"time":"2025-07-03 06:31:01"} 
[2025-07-03 06:31:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 06:31:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 06:31:02] local.INFO: Cron job executed {"time":"2025-07-03 06:31:02"} 
[2025-07-03 06:32:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 06:32:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 06:32:01] local.INFO: Cron job executed {"time":"2025-07-03 06:32:01"} 
[2025-07-03 06:32:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 06:32:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 06:32:02] local.INFO: Cron job executed {"time":"2025-07-03 06:32:02"} 
[2025-07-03 06:33:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 06:33:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 06:33:01] local.INFO: Cron job executed {"time":"2025-07-03 06:33:01"} 
[2025-07-03 06:33:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 06:33:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 06:33:02] local.INFO: Cron job executed {"time":"2025-07-03 06:33:02"} 
[2025-07-03 06:34:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 06:34:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 06:34:01] local.INFO: Cron job executed {"time":"2025-07-03 06:34:01"} 
[2025-07-03 06:34:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 06:34:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 06:34:02] local.INFO: Cron job executed {"time":"2025-07-03 06:34:02"} 
[2025-07-03 06:35:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 06:35:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 06:35:01] local.INFO: Cron job executed {"time":"2025-07-03 06:35:01"} 
[2025-07-03 06:35:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 06:35:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 06:35:02] local.INFO: Cron job executed {"time":"2025-07-03 06:35:02"} 
[2025-07-03 06:36:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 06:36:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 06:36:01] local.INFO: Cron job executed {"time":"2025-07-03 06:36:01"} 
[2025-07-03 06:36:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 06:36:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 06:36:02] local.INFO: Cron job executed {"time":"2025-07-03 06:36:02"} 
[2025-07-03 06:37:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 06:37:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 06:37:01] local.INFO: Cron job executed {"time":"2025-07-03 06:37:01"} 
[2025-07-03 06:37:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 06:37:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 06:37:02] local.INFO: Cron job executed {"time":"2025-07-03 06:37:02"} 
[2025-07-03 06:38:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 06:38:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 06:38:01] local.INFO: Cron job executed {"time":"2025-07-03 06:38:01"} 
[2025-07-03 06:38:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 06:38:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 06:38:02] local.INFO: Cron job executed {"time":"2025-07-03 06:38:02"} 
[2025-07-03 06:39:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 06:39:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 06:39:01] local.INFO: Cron job executed {"time":"2025-07-03 06:39:01"} 
[2025-07-03 06:39:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 06:39:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 06:39:02] local.INFO: Cron job executed {"time":"2025-07-03 06:39:02"} 
[2025-07-03 06:40:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 06:40:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 06:40:01] local.INFO: Cron job executed {"time":"2025-07-03 06:40:01"} 
[2025-07-03 06:40:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 06:40:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 06:40:03] local.INFO: Cron job executed {"time":"2025-07-03 06:40:03"} 
[2025-07-03 06:41:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 06:41:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 06:41:01] local.INFO: Cron job executed {"time":"2025-07-03 06:41:01"} 
[2025-07-03 06:41:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 06:41:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 06:41:02] local.INFO: Cron job executed {"time":"2025-07-03 06:41:02"} 
[2025-07-03 06:42:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 06:42:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 06:42:01] local.INFO: Cron job executed {"time":"2025-07-03 06:42:01"} 
[2025-07-03 06:42:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 06:42:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 06:42:02] local.INFO: Cron job executed {"time":"2025-07-03 06:42:02"} 
[2025-07-03 06:43:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 06:43:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 06:43:01] local.INFO: Cron job executed {"time":"2025-07-03 06:43:01"} 
[2025-07-03 06:43:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 06:43:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 06:43:02] local.INFO: Cron job executed {"time":"2025-07-03 06:43:02"} 
[2025-07-03 06:44:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 06:44:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 06:44:01] local.INFO: Cron job executed {"time":"2025-07-03 06:44:01"} 
[2025-07-03 06:44:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 06:44:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 06:44:02] local.INFO: Cron job executed {"time":"2025-07-03 06:44:02"} 
[2025-07-03 06:45:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 06:45:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 06:45:01] local.INFO: Cron job executed {"time":"2025-07-03 06:45:01"} 
[2025-07-03 06:45:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 06:45:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 06:45:02] local.INFO: Cron job executed {"time":"2025-07-03 06:45:02"} 
[2025-07-03 06:46:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 06:46:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 06:46:01] local.INFO: Cron job executed {"time":"2025-07-03 06:46:01"} 
[2025-07-03 06:46:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 06:46:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 06:46:02] local.INFO: Cron job executed {"time":"2025-07-03 06:46:02"} 
[2025-07-03 06:47:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 06:47:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 06:47:01] local.INFO: Cron job executed {"time":"2025-07-03 06:47:01"} 
[2025-07-03 06:47:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 06:47:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 06:47:02] local.INFO: Cron job executed {"time":"2025-07-03 06:47:02"} 
[2025-07-03 06:48:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 06:48:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 06:48:01] local.INFO: Cron job executed {"time":"2025-07-03 06:48:01"} 
[2025-07-03 06:48:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 06:48:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 06:48:02] local.INFO: Cron job executed {"time":"2025-07-03 06:48:02"} 
[2025-07-03 06:49:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 06:49:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 06:49:01] local.INFO: Cron job executed {"time":"2025-07-03 06:49:01"} 
[2025-07-03 06:49:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 06:49:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 06:49:02] local.INFO: Cron job executed {"time":"2025-07-03 06:49:02"} 
[2025-07-03 06:50:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 06:50:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 06:50:01] local.INFO: Cron job executed {"time":"2025-07-03 06:50:01"} 
[2025-07-03 06:50:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 06:50:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 06:50:02] local.INFO: Cron job executed {"time":"2025-07-03 06:50:02"} 
[2025-07-03 06:51:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 06:51:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 06:51:01] local.INFO: Cron job executed {"time":"2025-07-03 06:51:01"} 
[2025-07-03 06:51:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 06:51:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 06:51:02] local.INFO: Cron job executed {"time":"2025-07-03 06:51:02"} 
[2025-07-03 06:52:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 06:52:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 06:52:01] local.INFO: Cron job executed {"time":"2025-07-03 06:52:01"} 
[2025-07-03 06:52:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 06:52:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 06:52:02] local.INFO: Cron job executed {"time":"2025-07-03 06:52:02"} 
[2025-07-03 06:53:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 06:53:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 06:53:01] local.INFO: Cron job executed {"time":"2025-07-03 06:53:01"} 
[2025-07-03 06:53:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 06:53:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 06:53:02] local.INFO: Cron job executed {"time":"2025-07-03 06:53:02"} 
[2025-07-03 06:54:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 06:54:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 06:54:01] local.INFO: Cron job executed {"time":"2025-07-03 06:54:01"} 
[2025-07-03 06:54:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 06:54:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 06:54:02] local.INFO: Cron job executed {"time":"2025-07-03 06:54:02"} 
[2025-07-03 06:55:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 06:55:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 06:55:01] local.INFO: Cron job executed {"time":"2025-07-03 06:55:01"} 
[2025-07-03 06:55:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 06:55:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 06:55:02] local.INFO: Cron job executed {"time":"2025-07-03 06:55:02"} 
[2025-07-03 06:56:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 06:56:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 06:56:01] local.INFO: Cron job executed {"time":"2025-07-03 06:56:01"} 
[2025-07-03 06:56:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 06:56:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 06:56:02] local.INFO: Cron job executed {"time":"2025-07-03 06:56:02"} 
[2025-07-03 06:57:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 06:57:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 06:57:01] local.INFO: Cron job executed {"time":"2025-07-03 06:57:01"} 
[2025-07-03 06:57:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 06:57:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 06:57:02] local.INFO: Cron job executed {"time":"2025-07-03 06:57:02"} 
[2025-07-03 06:58:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 06:58:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 06:58:01] local.INFO: Cron job executed {"time":"2025-07-03 06:58:01"} 
[2025-07-03 06:58:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 06:58:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 06:58:02] local.INFO: Cron job executed {"time":"2025-07-03 06:58:02"} 
[2025-07-03 06:59:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 06:59:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 06:59:01] local.INFO: Cron job executed {"time":"2025-07-03 06:59:01"} 
[2025-07-03 06:59:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 06:59:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 06:59:02] local.INFO: Cron job executed {"time":"2025-07-03 06:59:02"} 
[2025-07-03 07:00:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 07:00:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 07:00:01] local.INFO: Cron job executed {"time":"2025-07-03 07:00:01"} 
[2025-07-03 07:00:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 07:00:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 07:00:02] local.INFO: Cron job executed {"time":"2025-07-03 07:00:02"} 
[2025-07-03 07:01:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 07:01:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 07:01:01] local.INFO: Cron job executed {"time":"2025-07-03 07:01:01"} 
[2025-07-03 07:01:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 07:01:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 07:01:02] local.INFO: Cron job executed {"time":"2025-07-03 07:01:02"} 
[2025-07-03 07:02:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 07:02:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 07:02:01] local.INFO: Cron job executed {"time":"2025-07-03 07:02:01"} 
[2025-07-03 07:02:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 07:02:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 07:02:02] local.INFO: Cron job executed {"time":"2025-07-03 07:02:02"} 
[2025-07-03 07:03:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 07:03:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 07:03:01] local.INFO: Cron job executed {"time":"2025-07-03 07:03:01"} 
[2025-07-03 07:03:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 07:03:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 07:03:03] local.INFO: Cron job executed {"time":"2025-07-03 07:03:03"} 
[2025-07-03 07:04:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 07:04:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 07:04:01] local.INFO: Cron job executed {"time":"2025-07-03 07:04:01"} 
[2025-07-03 07:04:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 07:04:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 07:04:03] local.INFO: Cron job executed {"time":"2025-07-03 07:04:03"} 
[2025-07-03 07:05:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 07:05:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 07:05:01] local.INFO: Cron job executed {"time":"2025-07-03 07:05:01"} 
[2025-07-03 07:05:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 07:05:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 07:05:02] local.INFO: Cron job executed {"time":"2025-07-03 07:05:02"} 
[2025-07-03 07:06:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 07:06:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 07:06:01] local.INFO: Cron job executed {"time":"2025-07-03 07:06:01"} 
[2025-07-03 07:06:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 07:06:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 07:06:02] local.INFO: Cron job executed {"time":"2025-07-03 07:06:02"} 
[2025-07-03 07:07:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 07:07:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 07:07:01] local.INFO: Cron job executed {"time":"2025-07-03 07:07:01"} 
[2025-07-03 07:07:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 07:07:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 07:07:02] local.INFO: Cron job executed {"time":"2025-07-03 07:07:02"} 
[2025-07-03 07:08:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 07:08:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 07:08:01] local.INFO: Cron job executed {"time":"2025-07-03 07:08:01"} 
[2025-07-03 07:08:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 07:08:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 07:08:02] local.INFO: Cron job executed {"time":"2025-07-03 07:08:02"} 
[2025-07-03 07:09:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 07:09:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 07:09:01] local.INFO: Cron job executed {"time":"2025-07-03 07:09:01"} 
[2025-07-03 07:09:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 07:09:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 07:09:02] local.INFO: Cron job executed {"time":"2025-07-03 07:09:02"} 
[2025-07-03 07:10:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 07:10:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 07:10:01] local.INFO: Cron job executed {"time":"2025-07-03 07:10:01"} 
[2025-07-03 07:10:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 07:10:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 07:10:02] local.INFO: Cron job executed {"time":"2025-07-03 07:10:02"} 
[2025-07-03 07:11:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 07:11:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 07:11:01] local.INFO: Cron job executed {"time":"2025-07-03 07:11:01"} 
[2025-07-03 07:11:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 07:11:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 07:11:02] local.INFO: Cron job executed {"time":"2025-07-03 07:11:02"} 
[2025-07-03 07:12:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 07:12:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 07:12:01] local.INFO: Cron job executed {"time":"2025-07-03 07:12:01"} 
[2025-07-03 07:12:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 07:12:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 07:12:02] local.INFO: Cron job executed {"time":"2025-07-03 07:12:02"} 
[2025-07-03 07:13:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 07:13:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 07:13:01] local.INFO: Cron job executed {"time":"2025-07-03 07:13:01"} 
[2025-07-03 07:13:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 07:13:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 07:13:02] local.INFO: Cron job executed {"time":"2025-07-03 07:13:02"} 
[2025-07-03 07:14:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 07:14:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 07:14:01] local.INFO: Cron job executed {"time":"2025-07-03 07:14:01"} 
[2025-07-03 07:14:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 07:14:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 07:14:02] local.INFO: Cron job executed {"time":"2025-07-03 07:14:02"} 
[2025-07-03 07:15:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 07:15:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 07:15:01] local.INFO: Cron job executed {"time":"2025-07-03 07:15:01"} 
[2025-07-03 07:15:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 07:15:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 07:15:02] local.INFO: Cron job executed {"time":"2025-07-03 07:15:02"} 
[2025-07-03 07:16:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 07:16:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 07:16:01] local.INFO: Cron job executed {"time":"2025-07-03 07:16:01"} 
[2025-07-03 07:16:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 07:16:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 07:16:02] local.INFO: Cron job executed {"time":"2025-07-03 07:16:02"} 
[2025-07-03 07:17:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 07:17:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 07:17:01] local.INFO: Cron job executed {"time":"2025-07-03 07:17:01"} 
[2025-07-03 07:17:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 07:17:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 07:17:03] local.INFO: Cron job executed {"time":"2025-07-03 07:17:03"} 
[2025-07-03 07:18:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 07:18:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 07:18:01] local.INFO: Cron job executed {"time":"2025-07-03 07:18:01"} 
[2025-07-03 07:18:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 07:18:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 07:18:02] local.INFO: Cron job executed {"time":"2025-07-03 07:18:02"} 
[2025-07-03 07:19:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 07:19:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 07:19:01] local.INFO: Cron job executed {"time":"2025-07-03 07:19:01"} 
[2025-07-03 07:19:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 07:19:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 07:19:02] local.INFO: Cron job executed {"time":"2025-07-03 07:19:02"} 
[2025-07-03 07:20:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 07:20:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 07:20:01] local.INFO: Cron job executed {"time":"2025-07-03 07:20:01"} 
[2025-07-03 07:20:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 07:20:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 07:20:02] local.INFO: Cron job executed {"time":"2025-07-03 07:20:02"} 
[2025-07-03 07:21:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 07:21:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 07:21:01] local.INFO: Cron job executed {"time":"2025-07-03 07:21:01"} 
[2025-07-03 07:21:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 07:21:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 07:21:02] local.INFO: Cron job executed {"time":"2025-07-03 07:21:02"} 
[2025-07-03 07:22:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 07:22:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 07:22:01] local.INFO: Cron job executed {"time":"2025-07-03 07:22:01"} 
[2025-07-03 07:22:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 07:22:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 07:22:03] local.INFO: Cron job executed {"time":"2025-07-03 07:22:03"} 
[2025-07-03 07:23:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 07:23:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 07:23:01] local.INFO: Cron job executed {"time":"2025-07-03 07:23:01"} 
[2025-07-03 07:23:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 07:23:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 07:23:03] local.INFO: Cron job executed {"time":"2025-07-03 07:23:03"} 
[2025-07-03 07:24:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 07:24:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 07:24:01] local.INFO: Cron job executed {"time":"2025-07-03 07:24:01"} 
[2025-07-03 07:24:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 07:24:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 07:24:02] local.INFO: Cron job executed {"time":"2025-07-03 07:24:02"} 
[2025-07-03 07:25:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 07:25:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 07:25:01] local.INFO: Cron job executed {"time":"2025-07-03 07:25:01"} 
[2025-07-03 07:25:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 07:25:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 07:25:03] local.INFO: Cron job executed {"time":"2025-07-03 07:25:03"} 
[2025-07-03 07:26:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 07:26:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 07:26:01] local.INFO: Cron job executed {"time":"2025-07-03 07:26:01"} 
[2025-07-03 07:26:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 07:26:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 07:26:02] local.INFO: Cron job executed {"time":"2025-07-03 07:26:02"} 
[2025-07-03 07:27:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 07:27:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 07:27:01] local.INFO: Cron job executed {"time":"2025-07-03 07:27:01"} 
[2025-07-03 07:27:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 07:27:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 07:27:02] local.INFO: Cron job executed {"time":"2025-07-03 07:27:02"} 
[2025-07-03 07:28:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 07:28:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 07:28:01] local.INFO: Cron job executed {"time":"2025-07-03 07:28:01"} 
[2025-07-03 07:28:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 07:28:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 07:28:02] local.INFO: Cron job executed {"time":"2025-07-03 07:28:02"} 
[2025-07-03 07:29:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 07:29:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 07:29:01] local.INFO: Cron job executed {"time":"2025-07-03 07:29:01"} 
[2025-07-03 07:29:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 07:29:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 07:29:02] local.INFO: Cron job executed {"time":"2025-07-03 07:29:02"} 
[2025-07-03 07:30:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 07:30:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 07:30:01] local.INFO: Cron job executed {"time":"2025-07-03 07:30:01"} 
[2025-07-03 07:30:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 07:30:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 07:30:03] local.INFO: Cron job executed {"time":"2025-07-03 07:30:03"} 
[2025-07-03 07:31:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 07:31:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 07:31:01] local.INFO: Cron job executed {"time":"2025-07-03 07:31:01"} 
[2025-07-03 07:31:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 07:31:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 07:31:02] local.INFO: Cron job executed {"time":"2025-07-03 07:31:02"} 
[2025-07-03 07:32:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 07:32:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 07:32:01] local.INFO: Cron job executed {"time":"2025-07-03 07:32:01"} 
[2025-07-03 07:32:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 07:32:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 07:32:02] local.INFO: Cron job executed {"time":"2025-07-03 07:32:02"} 
[2025-07-03 07:33:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 07:33:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 07:33:01] local.INFO: Cron job executed {"time":"2025-07-03 07:33:01"} 
[2025-07-03 07:33:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 07:33:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 07:33:02] local.INFO: Cron job executed {"time":"2025-07-03 07:33:02"} 
[2025-07-03 07:34:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 07:34:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 07:34:01] local.INFO: Cron job executed {"time":"2025-07-03 07:34:01"} 
[2025-07-03 07:34:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 07:34:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 07:34:02] local.INFO: Cron job executed {"time":"2025-07-03 07:34:02"} 
[2025-07-03 07:35:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 07:35:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 07:35:01] local.INFO: Cron job executed {"time":"2025-07-03 07:35:01"} 
[2025-07-03 07:35:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 07:35:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 07:35:02] local.INFO: Cron job executed {"time":"2025-07-03 07:35:02"} 
[2025-07-03 07:36:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 07:36:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 07:36:01] local.INFO: Cron job executed {"time":"2025-07-03 07:36:01"} 
[2025-07-03 07:36:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 07:36:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 07:36:02] local.INFO: Cron job executed {"time":"2025-07-03 07:36:02"} 
[2025-07-03 07:37:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 07:37:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 07:37:01] local.INFO: Cron job executed {"time":"2025-07-03 07:37:01"} 
[2025-07-03 07:37:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 07:37:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 07:37:02] local.INFO: Cron job executed {"time":"2025-07-03 07:37:02"} 
[2025-07-03 07:38:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 07:38:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 07:38:01] local.INFO: Cron job executed {"time":"2025-07-03 07:38:01"} 
[2025-07-03 07:38:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 07:38:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 07:38:02] local.INFO: Cron job executed {"time":"2025-07-03 07:38:02"} 
[2025-07-03 07:39:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 07:39:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 07:39:01] local.INFO: Cron job executed {"time":"2025-07-03 07:39:01"} 
[2025-07-03 07:39:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 07:39:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 07:39:02] local.INFO: Cron job executed {"time":"2025-07-03 07:39:02"} 
[2025-07-03 07:40:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 07:40:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 07:40:01] local.INFO: Cron job executed {"time":"2025-07-03 07:40:01"} 
[2025-07-03 07:40:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 07:40:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 07:40:02] local.INFO: Cron job executed {"time":"2025-07-03 07:40:02"} 
[2025-07-03 07:41:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 07:41:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 07:41:01] local.INFO: Cron job executed {"time":"2025-07-03 07:41:01"} 
[2025-07-03 07:41:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 07:41:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 07:41:02] local.INFO: Cron job executed {"time":"2025-07-03 07:41:02"} 
[2025-07-03 07:42:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 07:42:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 07:42:01] local.INFO: Cron job executed {"time":"2025-07-03 07:42:01"} 
[2025-07-03 07:42:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 07:42:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 07:42:02] local.INFO: Cron job executed {"time":"2025-07-03 07:42:02"} 
[2025-07-03 07:43:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 07:43:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 07:43:01] local.INFO: Cron job executed {"time":"2025-07-03 07:43:01"} 
[2025-07-03 07:43:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 07:43:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 07:43:02] local.INFO: Cron job executed {"time":"2025-07-03 07:43:02"} 
[2025-07-03 07:44:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 07:44:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 07:44:01] local.INFO: Cron job executed {"time":"2025-07-03 07:44:01"} 
[2025-07-03 07:44:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 07:44:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 07:44:03] local.INFO: Cron job executed {"time":"2025-07-03 07:44:03"} 
[2025-07-03 07:45:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 07:45:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 07:45:01] local.INFO: Cron job executed {"time":"2025-07-03 07:45:01"} 
[2025-07-03 07:45:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 07:45:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 07:45:02] local.INFO: Cron job executed {"time":"2025-07-03 07:45:02"} 
[2025-07-03 07:46:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 07:46:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 07:46:01] local.INFO: Cron job executed {"time":"2025-07-03 07:46:01"} 
[2025-07-03 07:46:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 07:46:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 07:46:02] local.INFO: Cron job executed {"time":"2025-07-03 07:46:02"} 
[2025-07-03 07:47:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 07:47:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 07:47:01] local.INFO: Cron job executed {"time":"2025-07-03 07:47:01"} 
[2025-07-03 07:47:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 07:47:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 07:47:02] local.INFO: Cron job executed {"time":"2025-07-03 07:47:02"} 
[2025-07-03 07:48:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 07:48:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 07:48:01] local.INFO: Cron job executed {"time":"2025-07-03 07:48:01"} 
[2025-07-03 07:48:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 07:48:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 07:48:02] local.INFO: Cron job executed {"time":"2025-07-03 07:48:02"} 
[2025-07-03 07:49:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 07:49:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 07:49:01] local.INFO: Cron job executed {"time":"2025-07-03 07:49:01"} 
[2025-07-03 07:49:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 07:49:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 07:49:03] local.INFO: Cron job executed {"time":"2025-07-03 07:49:03"} 
[2025-07-03 07:50:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 07:50:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 07:50:01] local.INFO: Cron job executed {"time":"2025-07-03 07:50:01"} 
[2025-07-03 07:50:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 07:50:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 07:50:02] local.INFO: Cron job executed {"time":"2025-07-03 07:50:02"} 
[2025-07-03 07:51:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 07:51:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 07:51:01] local.INFO: Cron job executed {"time":"2025-07-03 07:51:01"} 
[2025-07-03 07:51:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 07:51:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 07:51:03] local.INFO: Cron job executed {"time":"2025-07-03 07:51:03"} 
[2025-07-03 07:52:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 07:52:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 07:52:01] local.INFO: Cron job executed {"time":"2025-07-03 07:52:01"} 
[2025-07-03 07:52:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 07:52:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 07:52:03] local.INFO: Cron job executed {"time":"2025-07-03 07:52:03"} 
[2025-07-03 07:53:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 07:53:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 07:53:01] local.INFO: Cron job executed {"time":"2025-07-03 07:53:01"} 
[2025-07-03 07:53:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 07:53:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 07:53:02] local.INFO: Cron job executed {"time":"2025-07-03 07:53:02"} 
[2025-07-03 07:54:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 07:54:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 07:54:01] local.INFO: Cron job executed {"time":"2025-07-03 07:54:01"} 
[2025-07-03 07:54:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 07:54:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 07:54:02] local.INFO: Cron job executed {"time":"2025-07-03 07:54:02"} 
[2025-07-03 07:55:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 07:55:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 07:55:01] local.INFO: Cron job executed {"time":"2025-07-03 07:55:01"} 
[2025-07-03 07:55:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 07:55:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 07:55:03] local.INFO: Cron job executed {"time":"2025-07-03 07:55:03"} 
[2025-07-03 07:56:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 07:56:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 07:56:01] local.INFO: Cron job executed {"time":"2025-07-03 07:56:01"} 
[2025-07-03 07:56:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 07:56:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 07:56:02] local.INFO: Cron job executed {"time":"2025-07-03 07:56:02"} 
[2025-07-03 07:57:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 07:57:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 07:57:01] local.INFO: Cron job executed {"time":"2025-07-03 07:57:01"} 
[2025-07-03 07:57:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 07:57:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 07:57:02] local.INFO: Cron job executed {"time":"2025-07-03 07:57:02"} 
[2025-07-03 07:58:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 07:58:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 07:58:01] local.INFO: Cron job executed {"time":"2025-07-03 07:58:01"} 
[2025-07-03 07:58:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 07:58:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 07:58:02] local.INFO: Cron job executed {"time":"2025-07-03 07:58:02"} 
[2025-07-03 07:59:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 07:59:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 07:59:01] local.INFO: Cron job executed {"time":"2025-07-03 07:59:01"} 
[2025-07-03 07:59:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-03 07:59:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-03 07:59:02] local.INFO: Cron job executed {"time":"2025-07-03 07:59:02"} 
