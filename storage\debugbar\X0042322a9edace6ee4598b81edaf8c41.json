{"__meta": {"id": "X0042322a9edace6ee4598b81edaf8c41", "datetime": "2025-07-03 00:26:01", "utime": 1751502361.275857, "method": "POST", "uri": "/api/checkout/webhook", "ip": "127.0.0.1"}, "php": {"version": "8.4.6", "interface": "cgi-fcgi"}, "messages": {"count": 1, "messages": [{"message": "[00:26:01] LOG.error: SQLSTATE[23000]: Integrity constraint violation: 1048 Column 'order_id' cannot be null (Connection: mysql, SQL: insert into `payment_logs` (`order_id`, `stripe_event_id`, `event_type`, `event_data_id`, `status`, `webhook_data`, `updated_at`, `created_at`) values (?, evt_1RgayRRhkfMMoe7tBC2oiuAU, balance.available, ?, ?, {\"id\":\"evt_1RgayRRhkfMMoe7tBC2oiuAU\",\"object\":\"event\",\"api_version\":\"2025-03-31.basil\",\"created\":1751502359,\"data\":{\"object\":{\"object\":\"balance\",\"available\":[{\"amount\":26186926,\"currency\":\"usd\",\"source_types\":{\"card\":26186926}},{\"amount\":29267526,\"currency\":\"eur\",\"source_types\":{\"card\":29267526}}],\"livemode\":false,\"pending\":[{\"amount\":0,\"currency\":\"usd\",\"source_types\":{\"card\":0}},{\"amount\":377214,\"currency\":\"eur\",\"source_types\":{\"card\":377214}}],\"refund_and_dispute_prefunding\":{\"available\":[{\"amount\":0,\"currency\":\"usd\"},{\"amount\":0,\"currency\":\"eur\"}],\"pending\":[{\"amount\":0,\"currency\":\"usd\"},{\"amount\":0,\"currency\":\"eur\"}]}}},\"livemode\":false,\"pending_webhooks\":2,\"request\":{\"id\":null,\"idempotency_key\":null},\"type\":\"balance.available\"}, 2025-07-03 00:26:01, 2025-07-03 00:26:01)) {\n    \"exception\": {\n        \"errorInfo\": [\n            \"23000\",\n            1048,\n            \"Column 'order_id' cannot be null\"\n        ],\n        \"connectionName\": \"mysql\"\n    }\n}", "message_html": null, "is_string": false, "label": "error", "time": 1751502361.271857, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1751502361.11167, "end": 1751502361.275878, "duration": 0.16420793533325195, "duration_str": "164ms", "measures": [{"label": "Booting", "start": 1751502361.11167, "relative_start": 0, "end": 1751502361.241763, "relative_end": 1751502361.241763, "duration": 0.13009309768676758, "duration_str": "130ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1751502361.241776, "relative_start": 0.13010597229003906, "end": 1751502361.27588, "relative_end": 2.1457672119140625e-06, "duration": 0.034104108810424805, "duration_str": "34.1ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 7026752, "peak_usage_str": "7MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST api/checkout/webhook", "middleware": "api", "controller": "App\\Http\\Controllers\\Api\\V1\\StripeWebHookController@handleCheckoutWebhook", "namespace": null, "prefix": "api", "where": [], "excluded_middleware": ["Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken"], "as": "checkout.webhook", "file": "<a href=\"phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FHttp%2FControllers%2FApi%2FV1%2FStripeWebHookController.php&line=20\" onclick=\"\">app/Http/Controllers/Api/V1/StripeWebHookController.php:20-57</a>"}, "queries": {"nb_statements": 0, "nb_visible_statements": 0, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": [], "request": {"telescope": "<a href=\"http://ticketgol.test/_debugbar/telescope/9f4c579b-521d-4bdd-ae6d-b4db14928724\" target=\"_blank\">View in Telescope</a>", "path_info": "/api/checkout/webhook", "status_code": "<pre class=sf-dump id=sf-dump-270506510 data-indent-pad=\"  \"><span class=sf-dump-num>500</span>\n</pre><script>Sfdump(\"sf-dump-270506510\", {\"maxDepth\":0})</script>\n", "status_text": "Internal Server Error", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-593716779 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-593716779\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-557422433 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"28 characters\">evt_1RgayRRhkfMMoe7tBC2oiuAU</span>\"\n  \"<span class=sf-dump-key>object</span>\" => \"<span class=sf-dump-str title=\"5 characters\">event</span>\"\n  \"<span class=sf-dump-key>api_version</span>\" => \"<span class=sf-dump-str title=\"16 characters\">2025-03-31.basil</span>\"\n  \"<span class=sf-dump-key>created</span>\" => <span class=sf-dump-num>1751502359</span>\n  \"<span class=sf-dump-key>data</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>object</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>object</span>\" => \"<span class=sf-dump-str title=\"7 characters\">balance</span>\"\n      \"<span class=sf-dump-key>available</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>amount</span>\" => <span class=sf-dump-num>26186926</span>\n          \"<span class=sf-dump-key>currency</span>\" => \"<span class=sf-dump-str title=\"3 characters\">usd</span>\"\n          \"<span class=sf-dump-key>source_types</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>card</span>\" => <span class=sf-dump-num>26186926</span>\n          </samp>]\n        </samp>]\n        <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>amount</span>\" => <span class=sf-dump-num>29267526</span>\n          \"<span class=sf-dump-key>currency</span>\" => \"<span class=sf-dump-str title=\"3 characters\">eur</span>\"\n          \"<span class=sf-dump-key>source_types</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>card</span>\" => <span class=sf-dump-num>29267526</span>\n          </samp>]\n        </samp>]\n      </samp>]\n      \"<span class=sf-dump-key>livemode</span>\" => <span class=sf-dump-const>false</span>\n      \"<span class=sf-dump-key>pending</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>amount</span>\" => <span class=sf-dump-num>0</span>\n          \"<span class=sf-dump-key>currency</span>\" => \"<span class=sf-dump-str title=\"3 characters\">usd</span>\"\n          \"<span class=sf-dump-key>source_types</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>card</span>\" => <span class=sf-dump-num>0</span>\n          </samp>]\n        </samp>]\n        <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>amount</span>\" => <span class=sf-dump-num>377214</span>\n          \"<span class=sf-dump-key>currency</span>\" => \"<span class=sf-dump-str title=\"3 characters\">eur</span>\"\n          \"<span class=sf-dump-key>source_types</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>card</span>\" => <span class=sf-dump-num>377214</span>\n          </samp>]\n        </samp>]\n      </samp>]\n      \"<span class=sf-dump-key>refund_and_dispute_prefunding</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>available</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>amount</span>\" => <span class=sf-dump-num>0</span>\n            \"<span class=sf-dump-key>currency</span>\" => \"<span class=sf-dump-str title=\"3 characters\">usd</span>\"\n          </samp>]\n          <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>amount</span>\" => <span class=sf-dump-num>0</span>\n            \"<span class=sf-dump-key>currency</span>\" => \"<span class=sf-dump-str title=\"3 characters\">eur</span>\"\n          </samp>]\n        </samp>]\n        \"<span class=sf-dump-key>pending</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>amount</span>\" => <span class=sf-dump-num>0</span>\n            \"<span class=sf-dump-key>currency</span>\" => \"<span class=sf-dump-str title=\"3 characters\">usd</span>\"\n          </samp>]\n          <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>amount</span>\" => <span class=sf-dump-num>0</span>\n            \"<span class=sf-dump-key>currency</span>\" => \"<span class=sf-dump-str title=\"3 characters\">eur</span>\"\n          </samp>]\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>livemode</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>pending_webhooks</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>request</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>idempotency_key</span>\" => <span class=sf-dump-const>null</span>\n  </samp>]\n  \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"17 characters\">balance.available</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-557422433\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-495686508 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">gzip</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>stripe-signature</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"148 characters\">t=1751502359,v1=2d93f2eaca9531e8b565079d77409eb8132fff29ef6346dcfca620bc7acf703d,v0=1f7ad39de05a9f7213b71513da5c43ea967fb4bc23e6bbc06c485a9702a3bd9f</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">application/json; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">*/*; q=0.5, application/xml</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">1423</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">Stripe/1.0 (+https://stripe.com/docs/webhooks)</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">ticketgol.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-495686508\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-594733101 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-594733101\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-463605529 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 03 Jul 2025 00:26:01 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-463605529\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-196637686 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-196637686\", {\"maxDepth\":0})</script>\n"}}