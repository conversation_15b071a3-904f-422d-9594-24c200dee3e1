{"__meta": {"id": "Xfbe40cde002b2fc41246df75fb9310ff", "datetime": "2025-07-03 00:14:30", "utime": 1751501670.618007, "method": "POST", "uri": "/api/checkout/webhook", "ip": "127.0.0.1"}, "php": {"version": "8.4.6", "interface": "cgi-fcgi"}, "messages": {"count": 1, "messages": [{"message": "[00:14:30] LOG.error: SQLSTATE[23000]: Integrity constraint violation: 1048 Column 'order_id' cannot be null (Connection: mysql, SQL: insert into `payment_logs` (`order_id`, `stripe_event_id`, `event_type`, `event_data_id`, `status`, `webhook_data`, `updated_at`, `created_at`) values (?, evt_1RganIRhkfMMoe7tqOWdJv01, balance.available, ?, ?, {\"id\":\"evt_1RganIRhkfMMoe7tqOWdJv01\",\"object\":\"event\",\"api_version\":\"2025-03-31.basil\",\"created\":1751501668,\"data\":{\"object\":{\"object\":\"balance\",\"available\":[{\"amount\":26186926,\"currency\":\"usd\",\"source_types\":{\"card\":26186926}},{\"amount\":29267526,\"currency\":\"eur\",\"source_types\":{\"card\":29267526}}],\"livemode\":false,\"pending\":[{\"amount\":0,\"currency\":\"usd\",\"source_types\":{\"card\":0}},{\"amount\":377214,\"currency\":\"eur\",\"source_types\":{\"card\":377214}}],\"refund_and_dispute_prefunding\":{\"available\":[{\"amount\":0,\"currency\":\"usd\"},{\"amount\":0,\"currency\":\"eur\"}],\"pending\":[{\"amount\":0,\"currency\":\"usd\"},{\"amount\":0,\"currency\":\"eur\"}]}}},\"livemode\":false,\"pending_webhooks\":2,\"request\":{\"id\":null,\"idempotency_key\":null},\"type\":\"balance.available\"}, 2025-07-03 00:14:30, 2025-07-03 00:14:30)) {\n    \"exception\": {\n        \"errorInfo\": [\n            \"23000\",\n            1048,\n            \"Column 'order_id' cannot be null\"\n        ],\n        \"connectionName\": \"mysql\"\n    }\n}", "message_html": null, "is_string": false, "label": "error", "time": 1751501670.615713, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1751501670.396164, "end": 1751501670.618026, "duration": 0.2218620777130127, "duration_str": "222ms", "measures": [{"label": "Booting", "start": 1751501670.396164, "relative_start": 0, "end": 1751501670.510678, "relative_end": 1751501670.510678, "duration": 0.11451411247253418, "duration_str": "115ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1751501670.510692, "relative_start": 0.11452794075012207, "end": 1751501670.618028, "relative_end": 1.9073486328125e-06, "duration": 0.10733604431152344, "duration_str": "107ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 7027888, "peak_usage_str": "7MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST api/checkout/webhook", "middleware": "api", "controller": "App\\Http\\Controllers\\Api\\V1\\StripeWebHookController@handleCheckoutWebhook", "namespace": null, "prefix": "api", "where": [], "excluded_middleware": ["Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken"], "as": "checkout.webhook", "file": "<a href=\"phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FHttp%2FControllers%2FApi%2FV1%2FStripeWebHookController.php&line=20\" onclick=\"\">app/Http/Controllers/Api/V1/StripeWebHookController.php:20-57</a>"}, "queries": {"nb_statements": 0, "nb_visible_statements": 0, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": [], "request": {"telescope": "<a href=\"http://ticketgol.test/_debugbar/telescope/9f4c537d-75f8-47f7-8a57-8b099ffba274\" target=\"_blank\">View in Telescope</a>", "path_info": "/api/checkout/webhook", "status_code": "<pre class=sf-dump id=sf-dump-1809286694 data-indent-pad=\"  \"><span class=sf-dump-num>500</span>\n</pre><script>Sfdump(\"sf-dump-1809286694\", {\"maxDepth\":0})</script>\n", "status_text": "Internal Server Error", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-299865966 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-299865966\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-773909387 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"28 characters\">evt_1RganIRhkfMMoe7tqOWdJv01</span>\"\n  \"<span class=sf-dump-key>object</span>\" => \"<span class=sf-dump-str title=\"5 characters\">event</span>\"\n  \"<span class=sf-dump-key>api_version</span>\" => \"<span class=sf-dump-str title=\"16 characters\">2025-03-31.basil</span>\"\n  \"<span class=sf-dump-key>created</span>\" => <span class=sf-dump-num>1751501668</span>\n  \"<span class=sf-dump-key>data</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>object</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>object</span>\" => \"<span class=sf-dump-str title=\"7 characters\">balance</span>\"\n      \"<span class=sf-dump-key>available</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>amount</span>\" => <span class=sf-dump-num>26186926</span>\n          \"<span class=sf-dump-key>currency</span>\" => \"<span class=sf-dump-str title=\"3 characters\">usd</span>\"\n          \"<span class=sf-dump-key>source_types</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>card</span>\" => <span class=sf-dump-num>26186926</span>\n          </samp>]\n        </samp>]\n        <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>amount</span>\" => <span class=sf-dump-num>29267526</span>\n          \"<span class=sf-dump-key>currency</span>\" => \"<span class=sf-dump-str title=\"3 characters\">eur</span>\"\n          \"<span class=sf-dump-key>source_types</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>card</span>\" => <span class=sf-dump-num>29267526</span>\n          </samp>]\n        </samp>]\n      </samp>]\n      \"<span class=sf-dump-key>livemode</span>\" => <span class=sf-dump-const>false</span>\n      \"<span class=sf-dump-key>pending</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>amount</span>\" => <span class=sf-dump-num>0</span>\n          \"<span class=sf-dump-key>currency</span>\" => \"<span class=sf-dump-str title=\"3 characters\">usd</span>\"\n          \"<span class=sf-dump-key>source_types</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>card</span>\" => <span class=sf-dump-num>0</span>\n          </samp>]\n        </samp>]\n        <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>amount</span>\" => <span class=sf-dump-num>377214</span>\n          \"<span class=sf-dump-key>currency</span>\" => \"<span class=sf-dump-str title=\"3 characters\">eur</span>\"\n          \"<span class=sf-dump-key>source_types</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>card</span>\" => <span class=sf-dump-num>377214</span>\n          </samp>]\n        </samp>]\n      </samp>]\n      \"<span class=sf-dump-key>refund_and_dispute_prefunding</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>available</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>amount</span>\" => <span class=sf-dump-num>0</span>\n            \"<span class=sf-dump-key>currency</span>\" => \"<span class=sf-dump-str title=\"3 characters\">usd</span>\"\n          </samp>]\n          <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>amount</span>\" => <span class=sf-dump-num>0</span>\n            \"<span class=sf-dump-key>currency</span>\" => \"<span class=sf-dump-str title=\"3 characters\">eur</span>\"\n          </samp>]\n        </samp>]\n        \"<span class=sf-dump-key>pending</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>amount</span>\" => <span class=sf-dump-num>0</span>\n            \"<span class=sf-dump-key>currency</span>\" => \"<span class=sf-dump-str title=\"3 characters\">usd</span>\"\n          </samp>]\n          <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>amount</span>\" => <span class=sf-dump-num>0</span>\n            \"<span class=sf-dump-key>currency</span>\" => \"<span class=sf-dump-str title=\"3 characters\">eur</span>\"\n          </samp>]\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>livemode</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>pending_webhooks</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>request</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>idempotency_key</span>\" => <span class=sf-dump-const>null</span>\n  </samp>]\n  \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"17 characters\">balance.available</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-773909387\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1627506388 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">gzip</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>stripe-signature</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"148 characters\">t=1751501669,v1=8d4d9197cfe130cf8885c88e722f7d961c6b713042a56a30607097409254ee66,v0=8eff37f18e4c8053d17fef705f146a96c1d89497d45903f91d8530213f7900ad</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">application/json; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">*/*; q=0.5, application/xml</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">1423</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">Stripe/1.0 (+https://stripe.com/docs/webhooks)</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">ticketgol.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1627506388\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1293398932 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1293398932\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1821452649 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 03 Jul 2025 00:14:30 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1821452649\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-891805038 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-891805038\", {\"maxDepth\":0})</script>\n"}}