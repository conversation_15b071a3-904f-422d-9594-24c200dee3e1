import React, { forwardRef } from "react";
import { Link } from "@inertiajs/react";
import useTranslations from "@/hooks/useTranslations";
import { formatDate } from "@/helpers/formatUtils";
import { Calendar, Ticket, Eye, Download, MapPin } from "lucide-react";
import { formatTime, formatCurrency } from "@/helpers/formatUtils";

const OrderCard = forwardRef(({ order }, ref) => {
    const { translate } = useTranslations();

    const getStatusBadgeClass = (color) => {
        switch (color) {
            case "success":
                return "bg-green-100 text-green-800 border border-green-200";
            case "warning":
                return "bg-yellow-100 text-yellow-800 border border-yellow-200";
            case "danger":
                return "bg-red-100 text-red-800 border border-red-200";
            case "info":
                return "bg-blue-100 text-blue-800 border border-blue-200";
            default:
                return "bg-gray-100 text-gray-800 border border-gray-200";
        }
    };

    return (
        <div
            ref={ref}
            className="overflow-hidden bg-white rounded-lg border border-gray-200 shadow-md transition-all duration-300 hover:shadow-lg"
        >
            {/* Simplified Header with Order & Status */}
            <div className="p-4 bg-gradient-to-r from-blue-50 to-indigo-50 border-b border-gray-100">
                <div className="flex justify-between items-center">
                    <div className="flex gap-3 items-center">
                        <h2 className="text-lg font-bold text-gray-900">
                            {translate("order.order_number", "Order")} #
                            {order.order_no}
                        </h2>
                        <span
                            className={`px-2 py-1 rounded-full text-xs font-semibold ${getStatusBadgeClass(order.status.color)}`}
                        >
                            {order.status.label}
                        </span>
                    </div>
                    <div className="text-right">
                        <div className="text-xl font-bold text-gray-900">
                            {formatCurrency(order.total_price, order.currency)}
                        </div>
                        <div className="text-xs text-gray-500">
                            {order.quantity}{" "}
                            {order.quantity === 1
                                ? translate("ticket.ticket_text", "ticket")
                                : translate("ticket.tickets_text", "tickets")}
                        </div>
                    </div>
                </div>
            </div>

            {/* Content Area */}
            <div className="p-4">
                <div className="flex gap-4">
                    {/* Event Image - Fixed */}
                    <div className="flex-shrink-0 w-32 h-24">
                        <div className="relative group">
                            <figure className="overflow-hidden relative w-full h-32 bg-gradient-to-br from-blue-100 to-indigo-200 rounded-lg">
                                {order.ticket.event?.image ? (
                                    <img
                                        src={order.ticket.event.image}
                                        alt={
                                            order.ticket.event?.image_alt ||
                                            order.ticket.event?.name ||
                                            translate(
                                                "events.event_image",
                                                "Event Image",
                                            )
                                        }
                                        className="object-cover w-full h-full transition-transform duration-300 group-hover:scale-105"
                                    />
                                ) : (
                                    <div className="flex justify-center items-center h-full">
                                        <img
                                            className="w-48 opacity-60"
                                            src="/img/ticketgol-logo.png"
                                            alt={
                                                order.ticket.event?.name ||
                                                translate(
                                                    "events.event_image",
                                                    "Event Image",
                                                )
                                            }
                                        />
                                    </div>
                                )}
                            </figure>
                        </div>
                    </div>

                    {/* Event & Order Details */}
                    <div className="flex-1 space-y-3">
                        {/* Ticket Information - Highlighted */}
                        <div className="p-3 mb-3 bg-blue-50 rounded-lg border border-blue-200">
                            <div className="flex gap-3 items-center">
                                <Ticket className="w-5 h-5 text-blue-600" />
                                <div className="flex gap-4 items-center">
                                    <div>
                                        <span className="text-xs font-medium tracking-wide text-blue-600 uppercase">
                                            {translate(
                                                "common.ticket_number",
                                                "Ticket No",
                                            )}
                                        </span>
                                        <div className="font-mono font-bold text-blue-900">
                                            #{order.ticket.ticket_no}
                                        </div>
                                    </div>
                                    <div className="pl-4 border-l border-blue-300">
                                        <span className="text-xs font-medium tracking-wide text-blue-600 uppercase">
                                            {translate("common.type", "Type")}
                                        </span>
                                        <div className="font-semibold text-blue-900">
                                            {order.ticket.type ||
                                                translate(
                                                    "common.e_ticket",
                                                    "E-Ticket",
                                                )}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        {/* Event Information */}
                        <div>
                            <h3 className="mb-2 text-lg font-semibold text-gray-900">
                                {order.ticket.event?.name ||
                                    translate("common.not_available", "N/A")}
                            </h3>

                            <div className="grid grid-cols-1 gap-3 text-sm md:grid-cols-2">
                                {order.ticket.event?.date && (
                                    <div className="flex gap-2 items-center">
                                        <Calendar className="w-4 h-4 text-gray-500" />
                                        <span className="text-gray-600">
                                            {formatDate(
                                                order.ticket.event.date,
                                            )}
                                            {order.ticket.event?.time && (
                                                <span className="ml-2">
                                                    at{" "}
                                                    {formatTime(
                                                        order.ticket.event.time,
                                                    )}
                                                </span>
                                            )}
                                        </span>
                                    </div>
                                )}

                                {order.ticket.event?.stadium && (
                                    <div className="flex gap-2 items-center">
                                        <MapPin className="w-4 h-4 text-gray-500" />
                                        <span className="text-gray-600">
                                            {order.ticket.event.stadium.name}
                                        </span>
                                    </div>
                                )}
                            </div>

                            {/* Teams Information */}
                            {(order.ticket.event?.home_club ||
                                order.ticket.event?.guest_club) && (
                                <div className="pt-3 mt-3 border-t border-gray-200">
                                    <div className="flex gap-4 justify-center items-center">
                                        {order.ticket.event?.home_club && (
                                            <div className="text-center">
                                                <div className="mb-1 text-xs text-gray-600">
                                                    {translate(
                                                        "common.home",
                                                        "Home",
                                                    )}
                                                </div>
                                                <div className="font-semibold text-blue-600">
                                                    {
                                                        order.ticket.event
                                                            .home_club
                                                    }
                                                </div>
                                            </div>
                                        )}
                                        {order.ticket.event?.home_club &&
                                            order.ticket.event?.guest_club && (
                                                <div className="text-xl font-bold text-gray-400">
                                                    VS
                                                </div>
                                            )}
                                        {order.ticket.event?.guest_club && (
                                            <div className="text-center">
                                                <div className="mb-1 text-xs text-gray-600">
                                                    {translate(
                                                        "common.guest",
                                                        "Guest",
                                                    )}
                                                </div>
                                                <div className="font-semibold text-red-600">
                                                    {
                                                        order.ticket.event
                                                            .guest_club
                                                    }
                                                </div>
                                            </div>
                                        )}
                                    </div>
                                </div>
                            )}
                        </div>

                        {/* Order Date - Show only one date */}
                        <div className="flex gap-2 items-center text-sm text-gray-600">
                            <Calendar className="w-4 h-4" />
                            <span>
                                {order.purchase_date ? (
                                    <>
                                        {translate(
                                            "order.purchased",
                                            "Purchased",
                                        )}
                                        : {formatDate(order.purchase_date)}
                                    </>
                                ) : (
                                    <>
                                        {translate(
                                            "order.created_at",
                                            "Created",
                                        )}
                                        : {formatDate(order.created_at)}
                                    </>
                                )}
                            </span>
                        </div>
                    </div>
                </div>

                {/* Compact Footer with Actions */}
                <div className="flex justify-end items-center pt-3 mt-3 border-t border-gray-200">
                    <div className="flex gap-3 items-center">
                        <Link
                            href={route("my-account.order-detail", {
                                id: order.id,
                            })}
                            className="inline-flex gap-2 items-center px-3 py-2 text-sm font-medium text-white bg-blue-600 rounded-md transition-colors duration-200 hover:bg-blue-700"
                        >
                            <Eye className="w-4 h-4" />
                            {translate("order.view_details", "View Details")}
                        </Link>

                        {order.status.color === "success" && (
                            <button className="inline-flex gap-2 items-center px-3 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md transition-colors duration-200 hover:bg-gray-200">
                                <Download className="w-4 h-4" />
                                {translate(
                                    "order.download_ticket",
                                    "Download Ticket",
                                )}
                            </button>
                        )}
                    </div>
                </div>
            </div>
        </div>
    );
});

OrderCard.displayName = "OrderCard";

export default OrderCard;
