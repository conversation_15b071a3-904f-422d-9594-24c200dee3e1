{"__meta": {"id": "Xda2214a327be58db51d11cb4933a1c8f", "datetime": "2025-07-02 19:07:30", "utime": **********.873218, "method": "GET", "uri": "/api/v1/home", "ip": "127.0.0.1"}, "php": {"version": "8.4.6", "interface": "cgi-fcgi"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.69145, "end": **********.873235, "duration": 0.18178486824035645, "duration_str": "182ms", "measures": [{"label": "Booting", "start": **********.69145, "relative_start": 0, "end": **********.773005, "relative_end": **********.773005, "duration": 0.08155488967895508, "duration_str": "81.55ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.773024, "relative_start": 0.0815739631652832, "end": **********.873238, "relative_end": 3.0994415283203125e-06, "duration": 0.10021400451660156, "duration_str": "100ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 6773712, "peak_usage_str": "6MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET api/v1/home", "middleware": "api, set-locale", "controller": "App\\Http\\Controllers\\Api\\V1\\HomeController@index", "namespace": null, "prefix": "api/v1/home", "where": [], "as": "api.home.index", "file": "<a href=\"phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FHttp%2FControllers%2FApi%2FV1%2FHomeController.php&line=24\" onclick=\"\">app/Http/Controllers/Api/V1/HomeController.php:24-40</a>"}, "queries": {"nb_statements": 18, "nb_visible_statements": 18, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.03443, "accumulated_duration_str": "34.43ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `sessions` where `id` = 'CFoDwus13aj6e2xLfOgPLTvuRWf27pkZlMJ6n5jN' limit 1", "type": "query", "params": [], "bindings": ["CFoDwus13aj6e2xLfOgPLTvuRWf27pkZlMJ6n5jN"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 147}], "start": **********.78062, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:97", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=97", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "97"}, "connection": "ticketgol", "explain": null, "start_percent": 0, "width_percent": 2.469}, {"sql": "select count(*) as aggregate from (select `events`.`id`, `date`, `events`.`stadium_id`, `events`.`league_id`, `et`.`name` as `event_name`, `st`.`name` as `stadium_name`, `lt`.`name` as `league_name`, (select MIN(price) from `tickets` where `event_id` = `events`.`id` and `tickets`.`deleted_at` is null) as `min_price` from `events` left join `event_translations` as `et` on `events`.`id` = `et`.`event_id` and `et`.`locale` = 'en' left join `stadium_translations` as `st` on `events`.`stadium_id` = `st`.`stadium_id` and `st`.`locale` = 'en' left join `league_translations` as `lt` on `events`.`league_id` = `lt`.`league_id` and `lt`.`locale` = 'en' left join `club_translations` as `hc` on `events`.`home_club_id` = `hc`.`club_id` and `hc`.`locale` = 'en' left join `club_translations` as `gc` on `events`.`guest_club_id` = `gc`.`club_id` and `gc`.`locale` = 'en' where `is_published` = 1 and `date` >= '2025-07-02' and `is_feature_event` = 0 and `events`.`deleted_at` is null having `min_price` is not null) as `aggregate_table`", "type": "query", "params": [], "bindings": ["en", "en", "en", "en", "en", 1, "2025-07-02", 0], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/HomeService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\HomeService.php", "line": 35}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/V1/HomeController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\HomeController.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 19, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}, {"index": 20, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingRoutingDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingRoutingDispatcher.php", "line": 35}], "start": **********.789207, "duration": 0.01112, "duration_str": "11.12ms", "memory": 0, "memory_str": null, "filename": "HomeService.php:35", "source": {"index": 16, "namespace": null, "name": "app/Services/HomeService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\HomeService.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FServices%2FHomeService.php&line=35", "ajax": false, "filename": "HomeService.php", "line": "35"}, "connection": "ticketgol", "explain": null, "start_percent": 2.469, "width_percent": 32.297}, {"sql": "select `events`.`id`, `date`, `events`.`stadium_id`, `events`.`league_id`, `et`.`name` as `event_name`, `st`.`name` as `stadium_name`, `lt`.`name` as `league_name`, (select MIN(price) from `tickets` where `event_id` = `events`.`id` and `tickets`.`deleted_at` is null) as `min_price` from `events` left join `event_translations` as `et` on `events`.`id` = `et`.`event_id` and `et`.`locale` = 'en' left join `stadium_translations` as `st` on `events`.`stadium_id` = `st`.`stadium_id` and `st`.`locale` = 'en' left join `league_translations` as `lt` on `events`.`league_id` = `lt`.`league_id` and `lt`.`locale` = 'en' left join `club_translations` as `hc` on `events`.`home_club_id` = `hc`.`club_id` and `hc`.`locale` = 'en' left join `club_translations` as `gc` on `events`.`guest_club_id` = `gc`.`club_id` and `gc`.`locale` = 'en' where `is_published` = 1 and `date` >= '2025-07-02' and `is_feature_event` = 0 and `events`.`deleted_at` is null having `min_price` is not null order by `date` asc limit 8 offset 0", "type": "query", "params": [], "bindings": ["en", "en", "en", "en", "en", 1, "2025-07-02", 0], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/HomeService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\HomeService.php", "line": 35}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/V1/HomeController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\HomeController.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 19, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}, {"index": 20, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingRoutingDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingRoutingDispatcher.php", "line": 35}], "start": **********.8023899, "duration": 0.00114, "duration_str": "1.14ms", "memory": 0, "memory_str": null, "filename": "HomeService.php:35", "source": {"index": 16, "namespace": null, "name": "app/Services/HomeService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\HomeService.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FServices%2FHomeService.php&line=35", "ajax": false, "filename": "HomeService.php", "line": "35"}, "connection": "ticketgol", "explain": null, "start_percent": 34.766, "width_percent": 3.311}, {"sql": "select * from `media` where `media`.`model_id` in (4, 5, 6, 7, 9) and `media`.`model_type` = 'App\\\\Models\\\\Event'", "type": "query", "params": [], "bindings": ["App\\Models\\Event"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Services/HomeService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\HomeService.php", "line": 35}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Api/V1/HomeController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\HomeController.php", "line": 27}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 24, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}, {"index": 25, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingRoutingDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingRoutingDispatcher.php", "line": 35}], "start": **********.807413, "duration": 0.0024100000000000002, "duration_str": "2.41ms", "memory": 0, "memory_str": null, "filename": "HomeService.php:35", "source": {"index": 21, "namespace": null, "name": "app/Services/HomeService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\HomeService.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FServices%2FHomeService.php&line=35", "ajax": false, "filename": "HomeService.php", "line": "35"}, "connection": "ticketgol", "explain": null, "start_percent": 38.077, "width_percent": 7}, {"sql": "select `id` from `stadiums` where `stadiums`.`id` in (1, 5, 6) and `stadiums`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Services/HomeService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\HomeService.php", "line": 35}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Api/V1/HomeController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\HomeController.php", "line": 27}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 24, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}, {"index": 25, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingRoutingDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingRoutingDispatcher.php", "line": 35}], "start": **********.814142, "duration": 0.0016899999999999999, "duration_str": "1.69ms", "memory": 0, "memory_str": null, "filename": "HomeService.php:35", "source": {"index": 21, "namespace": null, "name": "app/Services/HomeService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\HomeService.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FServices%2FHomeService.php&line=35", "ajax": false, "filename": "HomeService.php", "line": "35"}, "connection": "ticketgol", "explain": null, "start_percent": 45.077, "width_percent": 4.909}, {"sql": "select `sluggable_id`, `slug`, `locale` from `slugs` where `locale` = 'en' and `slugs`.`sluggable_id` in (1, 5, 6) and `slugs`.`sluggable_type` = 'App\\\\Models\\\\Stadium'", "type": "query", "params": [], "bindings": ["en", "App\\Models\\Stadium"], "hints": null, "show_copy": true, "backtrace": [{"index": 26, "namespace": null, "name": "app/Services/HomeService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\HomeService.php", "line": 35}, {"index": 27, "namespace": null, "name": "app/Http/Controllers/Api/V1/HomeController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\HomeController.php", "line": 27}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 29, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}, {"index": 30, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingRoutingDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingRoutingDispatcher.php", "line": 35}], "start": **********.819548, "duration": 0.00158, "duration_str": "1.58ms", "memory": 0, "memory_str": null, "filename": "HomeService.php:35", "source": {"index": 26, "namespace": null, "name": "app/Services/HomeService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\HomeService.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FServices%2FHomeService.php&line=35", "ajax": false, "filename": "HomeService.php", "line": "35"}, "connection": "ticketgol", "explain": null, "start_percent": 49.985, "width_percent": 4.589}, {"sql": "select `id` from `leagues` where `leagues`.`id` in (1, 2, 3, 4, 7) and `leagues`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Services/HomeService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\HomeService.php", "line": 35}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Api/V1/HomeController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\HomeController.php", "line": 27}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 24, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}, {"index": 25, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingRoutingDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingRoutingDispatcher.php", "line": 35}], "start": **********.823458, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "HomeService.php:35", "source": {"index": 21, "namespace": null, "name": "app/Services/HomeService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\HomeService.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FServices%2FHomeService.php&line=35", "ajax": false, "filename": "HomeService.php", "line": "35"}, "connection": "ticketgol", "explain": null, "start_percent": 54.574, "width_percent": 2.178}, {"sql": "select `sluggable_id`, `slug`, `locale` from `slugs` where `locale` = 'en' and `slugs`.`sluggable_id` in (1, 2, 3, 4, 7) and `slugs`.`sluggable_type` = 'App\\\\Models\\\\League'", "type": "query", "params": [], "bindings": ["en", "App\\Models\\League"], "hints": null, "show_copy": true, "backtrace": [{"index": 26, "namespace": null, "name": "app/Services/HomeService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\HomeService.php", "line": 35}, {"index": 27, "namespace": null, "name": "app/Http/Controllers/Api/V1/HomeController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\HomeController.php", "line": 27}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 29, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}, {"index": 30, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingRoutingDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingRoutingDispatcher.php", "line": 35}], "start": **********.826649, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "HomeService.php:35", "source": {"index": 26, "namespace": null, "name": "app/Services/HomeService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\HomeService.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FServices%2FHomeService.php&line=35", "ajax": false, "filename": "HomeService.php", "line": "35"}, "connection": "ticketgol", "explain": null, "start_percent": 56.753, "width_percent": 2.12}, {"sql": "select `sluggable_id`, `slug`, `locale` from `slugs` where `locale` = 'en' and `slugs`.`sluggable_id` in (4, 5, 6, 7, 9) and `slugs`.`sluggable_type` = 'App\\\\Models\\\\Event'", "type": "query", "params": [], "bindings": ["en", "App\\Models\\Event"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Services/HomeService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\HomeService.php", "line": 35}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Api/V1/HomeController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\HomeController.php", "line": 27}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 24, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}, {"index": 25, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingRoutingDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingRoutingDispatcher.php", "line": 35}], "start": **********.829314, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "HomeService.php:35", "source": {"index": 21, "namespace": null, "name": "app/Services/HomeService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\HomeService.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FServices%2FHomeService.php&line=35", "ajax": false, "filename": "HomeService.php", "line": "35"}, "connection": "ticketgol", "explain": null, "start_percent": 58.873, "width_percent": 2.091}, {"sql": "select count(*) as aggregate from (select `events`.`id`, `date`, `events`.`stadium_id`, `events`.`league_id`, `et`.`name` as `event_name`, `st`.`name` as `stadium_name`, `lt`.`name` as `league_name`, (select MIN(price) from `tickets` where `event_id` = `events`.`id` and `tickets`.`deleted_at` is null) as `min_price` from `events` left join `event_translations` as `et` on `events`.`id` = `et`.`event_id` and `et`.`locale` = 'en' left join `stadium_translations` as `st` on `events`.`stadium_id` = `st`.`stadium_id` and `st`.`locale` = 'en' left join `league_translations` as `lt` on `events`.`league_id` = `lt`.`league_id` and `lt`.`locale` = 'en' left join `club_translations` as `hc` on `events`.`home_club_id` = `hc`.`club_id` and `hc`.`locale` = 'en' left join `club_translations` as `gc` on `events`.`guest_club_id` = `gc`.`club_id` and `gc`.`locale` = 'en' where `is_published` = 1 and `date` >= '2025-07-02' and `is_feature_event` = 1 and `events`.`deleted_at` is null having `min_price` is not null) as `aggregate_table`", "type": "query", "params": [], "bindings": ["en", "en", "en", "en", "en", 1, "2025-07-02", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/HomeService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\HomeService.php", "line": 48}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/V1/HomeController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\HomeController.php", "line": 28}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 19, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}, {"index": 20, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingRoutingDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingRoutingDispatcher.php", "line": 35}], "start": **********.833863, "duration": 0.0014399999999999999, "duration_str": "1.44ms", "memory": 0, "memory_str": null, "filename": "HomeService.php:48", "source": {"index": 16, "namespace": null, "name": "app/Services/HomeService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\HomeService.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FServices%2FHomeService.php&line=48", "ajax": false, "filename": "HomeService.php", "line": "48"}, "connection": "ticketgol", "explain": null, "start_percent": 60.964, "width_percent": 4.182}, {"sql": "select `events`.`id`, `date`, `events`.`stadium_id`, `events`.`league_id`, `et`.`name` as `event_name`, `st`.`name` as `stadium_name`, `lt`.`name` as `league_name`, (select MIN(price) from `tickets` where `event_id` = `events`.`id` and `tickets`.`deleted_at` is null) as `min_price` from `events` left join `event_translations` as `et` on `events`.`id` = `et`.`event_id` and `et`.`locale` = 'en' left join `stadium_translations` as `st` on `events`.`stadium_id` = `st`.`stadium_id` and `st`.`locale` = 'en' left join `league_translations` as `lt` on `events`.`league_id` = `lt`.`league_id` and `lt`.`locale` = 'en' left join `club_translations` as `hc` on `events`.`home_club_id` = `hc`.`club_id` and `hc`.`locale` = 'en' left join `club_translations` as `gc` on `events`.`guest_club_id` = `gc`.`club_id` and `gc`.`locale` = 'en' where `is_published` = 1 and `date` >= '2025-07-02' and `is_feature_event` = 1 and `events`.`deleted_at` is null having `min_price` is not null limit 8 offset 0", "type": "query", "params": [], "bindings": ["en", "en", "en", "en", "en", 1, "2025-07-02", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/HomeService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\HomeService.php", "line": 48}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/V1/HomeController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\HomeController.php", "line": 28}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 19, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}, {"index": 20, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingRoutingDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingRoutingDispatcher.php", "line": 35}], "start": **********.837166, "duration": 0.0020499999999999997, "duration_str": "2.05ms", "memory": 0, "memory_str": null, "filename": "HomeService.php:48", "source": {"index": 16, "namespace": null, "name": "app/Services/HomeService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\HomeService.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FServices%2FHomeService.php&line=48", "ajax": false, "filename": "HomeService.php", "line": "48"}, "connection": "ticketgol", "explain": null, "start_percent": 65.147, "width_percent": 5.954}, {"sql": "select * from `media` where `media`.`model_id` in (1, 2, 3) and `media`.`model_type` = 'App\\\\Models\\\\Event'", "type": "query", "params": [], "bindings": ["App\\Models\\Event"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Services/HomeService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\HomeService.php", "line": 48}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Api/V1/HomeController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\HomeController.php", "line": 28}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 24, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}, {"index": 25, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingRoutingDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingRoutingDispatcher.php", "line": 35}], "start": **********.841266, "duration": 0.00088, "duration_str": "880μs", "memory": 0, "memory_str": null, "filename": "HomeService.php:48", "source": {"index": 21, "namespace": null, "name": "app/Services/HomeService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\HomeService.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FServices%2FHomeService.php&line=48", "ajax": false, "filename": "HomeService.php", "line": "48"}, "connection": "ticketgol", "explain": null, "start_percent": 71.101, "width_percent": 2.556}, {"sql": "select `id` from `stadiums` where `stadiums`.`id` in (6, 7, 9) and `stadiums`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Services/HomeService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\HomeService.php", "line": 48}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Api/V1/HomeController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\HomeController.php", "line": 28}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 24, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}, {"index": 25, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingRoutingDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingRoutingDispatcher.php", "line": 35}], "start": **********.8443499, "duration": 0.0009599999999999999, "duration_str": "960μs", "memory": 0, "memory_str": null, "filename": "HomeService.php:48", "source": {"index": 21, "namespace": null, "name": "app/Services/HomeService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\HomeService.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FServices%2FHomeService.php&line=48", "ajax": false, "filename": "HomeService.php", "line": "48"}, "connection": "ticketgol", "explain": null, "start_percent": 73.657, "width_percent": 2.788}, {"sql": "select `sluggable_id`, `slug`, `locale` from `slugs` where `locale` = 'en' and `slugs`.`sluggable_id` in (6, 7, 9) and `slugs`.`sluggable_type` = 'App\\\\Models\\\\Stadium'", "type": "query", "params": [], "bindings": ["en", "App\\Models\\Stadium"], "hints": null, "show_copy": true, "backtrace": [{"index": 26, "namespace": null, "name": "app/Services/HomeService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\HomeService.php", "line": 48}, {"index": 27, "namespace": null, "name": "app/Http/Controllers/Api/V1/HomeController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\HomeController.php", "line": 28}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 29, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}, {"index": 30, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingRoutingDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingRoutingDispatcher.php", "line": 35}], "start": **********.847214, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "HomeService.php:48", "source": {"index": 26, "namespace": null, "name": "app/Services/HomeService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\HomeService.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FServices%2FHomeService.php&line=48", "ajax": false, "filename": "HomeService.php", "line": "48"}, "connection": "ticketgol", "explain": null, "start_percent": 76.445, "width_percent": 2.236}, {"sql": "select `id` from `leagues` where `leagues`.`id` in (3, 7) and `leagues`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Services/HomeService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\HomeService.php", "line": 48}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Api/V1/HomeController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\HomeController.php", "line": 28}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 24, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}, {"index": 25, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingRoutingDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingRoutingDispatcher.php", "line": 35}], "start": **********.8506489, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "HomeService.php:48", "source": {"index": 21, "namespace": null, "name": "app/Services/HomeService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\HomeService.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FServices%2FHomeService.php&line=48", "ajax": false, "filename": "HomeService.php", "line": "48"}, "connection": "ticketgol", "explain": null, "start_percent": 78.681, "width_percent": 1.656}, {"sql": "select `sluggable_id`, `slug`, `locale` from `slugs` where `locale` = 'en' and `slugs`.`sluggable_id` in (3, 7) and `slugs`.`sluggable_type` = 'App\\\\Models\\\\League'", "type": "query", "params": [], "bindings": ["en", "App\\Models\\League"], "hints": null, "show_copy": true, "backtrace": [{"index": 26, "namespace": null, "name": "app/Services/HomeService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\HomeService.php", "line": 48}, {"index": 27, "namespace": null, "name": "app/Http/Controllers/Api/V1/HomeController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\HomeController.php", "line": 28}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 29, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}, {"index": 30, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingRoutingDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingRoutingDispatcher.php", "line": 35}], "start": **********.853277, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "HomeService.php:48", "source": {"index": 26, "namespace": null, "name": "app/Services/HomeService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\HomeService.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FServices%2FHomeService.php&line=48", "ajax": false, "filename": "HomeService.php", "line": "48"}, "connection": "ticketgol", "explain": null, "start_percent": 80.337, "width_percent": 2.149}, {"sql": "select `sluggable_id`, `slug`, `locale` from `slugs` where `locale` = 'en' and `slugs`.`sluggable_id` in (1, 2, 3) and `slugs`.`sluggable_type` = 'App\\\\Models\\\\Event'", "type": "query", "params": [], "bindings": ["en", "App\\Models\\Event"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Services/HomeService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\HomeService.php", "line": 48}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Api/V1/HomeController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\HomeController.php", "line": 28}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 24, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}, {"index": 25, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingRoutingDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingRoutingDispatcher.php", "line": 35}], "start": **********.8557222, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "HomeService.php:48", "source": {"index": 21, "namespace": null, "name": "app/Services/HomeService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\HomeService.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FServices%2FHomeService.php&line=48", "ajax": false, "filename": "HomeService.php", "line": "48"}, "connection": "ticketgol", "explain": null, "start_percent": 82.486, "width_percent": 1.801}, {"sql": "update `sessions` set `payload` = 'YTozOntzOjY6Il90b2tlbiI7czo0MDoidFVWWWE4NzJCUm9BUzlpWGRnczM5alhDSGpwMURXU2l5Ykx5cWFyciI7czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6MjE6Imh0dHA6Ly90aWNrZXRnb2wudGVzdCI7fX0=', `last_activity` = **********, `user_id` = null, `ip_address` = '127.0.0.1', `user_agent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' where `id` = 'CFoDwus13aj6e2xLfOgPLTvuRWf27pkZlMJ6n5jN'", "type": "query", "params": [], "bindings": ["YTozOntzOjY6Il90b2tlbiI7czo0MDoidFVWWWE4NzJCUm9BUzlpWGRnczM5alhDSGpwMURXU2l5Ykx5cWFyciI7czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6MjE6Imh0dHA6Ly90aWNrZXRnb2wudGVzdCI7fX0=", **********, null, "127.0.0.1", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "CFoDwus13aj6e2xLfOgPLTvuRWf27pkZlMJ6n5jN"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 173}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 140}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 175}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 245}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 130}], "start": **********.866772, "duration": 0.00541, "duration_str": "5.41ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:173", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 173}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=173", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "173"}, "connection": "ticketgol", "explain": null, "start_percent": 84.287, "width_percent": 15.713}]}, "models": {"data": {"App\\Models\\Slug": {"value": 21, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FSlug.php&line=1", "ajax": false, "filename": "Slug.php", "line": "?"}}, "App\\Models\\Event": {"value": 8, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FEvent.php&line=1", "ajax": false, "filename": "Event.php", "line": "?"}}, "App\\Models\\League": {"value": 7, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FLeague.php&line=1", "ajax": false, "filename": "League.php", "line": "?"}}, "App\\Models\\Stadium": {"value": 6, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FStadium.php&line=1", "ajax": false, "filename": "Stadium.php", "line": "?"}}, "Spatie\\MediaLibrary\\MediaCollections\\Models\\Media": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Fspatie%2Flaravel-medialibrary%2Fsrc%2FMediaCollections%2FModels%2FMedia.php&line=1", "ajax": false, "filename": "Media.php", "line": "?"}}}, "count": 44, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "tUVYa872BRoAS9iXdgs39jXCHjp1DWSiybLyqarr", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://ticketgol.test\"\n]"}, "request": {"telescope": "<a href=\"http://ticketgol.test/_debugbar/telescope/9f4be5b3-29f1-499b-adea-7e646ed78fc0\" target=\"_blank\">View in Telescope</a>", "path_info": "/api/v1/home", "status_code": "<pre class=sf-dump id=sf-dump-1835953728 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1835953728\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-823921274 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-823921274\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-224331150 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-224331150\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-359849654 data-indent-pad=\"  \"><span class=sf-dump-note>array:11</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"853 characters\">ajs_anonymous_id=%2205cf8d6b-8a14-40b9-9d4f-c4ebd1620a6e%22; selected_locale=en; __stripe_mid=fd60eaa0-e90c-4914-bb27-ce58ed87fc6066be49; XSRF-TOKEN=eyJpdiI6Ik55d1BZUDEwK1dkRkcyRGNtbUE0K2c9PSIsInZhbHVlIjoiQ1MwNVFuelpiYkxCMFRpYVdGeDBZbmhDaWZIaTRBKzV2OGJ2NFp3RC9JTTVxRGZSVlo3UDJiMU1lTnNoYUxmMVZFanRvQmFwaWNRREdsL0ZPL0VMcFFhblVORzVDeHlVVzg2SmpLdSs5VkRta0hYVGI1SEdsU3grYmZCZGZ1NXkiLCJtYWMiOiI3NGUxMmZhMTQyYmU1OThhYTEwZDVlM2RiNjBiZjQzZDBiYjgzZDg3MDMzNGZkNmQ4YzQ2M2VmNjM5OTdlYjMyIiwidGFnIjoiIn0%3D; ticketgol_session=eyJpdiI6InRrNkhDbUFaYk13OVkxT0hSa1FRWVE9PSIsInZhbHVlIjoicFdLbExkQVFtWk5BNHBHWGFkdU94VDQxREFQdkVHSk9tT2pzQldaOU1IaEVnRCtxV0tGQ2Y5T2JSR0dFK0Q2SVduSG1Id1BmTVhUM0FOR0J1QWFYRzJrVEthbndyeGQ3VU5ZaXhGcGorN2ZaZkVHRjNaeGg5Z1VMQ3pvVXVzMjgiLCJtYWMiOiJiOWUxZmZiN2EyNTg0MmExYzVhZDdjMjcyMDM0OTM2N2ZjOTE4NTk1OTg2NDU5Njk4ZjQxZDMyMjkyZDBlMDU1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">en-GB,en;q=0.9,es;q=0.8,en-US;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://ticketgol.test/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-locale</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6Ik55d1BZUDEwK1dkRkcyRGNtbUE0K2c9PSIsInZhbHVlIjoiQ1MwNVFuelpiYkxCMFRpYVdGeDBZbmhDaWZIaTRBKzV2OGJ2NFp3RC9JTTVxRGZSVlo3UDJiMU1lTnNoYUxmMVZFanRvQmFwaWNRREdsL0ZPL0VMcFFhblVORzVDeHlVVzg2SmpLdSs5VkRta0hYVGI1SEdsU3grYmZCZGZ1NXkiLCJtYWMiOiI3NGUxMmZhMTQyYmU1OThhYTEwZDVlM2RiNjBiZjQzZDBiYjgzZDg3MDMzNGZkNmQ4YzQ2M2VmNjM5OTdlYjMyIiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">ticketgol.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-359849654\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1684848177 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>ajs_anonymous_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>selected_locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">tUVYa872BRoAS9iXdgs39jXCHjp1DWSiybLyqarr</span>\"\n  \"<span class=sf-dump-key>ticketgol_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">CFoDwus13aj6e2xLfOgPLTvuRWf27pkZlMJ6n5jN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1684848177\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1482417227 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 02 Jul 2025 19:07:30 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IlAvWFFxME50aGdiOFVmYjI2U2lCNXc9PSIsInZhbHVlIjoiNUhXQ0dqYjJ6WjhBUFR4dzNML1NDbDdGTnRjOHVrNEZZVWlKcTlLRUxWUTRRc3N1allNZG45Tm1NTkxVdXV0OTNtUFY4MTlFWU5vdGI4MXFGb0V6cXA0YlBodndubTIzMzZHQzRITXVORVhCZmpxVW0vNysxL0JVSWdnYmhHQlQiLCJtYWMiOiI4MmY1NTU1YTljYTgwYWE0NWI0NDM5MTQyMzE4NDU2ZTM4OTM0OWZhNDQ4ODYwMjU4NGVkZDY3NzljYTk4NTIxIiwidGFnIjoiIn0%3D; expires=Wed, 02 Jul 2025 21:07:30 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"445 characters\">ticketgol_session=eyJpdiI6InpRWU5tVXNUK2hIUW9UUzRDcWpXcVE9PSIsInZhbHVlIjoiUUFNcnY2OWFDYUV4VmdGV0tFY2M1OHFOaGwzT0lNOWNGOGdNRVJpTkM5R0JRazhMTFpUdnZGNWhrb3oxbitHTnRpVDdQNjVTNUZUN2xZc3pGT1Y3b2EyTE9TVEJNMzlpY3IvKzlqYTBuUU85cDV1U24zZ2ExcHdBWkJMMHlqM0EiLCJtYWMiOiJhNzdiMzMzZTFkZmRlNDlhZTUwMTk0MmE1OGQ5MzYyODhhMTE4NTQ3ZWQxMDE2MjlmNTcyMGFjNzg2ZDZlYTNkIiwidGFnIjoiIn0%3D; expires=Wed, 02 Jul 2025 21:07:30 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IlAvWFFxME50aGdiOFVmYjI2U2lCNXc9PSIsInZhbHVlIjoiNUhXQ0dqYjJ6WjhBUFR4dzNML1NDbDdGTnRjOHVrNEZZVWlKcTlLRUxWUTRRc3N1allNZG45Tm1NTkxVdXV0OTNtUFY4MTlFWU5vdGI4MXFGb0V6cXA0YlBodndubTIzMzZHQzRITXVORVhCZmpxVW0vNysxL0JVSWdnYmhHQlQiLCJtYWMiOiI4MmY1NTU1YTljYTgwYWE0NWI0NDM5MTQyMzE4NDU2ZTM4OTM0OWZhNDQ4ODYwMjU4NGVkZDY3NzljYTk4NTIxIiwidGFnIjoiIn0%3D; expires=Wed, 02-Jul-2025 21:07:30 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"417 characters\">ticketgol_session=eyJpdiI6InpRWU5tVXNUK2hIUW9UUzRDcWpXcVE9PSIsInZhbHVlIjoiUUFNcnY2OWFDYUV4VmdGV0tFY2M1OHFOaGwzT0lNOWNGOGdNRVJpTkM5R0JRazhMTFpUdnZGNWhrb3oxbitHTnRpVDdQNjVTNUZUN2xZc3pGT1Y3b2EyTE9TVEJNMzlpY3IvKzlqYTBuUU85cDV1U24zZ2ExcHdBWkJMMHlqM0EiLCJtYWMiOiJhNzdiMzMzZTFkZmRlNDlhZTUwMTk0MmE1OGQ5MzYyODhhMTE4NTQ3ZWQxMDE2MjlmNTcyMGFjNzg2ZDZlYTNkIiwidGFnIjoiIn0%3D; expires=Wed, 02-Jul-2025 21:07:30 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1482417227\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1508288287 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">tUVYa872BRoAS9iXdgs39jXCHjp1DWSiybLyqarr</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://ticketgol.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1508288287\", {\"maxDepth\":0})</script>\n"}}